#include <stdio.h>
extern int g_network_attached; // Declare the network attachment status flag from cloud_vsim.c
extern int g_reauth_started;   // Declare the re-authentication status flag from cloud_vsim.c
extern int g_cloud_dial_status;
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <curl/curl.h>
#include <errno.h>
#include <time.h>
#if USE_ICONV_CONVERSION
#include <iconv.h>
#endif
#include "cjson.h"
#include "qrzl_utils.h"
#include "ito_cloud_vsim_client.h"
#include "cloud_vsim.h"

// 外部全局变量声明
extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

// 全局变量：种子卡wan接口名称
static char g_seed_wan_name[32] = {0};

// 函数声明
static void stop_main_client_threads(void);
static void stop_tcp_thread_only(void);
static char* convert_utf8_to_gb2312(const char* utf8_str);
static int is_chinese_utf8(unsigned char c1, unsigned char c2, unsigned char c3);
static int is_chinese_utf8_4byte(unsigned char c1, unsigned char c2, unsigned char c3, unsigned char c4);

// 编码转换配置
#define USE_ENHANCED_UTF8_DISPLAY   1   // 0=简单模式，1=增强模式
#define USE_ICONV_CONVERSION        0   // 0=不使用iconv，1=使用iconv

// 错误码定义
#define ITO_CLOUD_SUCCESS           0   // 成功
#define ITO_CLOUD_ERROR_GENERAL    -1   // 一般错误
#define ITO_CLOUD_ERROR_NETWORK    -2   // 网络错误（HTTP状态码非200）
#define ITO_CLOUD_ERROR_AUTH       -3   // 认证错误（SN不合法等，HTTP 200但success=false）
#define ITO_CLOUD_ERROR_PARSE      -4   // 解析错误
#define ITO_CLOUD_ERROR_HTTP       -5   // HTTP请求失败（连接失败、超时等）

/**
 * 初始化种子卡wan接口名称
 */
static void init_seed_wan_name(void) {
    if (strlen(g_seed_wan_name) == 0) {
        if (cfg_get_item("seed_wan_name", g_seed_wan_name, sizeof(g_seed_wan_name)) != 0 || strlen(g_seed_wan_name) == 0) {
            LOG_W(LOG_TAG, "Failed to get 'seed_wan_name' from NV, using default 'wan1'");
            strncpy(g_seed_wan_name, "wan1", sizeof(g_seed_wan_name) - 1);
        }
        LOG_I(LOG_TAG, "初始化种子卡wan接口: %s", g_seed_wan_name);
    }
}

/**
 * 获取种子卡wan接口名称，如果为空则返回默认值
 */
static const char* get_seed_wan_name(void) {
    if (strlen(g_seed_wan_name) == 0) {
        return "wan1";  // 默认值
    }
    return g_seed_wan_name;
}

// API路径宏
#define CONFIG_PATH "/iotHub/api/device/v1/config/findConfig"
#define FLOW_UPLOAD_PATH "/iotHub/api/device/v1/flowUpload"
#define UPLOAD_DEVICE_INFO_PATH "/iotHub/api/device/v1/uploadDeviceInfo"
#define LOGIN_PATH "/iotHub/api/device/v1/login"
#define AUTH_SYNC_PATH "/iotHub/api/device/v1/authSync"
#define PROGRESS_UPLOAD_PATH "/iotHub/api/device/v1/progressUpload"
#define DEVICE_STATE_PATH "/iotHub/api/device/v1/deviceState"
#define ERROR_UPLOAD_PATH "/iotHub/api/device/v1/errorUpload"

#define MAX_MSG_BODY_LEN 1024
#define IDENTIFING_BIT 0x7e

// 云卡信息结构体实例
static CloudCardInfo cloud_card_info = {0};

// HTTP请求参数
#define HTTP_REQUEST_TIMEOUT 30 // 30秒超时
#define HTTP_MAX_RETRIES 2      // 最大重试次数

static pthread_t g_ito_http_thread;
static pthread_t g_ito_tcp_thread;
static pthread_t g_seed_dial_thread;
static int g_running = 0;
static int g_main_threads_running = 0; // 主要客户端线程运行状态
static int g_cloud_vsim_initialized = 0; // 云卡VSIM是否已初始化
static ito_cloud_config_t g_cloud_config;
static int g_tcp_sockfd = -1;
static int g_heartbeat_ok = 0;
static int g_auth_successful = 0;
static int g_vsim_started = 0;
static int g_seed_card_initialized = 0; // 标记种子卡是否已成功初始化和拨号

static uint64_t last_send_total_flow = 0L; // Last reported device flow usage, initialized to 0, obtained from the flow used after the device is online

// Static data initialization flag
static int g_static_data_initialized = 0;

// Global variables for maintaining message sequence number
static unsigned short g_msg_sequence = 0;
static unsigned int g_auth_count = 0; // Authentication counter

// Device control command IDs (according to protocol document section 4.1.4)
typedef enum {
    CMD_SPEED_LIMIT     = 701,
    CMD_POWER_OFF       = 702,
    CMD_REBOOT          = 703,
    CMD_NETWORK_SWITCH  = 704,
    CMD_WIFI_MODIFY     = 705,
    CMD_DEVICE_UPGRADE  = 706,
    CMD_FACTORY_RESET   = 707,
    CMD_LOG_COLLECT     = 708,
    CMD_CLEAR_CACHE     = 710,
    CMD_SET_WHITELIST   = 711
} device_control_cmd_t;

/**
 * Ensure static data is initialized (only once)
 */
static void ensure_static_data_initialized(void) {
    if (!g_static_data_initialized) {
        LOG_D(LOG_TAG, "Initializing device static data...");
        update_device_static_data();
        g_static_data_initialized = 1;
        LOG_D(LOG_TAG, "Device static data initialization complete.");
    }
}

struct MemoryStruct {
  char *memory;
  size_t size;
};

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp)
{
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)userp;

    char *ptr = realloc(mem->memory, mem->size + realsize + 1);
    if(ptr == NULL) {
        LOG_E(LOG_TAG, "not enough memory (realloc returned NULL)");
        return 0;
    }

    mem->memory = ptr;
    memcpy(&(mem->memory[mem->size]), contents, realsize);
    mem->size += realsize;
    mem->memory[mem->size] = 0;

    return realsize;
}

/**
 * URL encode function
 * @param src Source string
 * @param dest Destination buffer
 * @param dest_size Destination buffer size
 */
static void ito_url_encode(const char* src, char* dest, size_t dest_size) {
    if (!src || !dest || dest_size == 0) {
        return;
    }

    const char* hex = "0123456789ABCDEF";
    size_t dest_idx = 0;
    size_t i;

    for (i = 0; src[i] && dest_idx < dest_size - 1; i++) {
        unsigned char c = (unsigned char)src[i];

        // Characters that do not need to be encoded: letters, numbers, -, _, ., ~
        // Keep query parameter separators = and &
        if ((c >= 'A' && c <= 'Z') ||
            (c >= 'a' && c <= 'z') ||
            (c >= '0' && c <= '9') ||
            c == '-' || c == '_' || c == '.' || c == '~' ||
            c == '=' || c == '&') {
            dest[dest_idx++] = c;
        } else {
            // Characters that need to be encoded
            if (dest_idx + 2 < dest_size - 1) {
                dest[dest_idx++] = '%';
                dest[dest_idx++] = hex[c >> 4];
                dest[dest_idx++] = hex[c & 0x0F];
            } else {
                break; // Not enough space
            }
        }
    }

    dest[dest_idx] = '\0';
}

/**
 * Extract version code from version string
 * Input: "MZ804LD1.0_KSBC_COMMON_SL_V2.01.01.02P48U05_04-250703"
 * Output: "04"
 */
const char* get_version_code(const char* version_string) {
    static char version_code[8] = {0};  // Static buffer to store the result

    if (!version_string) {
        strcpy(version_code, "00");
        return version_code;
    }

    // Find the position of the last '_'
    const char* last_underscore = strrchr(version_string, '_');
    if (!last_underscore) {
        strcpy(version_code, "00");
        return version_code;
    }

    // Start searching from after the last '_'
    const char* start = last_underscore + 1;

    // Find the position of '-'
    const char* dash = strchr(start, '-');
    if (!dash) {
        strcpy(version_code, "00");
        return version_code;
    }

    // Calculate the length of the version code
    int len = dash - start;
    if (len <= 0 || len >= sizeof(version_code)) {
        strcpy(version_code, "00");
        return version_code;
    }

    // Copy the version code
    strncpy(version_code, start, len);
    version_code[len] = '\0';

    LOG_D(LOG_TAG, "Extracted version code '%s' from version string '%s'", version_code, version_string);

    return version_code;
}

/**
 * Fill empty fields with default data (based on actual data from vsim.log)
 * @param field_name Field name (for logging)
 * @param current_value Current value
 * @param default_value Default value
 * @return Returns the default value if the current value is empty, otherwise returns the current value
 */
static const char* fill_default_if_empty(const char* field_name, const char* current_value, const char* default_value) {
    if (!current_value || strlen(current_value) == 0) {
        LOG_D(LOG_TAG, "Field '%s' is empty, using default value: %s", field_name, default_value);
        return default_value;
    }
    return current_value;
}

/**
 * Request type enumeration
 */
typedef enum {
    REQUEST_TYPE_FLOW_UPLOAD = 1,
    REQUEST_TYPE_DEVICE_INFO,
    REQUEST_TYPE_LOGIN,
    REQUEST_TYPE_AUTH_SYNC,
    REQUEST_TYPE_PROGRESS_UPLOAD,
    REQUEST_TYPE_DEVICE_STATE,
    REQUEST_TYPE_CONFIG
} request_type_t;

// 函数声明
static void ensure_sim_data_filled(void);
static void ensure_network_data_filled(void);
static void ensure_signal_data_filled(void);
static void ensure_device_identity_filled(void);
static void ensure_wifi_data_filled(void);

/**
 * Ensure key data fields are not empty, fill with default values based on request type and vsim.log
 */
static void ensure_required_data_not_empty(request_type_t request_type) {
    LOG_D(LOG_TAG, "Checking and filling data for request type %d", request_type);

    // Basic data required for all requests

    // SN default value (required for all requests)
    if (!g_qrzl_device_static_data.sn || strlen(g_qrzl_device_static_data.sn) == 0) {
        strncpy(g_qrzl_device_static_data.sn, "DEFAULT_SN_123456", sizeof(g_qrzl_device_static_data.sn) - 1);
        LOG_D(LOG_TAG, "SN is empty, using default value: %s", g_qrzl_device_static_data.sn);
    }

    // IMEI default value (from vsim.log: 862769025435956)
    if (!g_qrzl_device_static_data.imei || strlen(g_qrzl_device_static_data.imei) == 0) {
        strncpy(g_qrzl_device_static_data.imei, "862769025435956", sizeof(g_qrzl_device_static_data.imei) - 1);
        LOG_D(LOG_TAG, "IMEI is empty, using default value: %s", g_qrzl_device_static_data.imei);
    }


    // Fill specific data based on request type
    switch (request_type) {
        case REQUEST_TYPE_FLOW_UPLOAD:
        case REQUEST_TYPE_DEVICE_STATE:
            // Flow upload and device state require network information
            ensure_network_data_filled();
            ensure_signal_data_filled();
            break;

        case REQUEST_TYPE_DEVICE_INFO:
        case REQUEST_TYPE_LOGIN:
            // Device info and login require complete device data
            ensure_device_identity_filled();
            ensure_network_data_filled();
            ensure_wifi_data_filled();
            break;

        case REQUEST_TYPE_AUTH_SYNC:
        case REQUEST_TYPE_PROGRESS_UPLOAD:
            // Authentication and progress upload mainly require SIM card data
            ensure_sim_data_filled();
            break;

        case REQUEST_TYPE_CONFIG:
            // Configuration retrieval requires basic device identification
            ensure_device_identity_filled();
            break;

        default:
            LOG_W(LOG_TAG, "Unknown request type: %d", request_type);
            break;
    }

    LOG_I(LOG_TAG, "Data integrity check complete, all necessary fields are filled.");
}

/**
 * Ensure SIM card related data is not empty
 */
static void ensure_sim_data_filled(void) {
    // ICCID default value (based on common formats)
    if (!g_qrzl_device_dynamic_data.iccid || strlen(g_qrzl_device_dynamic_data.iccid) == 0) {
        strncpy(g_qrzl_device_dynamic_data.iccid, "89860000000000000000", sizeof(g_qrzl_device_dynamic_data.iccid) - 1);
        LOG_D(LOG_TAG, "ICCID is empty, using default value: %s", g_qrzl_device_dynamic_data.iccid);
    }

    // IMSI default value (China Mobile test segment)
    if (!g_qrzl_device_dynamic_data.imsi || strlen(g_qrzl_device_dynamic_data.imsi) == 0) {
        strncpy(g_qrzl_device_dynamic_data.imsi, "***************", sizeof(g_qrzl_device_dynamic_data.imsi) - 1);
        LOG_D(LOG_TAG, "IMSI is empty, using default value: %s", g_qrzl_device_dynamic_data.imsi);
    }
}

/**
 * Ensure network data is not empty
 */
static void ensure_network_data_filled(void) {
    ensure_sim_data_filled(); // Network data depends on SIM data

    // Derive MCC/MNC from IMSI or use default values
    if ((!g_qrzl_device_dynamic_data.mcc || strlen(g_qrzl_device_dynamic_data.mcc) == 0) ||
        (!g_qrzl_device_dynamic_data.mnc || strlen(g_qrzl_device_dynamic_data.mnc) == 0)) {

        // Try to extract MCC/MNC from IMSI
        if (g_qrzl_device_dynamic_data.imsi && strlen(g_qrzl_device_dynamic_data.imsi) >= 6) {
            char mcc_from_imsi[4] = {0};
            char mnc_from_imsi[4] = {0};

            strncpy(mcc_from_imsi, g_qrzl_device_dynamic_data.imsi, 3);
            strncpy(mnc_from_imsi, g_qrzl_device_dynamic_data.imsi + 3, 2);

            if (!g_qrzl_device_dynamic_data.mcc || strlen(g_qrzl_device_dynamic_data.mcc) == 0) {
                strncpy(g_qrzl_device_dynamic_data.mcc, mcc_from_imsi, sizeof(g_qrzl_device_dynamic_data.mcc) - 1);
                LOG_D(LOG_TAG, "Extracted MCC from IMSI: %s", g_qrzl_device_dynamic_data.mcc);
            }

            if (!g_qrzl_device_dynamic_data.mnc || strlen(g_qrzl_device_dynamic_data.mnc) == 0) {
                strncpy(g_qrzl_device_dynamic_data.mnc, mnc_from_imsi, sizeof(g_qrzl_device_dynamic_data.mnc) - 1);
                LOG_D(LOG_TAG, "Extracted MNC from IMSI: %s", g_qrzl_device_dynamic_data.mnc);
            }
        } else {
            // Use default values
            if (!g_qrzl_device_dynamic_data.mcc || strlen(g_qrzl_device_dynamic_data.mcc) == 0) {
                strncpy(g_qrzl_device_dynamic_data.mcc, "460", sizeof(g_qrzl_device_dynamic_data.mcc) - 1);
                LOG_D(LOG_TAG, "MCC is empty, using default value: %s", g_qrzl_device_dynamic_data.mcc);
            }

            if (!g_qrzl_device_dynamic_data.mnc || strlen(g_qrzl_device_dynamic_data.mnc) == 0) {
                strncpy(g_qrzl_device_dynamic_data.mnc, "00", sizeof(g_qrzl_device_dynamic_data.mnc) - 1);
                LOG_D(LOG_TAG, "MNC is empty, using default value: %s", g_qrzl_device_dynamic_data.mnc);
            }
        }
    }

    // LAC default value
    if (!g_qrzl_device_dynamic_data.lac || strlen(g_qrzl_device_dynamic_data.lac) == 0) {
        strncpy(g_qrzl_device_dynamic_data.lac, "1000", sizeof(g_qrzl_device_dynamic_data.lac) - 1);
        LOG_D(LOG_TAG, "LAC is empty, using default value: %s", g_qrzl_device_dynamic_data.lac);
    }

    // CID default value
    if (!g_qrzl_device_dynamic_data.cid || strlen(g_qrzl_device_dynamic_data.cid) == 0) {
        strncpy(g_qrzl_device_dynamic_data.cid, "2000", sizeof(g_qrzl_device_dynamic_data.cid) - 1);
        LOG_D(LOG_TAG, "CID is empty, using default value: %s", g_qrzl_device_dynamic_data.cid);
    }
}

/**
 * Ensure signal data is not empty
 */
static void ensure_signal_data_filled(void) {
    // RSSI default value
    if (!g_qrzl_device_dynamic_data.rssi || strlen(g_qrzl_device_dynamic_data.rssi) == 0) {
        strncpy(g_qrzl_device_dynamic_data.rssi, "-70", sizeof(g_qrzl_device_dynamic_data.rssi) - 1);
        LOG_D(LOG_TAG, "RSSI is empty, using default value: %s", g_qrzl_device_dynamic_data.rssi);
    }

    // Battery power default value
    if (!g_qrzl_device_dynamic_data.remain_power || strlen(g_qrzl_device_dynamic_data.remain_power) == 0) {
        strncpy(g_qrzl_device_dynamic_data.remain_power, "80", sizeof(g_qrzl_device_dynamic_data.remain_power) - 1);
        LOG_D(LOG_TAG, "Battery power is empty, using default value: %s%%", g_qrzl_device_dynamic_data.remain_power);
    }
}

/**
 * Ensure device identification data is not empty
 */
static void ensure_device_identity_filled(void) {
    // MAC address default value
    if (!g_qrzl_device_static_data.mac || strlen(g_qrzl_device_static_data.mac) == 0) {
        strncpy(g_qrzl_device_static_data.mac, "00:11:22:33:44:55", sizeof(g_qrzl_device_static_data.mac) - 1);
        LOG_D(LOG_TAG, "MAC is empty, using default value: %s", g_qrzl_device_static_data.mac);
    }

    // Software version default value
    if (!g_qrzl_device_static_data.soft_version || strlen(g_qrzl_device_static_data.soft_version) == 0) {
        strncpy(g_qrzl_device_static_data.soft_version, "MZ804LD1.0_KSBC_COMMON_SL_V2.01.01.02P48U05_04-250703",
                sizeof(g_qrzl_device_static_data.soft_version) - 1);
        LOG_D(LOG_TAG, "Software version is empty, using default value: %s", g_qrzl_device_static_data.soft_version);
    }
}

/**
 * Ensure WiFi data is not empty
 */
static void ensure_wifi_data_filled(void) {
    // WiFi SSID default value
    if (!g_qrzl_device_dynamic_data.wifi_ssid || strlen(g_qrzl_device_dynamic_data.wifi_ssid) == 0) {
        strncpy(g_qrzl_device_dynamic_data.wifi_ssid, "", sizeof(g_qrzl_device_dynamic_data.wifi_ssid) - 1);
        LOG_D(LOG_TAG, "WiFi SSID is empty, using empty string.");
    }

    // WiFi password default value
    if (!g_qrzl_device_dynamic_data.wifi_key || strlen(g_qrzl_device_dynamic_data.wifi_key) == 0) {
        strncpy(g_qrzl_device_dynamic_data.wifi_key, "", sizeof(g_qrzl_device_dynamic_data.wifi_key) - 1);
        LOG_D(LOG_TAG, "WiFi password is empty, using empty string.");
    }
}

/**
 * Print current device data status (for debugging)
 */
static void print_device_data_status(void) {
    LOG_I(LOG_TAG, "=== Current Device Data Status ===");
    LOG_I(LOG_TAG, "Static Data:");
    LOG_I(LOG_TAG, "  SN: %s", g_qrzl_device_static_data.sn ? g_qrzl_device_static_data.sn : "NULL");
    LOG_I(LOG_TAG, "  IMEI: %s", g_qrzl_device_static_data.imei ? g_qrzl_device_static_data.imei : "NULL");
    LOG_I(LOG_TAG, "  MAC: %s", g_qrzl_device_static_data.mac ? g_qrzl_device_static_data.mac : "NULL");
    LOG_I(LOG_TAG, "  Software Version: %s", g_qrzl_device_static_data.soft_version ? g_qrzl_device_static_data.soft_version : "NULL");

    LOG_I(LOG_TAG, "Dynamic Data:");
    LOG_I(LOG_TAG, "  ICCID: %s", g_qrzl_device_dynamic_data.iccid ? g_qrzl_device_dynamic_data.iccid : "NULL");
    LOG_I(LOG_TAG, "  IMSI: %s", g_qrzl_device_dynamic_data.imsi ? g_qrzl_device_dynamic_data.imsi : "NULL");
    LOG_I(LOG_TAG, "  MCC: %s", g_qrzl_device_dynamic_data.mcc ? g_qrzl_device_dynamic_data.mcc : "NULL");
    LOG_I(LOG_TAG, "  MNC: %s", g_qrzl_device_dynamic_data.mnc ? g_qrzl_device_dynamic_data.mnc : "NULL");
    LOG_I(LOG_TAG, "  LAC: %s", g_qrzl_device_dynamic_data.lac ? g_qrzl_device_dynamic_data.lac : "NULL");
    LOG_I(LOG_TAG, "  CID: %s", g_qrzl_device_dynamic_data.cid ? g_qrzl_device_dynamic_data.cid : "NULL");
    LOG_I(LOG_TAG, "  RSSI: %s", g_qrzl_device_dynamic_data.rssi ? g_qrzl_device_dynamic_data.rssi : "NULL");
    LOG_I(LOG_TAG, "  Battery Power: %s%%", g_qrzl_device_dynamic_data.remain_power ? g_qrzl_device_dynamic_data.remain_power : "NULL");
    LOG_I(LOG_TAG, "  Connections: %d", g_qrzl_device_dynamic_data.conn_num);
    LOG_I(LOG_TAG, "================================");
}

/**
 * Resolve hostname to IP address
 * @param hostname Hostname
 * @param ip_buffer Buffer to store the resolved IP address
 * @param buffer_size Size of the buffer
 * @param interface Network interface to bind (can be NULL)
 * @return 0 on success, -1 on failure
 */
static int resolve_hostname(const char* hostname, char* ip_buffer, int buffer_size, const char* interface) {
    struct hostent *host_entry;
    struct in_addr addr;

    if (!hostname || !ip_buffer || buffer_size <= 0) {
        return -1;
    }

    // First, check if it is already an IP address
    if (inet_aton(hostname, &addr)) {
        strncpy(ip_buffer, hostname, buffer_size - 1);
        ip_buffer[buffer_size - 1] = '\0';
        return 0;
    }

    if (interface == NULL || strlen(interface) == 0) {
        // 没有指定接口或接口为空字符串，使用标准的gethostbyname
        LOG_D(LOG_TAG, "Resolving hostname '%s' using default route", hostname);

        host_entry = gethostbyname(hostname);
        if (host_entry == NULL) {
            LOG_E(LOG_TAG, "Failed to resolve hostname: %s", hostname);
            return -1;
        }

        // Get the first IP address
        if (host_entry->h_addr_list[0] != NULL) {
            struct in_addr *addr_ptr = (struct in_addr *)host_entry->h_addr_list[0];
            const char* ip_str = inet_ntoa(*addr_ptr);
            if (ip_str) {
                strncpy(ip_buffer, ip_str, buffer_size - 1);
                ip_buffer[buffer_size - 1] = '\0';
                LOG_D(LOG_TAG, "Resolved %s to %s using default route", hostname, ip_buffer);
                return 0;
            }
        }
    } else {
        // 指定了接口，使用gethostbyname_l进行网卡绑定解析
        LOG_D(LOG_TAG, "Resolving hostname '%s' using interface '%s'", hostname, interface);

        unsigned long ip = gethostbyname_l(hostname, interface);
        if(ip == 0) {
            LOG_E(LOG_TAG, "Failed to resolve hostname: %s using interface: %s", hostname, interface);
            return -1;
        }

        LOG_I(LOG_TAG, "Success to resolve hostname: %s %lu using interface: %s", hostname, ip, interface);
        if (inet_ntop(AF_INET, &ip, ip_buffer, buffer_size)) {
            ip_buffer[buffer_size - 1] = '\0';
            LOG_D(LOG_TAG, "Resolved %s to %s using interface %s", hostname, ip_buffer, interface);
            return 0;
        }
    }

    LOG_E(LOG_TAG, "Failed to get IP address for hostname: %s", hostname);
    return -1;
}

/**
 * Calculate checksum (from message header to message body end)
 */
static unsigned char calculate_xor_checksum(const unsigned char *data, size_t len) {
    unsigned char checksum = 0;
    size_t i;
    for (i = 0; i < len; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * Handle protocol escaping (0x7e -> 0x7d 0x02; 0x7d -> 0x7d 0x01)
 */
static size_t escape_protocol_data(const unsigned char *src, size_t src_len,
                                  unsigned char *dst, size_t dst_size) {
    size_t dst_len = 0;
    size_t i;
    for (i = 0; i < src_len && dst_len < dst_size - 2; i++) {
        if (src[i] == 0x7e) {
            if (dst_len + 2 >= dst_size) break;
            dst[dst_len++] = 0x7d;
            dst[dst_len++] = 0x02;
        } else if (src[i] == 0x7d) {
            if (dst_len + 2 >= dst_size) break;
            dst[dst_len++] = 0x7d;
            dst[dst_len++] = 0x01;
        } else {
            if (dst_len >= dst_size) break;
            dst[dst_len++] = src[i];
        }
    }
    return dst_len;
}

/**
 * Unescape protocol data
 */
static size_t unescape_protocol_data(const unsigned char *src, size_t src_len,
                                    unsigned char *dst, size_t dst_size) {
    size_t dst_len = 0;
    size_t i;
    for (i = 0; i < src_len && dst_len < dst_size; i++) {
        if (src[i] == 0x7d && i + 1 < src_len) {
            if (src[i+1] == 0x02) {
                dst[dst_len++] = 0x7e;
                i++; // Skip next byte
            } else if (src[i+1] == 0x01) {
                dst[dst_len++] = 0x7d;
                i++; // Skip next byte
            } else {
                dst[dst_len++] = src[i];
            }
        } else {
            dst[dst_len++] = src[i];
        }
    }
    return dst_len;
}

// ===================== HTTP请求处理 =====================

static int ito_cloud_http_post_with_response(const char* path, const char* host, int port,
                                            const char* post_data, char* response_buffer,
                                            size_t buffer_size, const char* interface)
{
    CURL *curl;
    CURLcode res;
    struct MemoryStruct chunk;
    chunk.memory = malloc(1);
    chunk.size = 0;

    char url[1024];
    snprintf(url, sizeof(url), "http://%s:%d%s", host, port, path);

    LOG_D(LOG_TAG, "HTTP POST URL: %s", url);
    LOG_D(LOG_TAG, "HTTP POST Data: %s", post_data ? post_data : "NULL");

    curl = curl_easy_init();
    if(curl) {
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");

        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&chunk);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, HTTP_REQUEST_TIMEOUT);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 10L); // Connection timeout 10 seconds

        // 绑定网络接口（如果指定了接口名称）
        if (interface && strlen(interface) > 0) {
            curl_easy_setopt(curl, CURLOPT_INTERFACE, interface);
            LOG_D(LOG_TAG, "HTTP POST 绑定网络接口: %s", interface);
        }

        res = curl_easy_perform(curl);
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
        
        if(res != CURLE_OK) {
            LOG_E(LOG_TAG, "curl_easy_perform() failed: %s", curl_easy_strerror(res));
            free(chunk.memory);
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
            return ITO_CLOUD_ERROR_HTTP; // HTTP请求失败（连接失败、超时等）
        }

        // Log HTTP status code and response with encoding conversion
        LOG_D(LOG_TAG, "HTTP %s => Status: %ld, Response: %s",
             path, http_code, chunk.memory);

        // Check HTTP status code
        if (http_code != 200) {
            LOG_E(LOG_TAG, "HTTP request failed with code: %ld", http_code);
            free(chunk.memory);
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
            return ITO_CLOUD_ERROR_NETWORK; // HTTP状态码错误，返回网络错误
        }

        // Parse response to check for success
        if (chunk.size > 0) {
            cJSON *root = cJSON_Parse(chunk.memory);
            if (root) {
                cJSON *success = cJSON_GetObjectItem(root, "success");
                if (!success || success->type != cJSON_True) {
                    cJSON *errorCode = cJSON_GetObjectItem(root, "errorCode");
                    cJSON *errorMessage = cJSON_GetObjectItem(root, "errorMessage");
                    if (errorMessage && errorMessage->valuestring) {
                        char* display_error = convert_utf8_to_gb2312(errorMessage->valuestring);
                        if (display_error) {
                            LOG_E(LOG_TAG, "API error: [%s] %s",
                                 errorCode ? errorCode->valuestring : "unknown",
                                 display_error);
                            free(display_error);
                        }
                    } else {
                        LOG_E(LOG_TAG, "API error: [%s] %s",
                             errorCode ? errorCode->valuestring : "unknown",
                             errorMessage ? errorMessage->valuestring : "unknown");
                    }

                    printf("\r\n");
                    for(int i = 0; i < strlen(errorMessage->valuestring); i++) {
                        printf("0x%02X ", errorMessage->valuestring[i]);
                    }
                    printf("\r\n");
                
                    cJSON_Delete(root);
                    free(chunk.memory);
                    curl_slist_free_all(headers);
                    curl_easy_cleanup(curl);
                    return ITO_CLOUD_ERROR_AUTH; // success=false，返回认证错误
                }
                cJSON_Delete(root);
            }
        }

        // If a response buffer is provided, copy the response data
        if (response_buffer && buffer_size > 0 && chunk.memory) {
            size_t copy_size = (chunk.size < buffer_size - 1) ? chunk.size : buffer_size - 1;
            memcpy(response_buffer, chunk.memory, copy_size);
            response_buffer[copy_size] = '\0';
        }

        free(chunk.memory);
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
    } else {
        LOG_E(LOG_TAG, "Failed to initialize CURL");
        return -1;
    }
    return 0;
}

static int ito_cloud_http_get_with_response(const char* path, const char* host, int port,
                                           const char* query_params, char* response_buffer,
                                           size_t buffer_size, const char* interface)
{
    CURL *curl;
    CURLcode res;
    struct MemoryStruct chunk;
    chunk.memory = malloc(1);
    chunk.size = 0;

    char url[1024];
    if (query_params && strlen(query_params) > 0) {
        snprintf(url, sizeof(url), "http://%s:%d%s?%s", host, port, path, query_params);
    } else {
        snprintf(url, sizeof(url), "http://%s:%d%s", host, port, path);
    }

    LOG_D(LOG_TAG, "HTTP GET URL: %s", url);

    curl = curl_easy_init();
    if(curl) {
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&chunk);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, HTTP_REQUEST_TIMEOUT);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 10L); // Connection timeout 10 seconds

        // 绑定网络接口（如果指定了接口名称）
        if (interface && strlen(interface) > 0) {
            curl_easy_setopt(curl, CURLOPT_INTERFACE, interface);
            LOG_D(LOG_TAG, "HTTP GET 绑定网络接口: %s", interface);
        } 

        res = curl_easy_perform(curl);
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

        if(res != CURLE_OK) {
            LOG_E(LOG_TAG, "curl_easy_perform() failed: %s", curl_easy_strerror(res));
            free(chunk.memory);
            curl_easy_cleanup(curl);
            return ITO_CLOUD_ERROR_HTTP; // HTTP请求失败（连接失败、超时等）
        }

        // Log HTTP status code and response with encoding conversion
        LOG_D(LOG_TAG, "HTTP %s => Status: %ld, Response: %s",
             path, http_code, chunk.memory);

        // Check HTTP status code
        if (http_code != 200) {
            LOG_E(LOG_TAG, "HTTP request failed with code: %ld", http_code);
            free(chunk.memory);
            curl_easy_cleanup(curl);
            return ITO_CLOUD_ERROR_NETWORK; // HTTP状态码错误，返回网络错误
        }

        // Parse response to check for success
        if (chunk.size > 0) {
            cJSON *root = cJSON_Parse(chunk.memory);
            if (root) {
                cJSON *success = cJSON_GetObjectItem(root, "success");
                if (!success || success->type != cJSON_True) {
                    cJSON *errorCode = cJSON_GetObjectItem(root, "errorCode");
                    cJSON *errorMessage = cJSON_GetObjectItem(root, "errorMessage");
                    if (errorMessage && errorMessage->valuestring) {
                        char* display_error = convert_utf8_to_gb2312(errorMessage->valuestring);
                        if (display_error) {
                            LOG_E(LOG_TAG, "API error: [%s] %s",
                                 errorCode ? errorCode->valuestring : "unknown",
                                 display_error);
                            free(display_error);
                        }
                    } else {
                        LOG_E(LOG_TAG, "API error: [%s] %s",
                             errorCode ? errorCode->valuestring : "unknown",
                             errorMessage ? errorMessage->valuestring : "unknown");
                    }  

                    printf("\r\n");
                    for(int i = 0; i < strlen(errorMessage->valuestring); i++) {
                        printf("0x%02X ", errorMessage->valuestring[i]);
                    }
                    printf("\r\n");

                    cJSON_Delete(root);
                    free(chunk.memory);
                    curl_easy_cleanup(curl);
                    return ITO_CLOUD_ERROR_AUTH; // success=false，返回认证错误
                }
                cJSON_Delete(root);
            }
        }

        // If a response buffer is provided, copy the response data
        if (response_buffer && buffer_size > 0 && chunk.memory) {
            size_t copy_size = (chunk.size < buffer_size - 1) ? chunk.size : buffer_size - 1;
            memcpy(response_buffer, chunk.memory, copy_size);
            response_buffer[copy_size] = '\0';
        }

        free(chunk.memory);
        curl_easy_cleanup(curl);
    } else {
        LOG_E(LOG_TAG, "Failed to initialize CURL");
        return -1;
    }
    return 0;
}

static int ito_cloud_http_post(const char* path, const char* host, int port, const char* post_data)
{
    return ito_cloud_http_post_with_response(path, host, port, post_data, NULL, 0, NULL);
}

/**
 * HTTP request with retry mechanism
 * @param interface 网络接口名称，如"wan1"，NULL表示不绑定特定接口
 */
static int ito_cloud_http_request_with_retry(const char* path, const char* host, int port,
                                            const char* post_data, char* response_buffer,
                                            size_t buffer_size, const char* interface)
{
    int retry_count = 0;
    int result = -1;

    // Construct full URL for debug output
    char debug_url[1024];
    snprintf(debug_url, sizeof(debug_url), "http://%s:%d%s", host, port, path);
    LOG_D(LOG_TAG, "HTTP Request with retry - URL: %s, Interface: %s", debug_url, interface ? interface : "default");

    while (retry_count <= HTTP_MAX_RETRIES) {
        result = ito_cloud_http_post_with_response(path, host, port, post_data,
                                                  response_buffer, buffer_size, interface);
        if (result == 0) {
            return 0; // Success
        }

        retry_count++;
        LOG_W(LOG_TAG, "HTTP request failed, retrying (%d/%d)...", retry_count, HTTP_MAX_RETRIES);
        sleep(1 << retry_count); // Exponential backoff
    }
    
    return result;
}

/**
 * HTTP request with retry mechanism (兼容性包装函数)
 */
static int ito_cloud_http_request_with_retry_compat(const char* path, const char* host, int port,
                                                   const char* post_data, char* response_buffer,
                                                   size_t buffer_size)
{
    return ito_cloud_http_request_with_retry(path, host, port, post_data, response_buffer, buffer_size, NULL);
}

// ===================== Configuration Retrieval =====================

static int ito_cloud_get_config(ito_cloud_config_t *config, const char *interface)
{
    CURL *curl;
    CURLcode res;
    struct MemoryStruct chunk;
    chunk.memory = malloc(1);
    chunk.size = 0;

    // Ensure all necessary data is not empty
    ensure_required_data_not_empty(REQUEST_TYPE_CONFIG);

    char url[1024];
    const char* version_code = get_version_code(g_qrzl_device_static_data.soft_version);

    snprintf(url, sizeof(url), "http://%s:%d%s?sn=%s&imsi=%s&versionName=%s&versionCode=%s",
             config->http_ip, config->http_port, CONFIG_PATH,
             g_qrzl_device_static_data.sn, g_qrzl_device_dynamic_data.imsi,
             g_qrzl_device_static_data.soft_version, version_code);

    LOG_D(LOG_TAG, "HTTP GET URL: %s", url);

    curl = curl_easy_init();
    if(curl) {
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&chunk);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, HTTP_REQUEST_TIMEOUT);     

        // 绑定网络接口（如果指定了接口名称）
        if (interface && strlen(interface) > 0) {
            curl_easy_setopt(curl, CURLOPT_INTERFACE, interface);
            LOG_D(LOG_TAG, "HTTP GET_CONFIG 绑定网络接口: %s", interface);
        }

        res = curl_easy_perform(curl);
        if(res != CURLE_OK) {
            LOG_E(LOG_TAG, "curl_easy_perform() failed: %s", curl_easy_strerror(res));
            free(chunk.memory);
            curl_easy_cleanup(curl);
            return -1;
        }

        // Log server response with encoding conversion
        LOG_D(LOG_TAG, "Response from server:\n%s", chunk.memory);

        cJSON *root = cJSON_Parse(chunk.memory);
        if (root) {
            cJSON *success = cJSON_GetObjectItem(root, "success");
            if (success && success->type == cJSON_True) {
                cJSON *data = cJSON_GetObjectItem(root, "data");
                if (data) {
                    cJSON *tcpIp = cJSON_GetObjectItem(data, "tcpIp");
                    cJSON *tcpPort = cJSON_GetObjectItem(data, "tcpPort");
                    cJSON *httpIp = cJSON_GetObjectItem(data, "httpIp");
                    cJSON *httpPort = cJSON_GetObjectItem(data, "httpPort");
                    cJSON *heartBeatTimes = cJSON_GetObjectItem(data, "heartBeatTimes");
                    cJSON *flowUploadTimes = cJSON_GetObjectItem(data, "flowUploadTimes");
                    cJSON *wifiName = cJSON_GetObjectItem(data, "wifiName");
                    cJSON *wifiPwd = cJSON_GetObjectItem(data, "wifiPwd");

                    if (tcpIp) strncpy(config->tcp_ip, tcpIp->valuestring, sizeof(config->tcp_ip) - 1);
                    if (tcpPort) config->tcp_port = tcpPort->valueint;
                    if (httpIp) strncpy(config->http_ip, httpIp->valuestring, sizeof(config->http_ip) - 1);
                    if (httpPort) config->http_port = httpPort->valueint;
                    if (heartBeatTimes) config->heart_beat_times = heartBeatTimes->valueint;
                    if (flowUploadTimes) config->flow_upload_times = flowUploadTimes->valueint;
                    if (wifiName) strncpy(config->wifi_name, wifiName->valuestring, sizeof(config->wifi_name) - 1);
                    if (wifiPwd) strncpy(config->wifi_pwd, wifiPwd->valuestring, sizeof(config->wifi_pwd) - 1);

                    // Handle WiFi configuration update
                    if (wifiName && wifiName->valuestring && strlen(wifiName->valuestring) > 0) {
                        LOG_I(LOG_TAG, "Received WiFi configuration update from server: SSID=%s", wifiName->valuestring);

                        // Initialize WiFi configuration struct
                        struct wifi_config_t wifi_config = {};
                        init_wifi_config_value(&wifi_config);

                        // Set new SSID
                        strncpy(wifi_config.ssid, wifiName->valuestring, sizeof(wifi_config.ssid) - 1);
                        wifi_config.ssid[sizeof(wifi_config.ssid) - 1] = '\0';

                        // Set new password (if provided)
                        if (wifiPwd && wifiPwd->valuestring && strlen(wifiPwd->valuestring) > 7) {
                            strncpy(wifi_config.key, wifiPwd->valuestring, sizeof(wifi_config.key) - 1);
                            wifi_config.key[sizeof(wifi_config.key) - 1] = '\0';
                            LOG_I(LOG_TAG, "Received WiFi password update from server.");
                        } else {
                            LOG_W(LOG_TAG, "WiFi password invalid or shorter than 8 characters, keeping original password.");
                        }

                        // Apply WiFi configuration update
                        int ret = update_wifi_by_config(&wifi_config);
                        if (ret == 0) {
                            LOG_I(LOG_TAG, "WiFi configuration updated successfully.");
                        } else {
                            LOG_E(LOG_TAG, "WiFi configuration update failed, error code: %d", ret);
                        }
                    }

                    LOG_I(LOG_TAG, "Get config success: tcp_ip=%s, tcp_port=%d", config->tcp_ip, config->tcp_port);
                }
            } else {
                cJSON *errorCode = cJSON_GetObjectItem(root, "errorCode");
                cJSON *errorMessage = cJSON_GetObjectItem(root, "errorMessage");

                if (errorMessage && errorMessage->valuestring) {
                    char* display_error = convert_utf8_to_gb2312(errorMessage->valuestring);
                    if (display_error) {
                        LOG_E(LOG_TAG, "Failed to get config: [%s] %s",
                                errorCode ? errorCode->valuestring : "unknown",
                                display_error);
                        free(display_error);
                    } else {
                        LOG_E(LOG_TAG, "Failed to get config: [%s] %s",
                             errorCode ? errorCode->valuestring : "unknown",
                             errorMessage ? errorMessage->valuestring : "unknown");
                    }
                }
            }
            cJSON_Delete(root);
        } else {
            LOG_E(LOG_TAG, "cJSON_Parse error: %s", cJSON_GetErrorPtr());
        }

        curl_easy_cleanup(curl);
        free(chunk.memory);
    }
    return 0;
}

// ===================== TCP设备控制处理 =====================

static int ito_cloud_handle_device_control(cJSON *control_data);
static int ito_cloud_send_tcp_generic_reply(WORD seq_num, WORD msg_id, BYTE result);
static void ito_cloud_process_tcp_message(const unsigned char* buffer, size_t length, unsigned short msg_id);

// Convert a string to 8421 BCD code and store it in an unsigned char array in big-endian format
void string_to_8421(const char *str, unsigned char *bcd)
{
    int i = 0;  // Index for the string
    int j = 0;  // Index for the BCD array

    // Iterate through each character in the string
    while (str[i] != '\0') {
        if (str[i] >= '0' && str[i] <= '9') {
            char digit = str[i] - '0';  // Get the number corresponding to the current character
            if (i % 2 == 0) {
                // Store the even-positioned digit in the high 4 bits
                bcd[j] |= (digit << 4);  // Store the 4-bit number in the high 4 bits
            } else {
                // Store the odd-positioned digit in the low 4 bits
                bcd[j] |= digit;  // Store the 4-bit number in the low 4 bits
                j++;  // If it's the second digit, move to the next byte
            }
        } else {
            LOG_E(LOG_TAG, "Invalid character in the string: %c", str[i]);
            return;
        }
        i++;
    }
}

/**
 * Generic TCP message sending function
 * @param sockfd Socket identifier
 * @param msg_id Message ID
 * @param msg_body Message body to send
 * @param msg_body_len Length of the message body to send
 * @return Returns the number of bytes sent on success, -1 on failure
 */
static int common_tcp_send_message(const int sockfd, WORD msg_id, const unsigned char *msg_body, WORD msg_body_len)
{
    // Complete message (without flag bits)
    unsigned char not_flag_msg[MAX_MSG_BODY_LEN] = {0};
    // Length of the complete message (without flag bits)
    size_t msg_len = 0;
    
    // Checksum
    unsigned char checksum = 0;
    // SN to BCD code
    unsigned char sn_bcd[7] = {0};
    memset(sn_bcd, 0, sizeof(sn_bcd));
    string_to_8421(g_qrzl_device_static_data.sn, sn_bcd);
    // Message body attribute
    unsigned short msg_body_attr = msg_body_len;

    // Host byte order to network byte order
    unsigned short net_msg_id = htons(msg_id);
    unsigned short net_msg_body_attr = htons(msg_body_len);

    // Start building the request header
    unsigned char msg_header_str[13] = {0};
    // Offset
    int offset = 0;
    // Fill message ID
    memcpy(msg_header_str + offset, &net_msg_id, sizeof(net_msg_id));
    offset += sizeof(net_msg_id);
    // Fill message body attribute
    memcpy(msg_header_str + offset, &net_msg_body_attr, sizeof(net_msg_body_attr));
    offset += sizeof(net_msg_body_attr);
    // Fill SN
    memcpy(msg_header_str + offset, sn_bcd, sizeof(sn_bcd));
    offset += sizeof(sn_bcd);
    // Fill message sequence number
    memcpy(msg_header_str + offset, &g_msg_sequence, sizeof(g_msg_sequence));
    // Increment sequence number
    g_msg_sequence = (g_msg_sequence + 1) % 65536;


    if (msg_body_len != 0)
    {
        LOG_I(LOG_TAG, "TCP sending message is not empty");
        // Initialize header + body message
        unsigned char header_body_msg[MAX_MSG_BODY_LEN] = {0};
        // Header + body message length
        u_int16_t header_body_msg_len = sizeof(msg_header_str)+msg_body_len;
        // Fill header
        memcpy(header_body_msg, msg_header_str, sizeof(msg_header_str));
        // Fill body (header + body)
        memcpy(header_body_msg+sizeof(msg_header_str), msg_body, msg_body_len);
        // Calculate checksum (from the header, XOR with the next byte until the byte before the checksum, occupies 1 byte)
        checksum = calculate_xor_checksum(header_body_msg, header_body_msg_len);

        // Fill into the complete message not_flag_msg
        memcpy(not_flag_msg, header_body_msg, header_body_msg_len);
        // Add checksum to the end of the message
        memcpy(not_flag_msg+header_body_msg_len, &checksum, sizeof(checksum));
        // Calculate the length of the complete message
        msg_len = header_body_msg_len + sizeof(checksum);
    }
    else
    {
        LOG_I(LOG_TAG, "TCP sending message is empty\n");
        // Calculate checksum
        checksum = calculate_xor_checksum(msg_header_str, sizeof(msg_header_str));
        // Fill the complete message
        memcpy(not_flag_msg, msg_header_str, sizeof(msg_header_str));
        memcpy(not_flag_msg+sizeof(msg_header_str), &checksum, sizeof(checksum));
        msg_len = sizeof(msg_header_str) + sizeof(checksum);
    }
    
    LOG_I(LOG_TAG,"Message construction complete, starting escape processing...");
    unsigned char escaped_msg[MAX_MSG_BODY_LEN] = {0};
    escape_protocol_data(not_flag_msg, &msg_len, escaped_msg, sizeof(escaped_msg));

    // Build the complete message with flag bits (flag + header + body + checksum + flag)
    unsigned char message[MAX_MSG_BODY_LEN] = {0};
    message[0] = IDENTIFING_BIT;
    memcpy(message+1, escaped_msg, msg_len);
    message[msg_len+1] = IDENTIFING_BIT;
    // Update message length + 2 flag bits
    msg_len += 2;
    LOG_I(LOG_TAG,"Message escaping complete, ready to send, length: %d", msg_len);

    ssize_t sent_bytes = send(sockfd, message, msg_len, 0);
    if (sent_bytes == -1)
    {
        LOG_E(LOG_TAG, "Send failed, error code: %d", errno);
        LOG_E(LOG_TAG, "Error description: %s", strerror(errno));
    }
    return sent_bytes;
}

/**
 * TCP 接收消息通用函数
 * @param rev_msg 接收到的消息
 * @param rev_msg_len 接收到的消息长度
 */
static void common_tcp_rev_message(const unsigned char *rev_msg, size_t rev_msg_len)
{
    if (rev_msg_len < 14) {
        LOG_E(LOG_TAG, "Received message too short: %d", rev_msg_len);
        return;
    }

    // 消息ID
    WORD msg_id;
    // 消息体属性
    WORD msg_body_attr;
    // SN
    unsigned char sn_bcd[7] = {0};
    // 消息流水号
    WORD msg_seq_num;

    // 接收到的数据
    unsigned char *msg_now_ptr = rev_msg;

    memcpy(&msg_id, msg_now_ptr, sizeof(msg_id));
    msg_id = ntohs(msg_id);
    msg_now_ptr += sizeof(msg_id);

    memcpy(&msg_body_attr, msg_now_ptr, sizeof(msg_body_attr));
    msg_body_attr = ntohs(msg_body_attr);
    msg_now_ptr += sizeof(msg_body_attr);

    memcpy(sn_bcd, msg_now_ptr, sizeof(sn_bcd));
    msg_now_ptr += sizeof(sn_bcd);

    memcpy(&msg_seq_num, msg_now_ptr, sizeof(msg_seq_num));
    msg_seq_num = ntohs(msg_seq_num);
    msg_now_ptr += sizeof(msg_seq_num);

    LOG_I(LOG_TAG, "Received Message ID: 0x%04x", msg_id);
    LOG_I(LOG_TAG, "Message Body Attribute: 0x%04x", msg_body_attr);
    LOG_I(LOG_TAG, "Sequence Number: %u", msg_seq_num);

    // 消息体长度，低10位为消息体长度
    u_int16_t body_msg_len = msg_body_attr & 1023;
    // body_msg 开始指向消息体
    unsigned char *body_msg = msg_now_ptr;
    LOG_I(LOG_TAG, "body_msg_len: %d", body_msg_len);

    // 根据消息ID处理消息体
    switch (msg_id) {
        case 0x0001:  // 终端通用应答
            LOG_I(LOG_TAG, "Received General Terminal Acknowledgment.");
            break;
        case 0x8001:  // 平台通用应答
            LOG_I(LOG_TAG, "Received General Platform Acknowledgment.");
            break;
        case 0x0002:  // 终端心跳
            LOG_I(LOG_TAG, "Received Terminal Heartbeat.");
            break;
        case 0x07d2:
            LOG_I(LOG_TAG, "server msg.");
            ito_cloud_process_tcp_message(body_msg, body_msg_len, msg_seq_num);
            break;
        default:
            LOG_I(LOG_TAG, "Received Unknown Message ID.");
            break;
    }
}

/**
 * 处理TCP消息
 */
static void ito_cloud_process_tcp_message(const unsigned char* buffer, size_t length, unsigned short msg_seq_num)
{

    // TODO 在自定义的消息体中可能会有消息ID和保留字段，实际需要打印查看是否存在，此处先尝试获取
    u_int16_t msg_control_id;
    u_int8_t *body_msg_now_ptr = buffer;

    // 消息体存储缓冲区
    unsigned char msg_data[512] = {0};

    // 控制消息ID
    memcpy(&msg_control_id, body_msg_now_ptr, sizeof(msg_control_id));
    msg_control_id = ntohs(msg_control_id);
    body_msg_now_ptr += sizeof(msg_control_id);

    body_msg_now_ptr += 2; // 保留字段跳过

    // 消息体长度(减去控制消息ID和保留字段的长度)
    u_int16_t control_data_len = msg_data - sizeof(msg_control_id) - 2;

    // 填充消息体缓冲区
    memcpy(msg_data, body_msg_now_ptr,control_data_len);
    // 结束符别忘了
    msg_data[control_data_len] = '\0';

    LOG_I(LOG_TAG, "msg_control_id = 0x%04x", msg_control_id);
    LOG_I(LOG_TAG, "msg_data: %s", msg_data);

    // 转换成JSON体处理
    cJSON *root = cJSON_Parse(msg_data);
    
    if (root) {
        int ret = ito_cloud_handle_device_control(root);
        ito_cloud_send_tcp_generic_reply(msg_seq_num, 0x07d2, ret ? 1 : 0);
        cJSON_Delete(root);
    } else {
        LOG_E(LOG_TAG, "JSON 转换失败，请检查数据格式是否正确.");
    }

    // // 简化的消息解析（实际需按协议处理转义/校验等）
    // if (length < 16) {
    //     LOG_E(LOG_TAG, "Invalid TCP message length: %zu", length);
    //     return;
    // }

    // // 解析消息头（大端序）
    // WORD msg_id = (buffer[0] << 8) | buffer[1];
    // WORD msg_seq = (buffer[11] << 8) | buffer[12];

    // LOG_D(LOG_TAG, "Received TCP msg: ID=0x%04X, Seq=%u", msg_id, msg_seq);

    // // 设备控制消息处理
    // if (msg_id == 0x07d2) { // 设备控制消息ID
    //     // 转换为JSON处理（实际协议是二进制格式，这里简化为JSON）
    //     char json_buf[256];
    //     snprintf(json_buf, sizeof(json_buf), 
    //         "{\"cmdID\":%d,\"speedLimitVal\":%d,\"isp\":%d,\"ssid\":\"%s\",\"pwd\":\"%s\"}",
    //         buffer[16],  // cmdID
    //         (buffer[20] << 24) | (buffer[21] << 16) | (buffer[22] << 8) | buffer[23], // speedLimitVal
    //         buffer[24],  // isp
    //         &buffer[25], // ssid
    //         &buffer[25 + strlen((char*)&buffer[25]) + 1] // pwd
    //     );

    //     cJSON *root = cJSON_Parse(json_buf);
    //     if (root) {
    //         int ret = ito_cloud_handle_device_control(root);
    //         ito_cloud_send_tcp_generic_reply(msg_seq, 0x07d2, ret ? 1 : 0);
    //         cJSON_Delete(root);
    //     }
    // }
}

/**
 * 设备限速
 * @param control_data json数据
 * @return 1 成功，0 失败
 */
static int device_speed_limit(cJSON *control_data)
{   
    uint64_t up_speed_limit = 0L;
    uint64_t down_speed_limit = 0L;
    cJSON *limit_switch = cJSON_GetObjectItem(control_data, "speedLimitSwitch");
    if (limit_switch && limit_switch->type == cJSON_Number) {
        if (limit_switch->valueint == 1) {
            cJSON *up = cJSON_GetObjectItem(control_data, "speedLimitUpVal");
            cJSON *down = cJSON_GetObjectItem(control_data, "speedLimitDownVal");
            if (up && up->type == cJSON_Number) {
                up_speed_limit = up->valueint < 0 ? 0L : up->valueint;
            }
             if (down && down->type == cJSON_Number) {
                down_speed_limit = down->valueint < 0 ? 0L : down->valueint;
            }
        }
        limit_net_speed(up_speed_limit, down_speed_limit);
    } else {
        LOG_W(LOG_TAG, "speedLimitSwitch is NULL or not a number.");
        return 0;
    }
    return 1;
}

/**
 * 处理设备控制命令
 */
static int ito_cloud_handle_device_control(cJSON *control_data)
{
    cJSON *cmd_id = cJSON_GetObjectItem(control_data, "cmdID");
    if (!cmd_id) return 0;

    int command = cmd_id->valueint;
    LOG_I(LOG_TAG, "Processing device control: CMD=%d", command);

    switch (command) {
        case CMD_SPEED_LIMIT:
            LOG_W(LOG_TAG, "Executing ESPEED LIMIT command");
            return device_speed_limit(control_data);

        case CMD_POWER_OFF:
            LOG_W(LOG_TAG, "Executing POWER OFF command");
            shutdown_device();
            return 1;
            
        case CMD_REBOOT:
            LOG_W(LOG_TAG, "Executing REBOOT command");
            restart_device();
            return 1;
        
        case CMD_NETWORK_SWITCH:
            // TODO 三网切换
            return 1;
            
        case CMD_WIFI_MODIFY: {
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);

            cJSON *ssid = cJSON_GetObjectItem(control_data, "ssid");
            cJSON *pwd = cJSON_GetObjectItem(control_data, "pwd");
            
            if (ssid && pwd) {
                if (ssid->type == cJSON_String) {
                    LOG_I(LOG_TAG, "Updating WiFi: SSID=%s", ssid->valuestring);
                    snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", ssid->valuestring);
                }
                if (pwd->type == cJSON_String) {
                    LOG_I(LOG_TAG, "Updating WiFi: PSSWORD=%s", pwd->valuestring);
                    snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", pwd->valuestring);
                }
                update_wifi_by_config(&wifi_config);
                return 1;
            }
            return 0;
        }

        case CMD_DEVICE_UPGRADE:
            // TODO 设备升级推送
            return 1;

        case CMD_FACTORY_RESET:
            LOG_W(LOG_TAG, "Executing FACTORY RESET command");
            reset_device();
            return 1;
        
        case CMD_LOG_COLLECT:
            // TODO 日志采集
            return 1;

        case CMD_CLEAR_CACHE:
            // TODO 清除设备缓存
            return 1;

        case CMD_SET_WHITELIST:
            // TODO 设置白名单
            return 1;

        default:
            LOG_E(LOG_TAG, "Unsupported command: %d", command);
            return 0;
    }
}

/**
 * 发送TCP通用应答
 */
static int ito_cloud_send_tcp_generic_reply(WORD seq_num, WORD msg_id, BYTE result)
{
    unsigned char reply[16] = {
        0x80, 0x01,  // 消息ID: 0x8001 (平台通用应答)
        0x00, 0x00,  // 消息体属性
        // 设备SN (示例)
        '0','0','0','0','0','0','0','0','0','0','0','0',
        (seq_num >> 8) & 0xFF,  // 流水号高字节
        seq_num & 0xFF,         // 流水号低字节
        (msg_id >> 8) & 0xFF,   // 应答ID高字节
        msg_id & 0xFF,          // 应答ID低字节
        result                  // 结果(0失败/1成功)
    };

    if (send(g_tcp_sockfd, reply, sizeof(reply), 0) < 0) {
        LOG_E(LOG_TAG, "Failed to send TCP reply");
        return -1;
    }
    return 0;
}

// ===================== 心跳包实现 =====================

/**
 * 发送TCP心跳包 (符合协议规范)
 */
static int ito_cloud_send_tcp_heartbeat(void) {
    if (g_tcp_sockfd < 0) {
        LOG_E(LOG_TAG, "TCP socket is not connected.");
        return -1;
    }

    // 1. 构建消息头 (根据协议2.3.4节)
    unsigned char raw_buffer[32] = {0};
    size_t raw_len = 0;
    
    // 消息ID: 0x0002 (终端心跳)
    raw_buffer[raw_len++] = 0x00;  // 高字节
    raw_buffer[raw_len++] = 0x02;  // 低字节
    
    // 消息体属性 (0x0000 - 无消息体)
    raw_buffer[raw_len++] = 0x00;  // 高字节
    raw_buffer[raw_len++] = 0x00;  // 低字节
    
    // 设备SN (BCD[7]格式)
    // 将SN字符串转换为BCD格式，不足12位前面补0
    char sn_str[13] = {0};
    int sn_len = strlen(g_qrzl_device_static_data.sn);
    int sn_pos = 0;
    
    // 补0至12位
    int zero_padding = 12 - sn_len;
    int i;
    for (i = 0; i < zero_padding; i++) {
        sn_str[sn_pos++] = '0';
    }

    // 复制SN
    for (i = 0; i < sn_len && sn_pos < 12; i++) {
        sn_str[sn_pos++] = g_qrzl_device_static_data.sn[i];
    }
    sn_str[12] = '\0';

    // 转换为BCD格式 (每2个字符组成1字节)
    for (i = 0; i < 6; i++) {
        char high = sn_str[i * 2] - '0';
        char low = sn_str[i * 2 + 1] - '0';
        raw_buffer[raw_len++] = (high << 4) | low;
    }
    // 第7个BCD字节 (最后2位补0)
    raw_buffer[raw_len++] = 0x00;
    
    // 消息流水号 (循环累加)
    raw_buffer[raw_len++] = (g_msg_sequence >> 8) & 0xFF;  // 高字节
    raw_buffer[raw_len++] = g_msg_sequence & 0xFF;         // 低字节
    g_msg_sequence = (g_msg_sequence + 1) % 65536;
    
    // 2. 计算校验码 (仅消息头，因为消息体为空)
    unsigned char checksum = calculate_xor_checksum(raw_buffer, raw_len);
    
    // 3. 构建完整消息帧 (带转义)
    unsigned char final_buffer[64] = {0};
    size_t final_len = 0;
    
    // 起始标识位
    final_buffer[final_len++] = 0x7e;
    
    // 添加转义后的消息头
    unsigned char escaped_header[32] = {0};
    size_t escaped_header_len = escape_protocol_data(raw_buffer, raw_len, 
                                                   escaped_header, sizeof(escaped_header));
    memcpy(final_buffer + final_len, escaped_header, escaped_header_len);
    final_len += escaped_header_len;
    
    // 添加转义后的校验码
    unsigned char escaped_checksum[2] = {0};
    size_t escaped_checksum_len = escape_protocol_data(&checksum, 1, 
                                                      escaped_checksum, sizeof(escaped_checksum));
    memcpy(final_buffer + final_len, escaped_checksum, escaped_checksum_len);
    final_len += escaped_checksum_len;
    
    // 结束标识位
    final_buffer[final_len++] = 0x7e;
    
    LOG_D(LOG_TAG, "Sending heartbeat (size: %zu bytes)", final_len);
    
    // 4. 发送心跳包
    if (send(g_tcp_sockfd, final_buffer, final_len, 0) < 0) {
        LOG_E(LOG_TAG, "Failed to send heartbeat: %s", strerror(errno));
        return -1;
    }
    
    // 5. 接收并处理响应 (平台通用应答)
    unsigned char resp_buffer[128] = {0};
    fd_set read_fds;
    struct timeval tv = {.tv_sec = 5, .tv_usec = 0}; // 5秒超时
    
    FD_ZERO(&read_fds);
    FD_SET(g_tcp_sockfd, &read_fds);
    
    if (select(g_tcp_sockfd + 1, &read_fds, NULL, NULL, &tv) > 0) {
        if (FD_ISSET(g_tcp_sockfd, &read_fds)) {
            ssize_t n = recv(g_tcp_sockfd, resp_buffer, sizeof(resp_buffer) - 1, 0);
            if (n > 0) {
                LOG_D(LOG_TAG, "Received %zd bytes response", n);
                
                // 简化的响应验证 (实际应完整解析协议)
                if (n >= 4 && resp_buffer[0] == 0x7e && resp_buffer[1] == 0x80 && resp_buffer[2] == 0x01) {
                    LOG_I(LOG_TAG, "Heartbeat acknowledged");
                    g_heartbeat_ok = 1;
                    return 0;
                }
                
                LOG_E(LOG_TAG, "Invalid heartbeat response");
            } else if (n == 0) {
                LOG_W(LOG_TAG, "TCP connection closed by peer");
                //g_heartbeat_ok = 0;
                return -1;
            } else {
                LOG_E(LOG_TAG, "TCP recv error: %s", strerror(errno));
            }
        }
    } else {
        LOG_W(LOG_TAG, "Heartbeat response timeout");
    }
    
    //g_heartbeat_ok = 0;
    return -1;
}

// ===================== 业务功能实现 =====================

static int ito_cloud_report_flow(const char *interface)
{
    char post_data[1024];

    // 确保静态数据已初始化
    ensure_static_data_initialized();

    // 更新动态数据
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_FLOW_UPLOAD);

    char gsm_info[128];
    snprintf(gsm_info, sizeof(gsm_info), "%s,%s,%s,%s,%s",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mnc,
             g_qrzl_device_dynamic_data.lac,
             g_qrzl_device_dynamic_data.cid,
             g_qrzl_device_dynamic_data.rssi);

    // 参考实际数据格式构造参数
    /*
    device_total_flow=6292&seed_total_flow=6292&signal=111&devcie_con_num=0&isp=3&sn=56132044000056&using_iccid=89860426102190404517&seed_card_iccid=89860426102190404517&phoneType=GSM&cdma=11,65534,126343938,,,83,11&gsm=460,11,30516,126343938,83,11&baseStationList=["460,00,10173,176305679,106","460,00,10133,176305123,105","460,00,10173,176305679,106","460,00,10121,176305679,106","460,00,10143,176305679,106","460,00,10133,176305679,106","460,00,10121,176305679,106","460,00,10173,176305679,106","460,00,10155,176305679,106","460,00,10173,176305679,106"]
    */

    // 获取云卡信息
    extern CloudCardInfo cloud_card_info;
    const char* vsim_iccid = (cloud_card_info.is_valid && strlen(cloud_card_info.real_iccid) > 0) ?
                            cloud_card_info.real_iccid : "";

    // // 确保流量数据不为0，使用默认值
    // if (g_qrzl_device_dynamic_data.realtime_total_bytes == 0)  {
    //     g_qrzl_device_dynamic_data.realtime_total_bytes = 6292; // 参考新的实际数据
    // }

    // 构造CDMA信息（参考新的实际数据格式：11,65534,126343938,,,83,11）
    char cdma_info[128];
    snprintf(cdma_info, sizeof(cdma_info), "%s,65534,%s,,,%s,%s",
             g_qrzl_device_dynamic_data.mnc ? g_qrzl_device_dynamic_data.mnc : "11",
             g_qrzl_device_dynamic_data.cid ? g_qrzl_device_dynamic_data.cid : "126343938",
             g_qrzl_device_dynamic_data.rssi,
             g_qrzl_device_dynamic_data.mnc ? g_qrzl_device_dynamic_data.mnc : "11");

    // 构造GSM信息（参考新的实际数据格式：460,11,30516,126343938,83,11）
    char gsm_enhanced[128];
    snprintf(gsm_enhanced, sizeof(gsm_enhanced), "%s,%s,%s,%s,%s,%s",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mnc,
             g_qrzl_device_dynamic_data.lac ? g_qrzl_device_dynamic_data.lac : "30516",
             g_qrzl_device_dynamic_data.cid ? g_qrzl_device_dynamic_data.cid : "126343938",
             g_qrzl_device_dynamic_data.rssi,
             g_qrzl_device_dynamic_data.mnc ? g_qrzl_device_dynamic_data.mnc : "11");

    // 构造基站列表（JSON数组格式，不需要URL编码）
    char base_station_list[512];
    snprintf(base_station_list, sizeof(base_station_list),
             "[\"%s,00,10173,176305679,106\",\"%s,00,10133,176305123,105\",\"%s,00,10173,176305679,106\",\"%s,00,10121,176305679,106\",\"%s,00,10143,176305679,106\"]",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mcc);

    // 参考新的实际数据格式：device_total_flow=6292&seed_total_flow=6292&signal=111&devcie_con_num=0&isp=3&sn=56132044000056&using_iccid=89860426102190404517&seed_card_iccid=89860426102190404517&phoneType=GSM&cdma=11,65534,126343938,,,83,11&gsm=460,11,30516,126343938,83,11&baseStationList=["460,00,10173,176305679,106",...]
    uint64_t device_current_flow_usage = g_qrzl_device_dynamic_data.realtime_total_bytes - last_send_total_flow >= 0 ? g_qrzl_device_dynamic_data.realtime_total_bytes - last_send_total_flow : 0; // 计算上次与本次流量使用的差值

    // 根据认证状态决定using_iccid：鉴权成功后使用云卡ICCID，否则使用种子卡ICCID
    const char* using_iccid = (g_auth_successful && strlen(vsim_iccid) > 0) ?
                              vsim_iccid : g_qrzl_device_dynamic_data.iccid;
    // seed_card_iccid 始终使用种子卡ICCID
    const char* seed_card_iccid = g_qrzl_device_dynamic_data.iccid;

    snprintf(post_data, sizeof(post_data),
             "device_total_flow=%llu&seed_total_flow=%llu&signal=%s&devcie_con_num=%d&isp=%d&sn=%s&using_iccid=%s&seed_card_iccid=%s&phoneType=GSM&cdma=%s&gsm=%s&baseStationList=%s",
             device_current_flow_usage,
             device_current_flow_usage, // seed_total_flow 使用相同值
             g_qrzl_device_dynamic_data.rssi,
             g_qrzl_device_dynamic_data.conn_num,
             get_isp_by_imsi(g_qrzl_device_dynamic_data.imsi),
             g_qrzl_device_static_data.sn,
             using_iccid,      // using_iccid: 鉴权成功后使用云卡ICCID，否则使用种子卡ICCID
             seed_card_iccid,  // seed_card_iccid: 始终使用种子卡ICCID
             cdma_info,
             gsm_enhanced,
             base_station_list);

    // URL 编码 post_data
    char encoded_data[2048] = {0};
    ito_url_encode(post_data, encoded_data, sizeof(encoded_data));

    LOG_D(LOG_TAG, "流量上报ICCID信息:");
    LOG_D(LOG_TAG, "  认证状态: %s", g_auth_successful ? "已认证" : "未认证");
    LOG_D(LOG_TAG, "  种子卡ICCID: %s", g_qrzl_device_dynamic_data.iccid);
    LOG_D(LOG_TAG, "  云卡ICCID: %s", strlen(vsim_iccid) > 0 ? vsim_iccid : "无");
    LOG_D(LOG_TAG, "  using_iccid: %s", using_iccid);
    LOG_D(LOG_TAG, "  seed_card_iccid: %s", seed_card_iccid);

    LOG_D(LOG_TAG, "Flow upload request query params (original): %s", post_data);
    LOG_D(LOG_TAG, "Flow upload request query params (encoded): %s", encoded_data);

    // 使用 GET 请求方式，传递 URL 编码后的数据
    if (ito_cloud_http_get_with_response(FLOW_UPLOAD_PATH, g_cloud_config.http_ip,
                                        g_cloud_config.http_port, encoded_data, NULL, 0, interface) != 0) {
        LOG_E(LOG_TAG, "Failed to report flow");
        return -1;
    }

    // 流量上报完成之后记录本次已使用的流量，用于下次上报计算差值
    last_send_total_flow = g_qrzl_device_dynamic_data.realtime_total_bytes;
    
    return 0;
}

static int ito_cloud_upload_device_info(const char *interface)
{
    // 确保静态数据已初始化
    ensure_static_data_initialized();

    // 更新动态数据
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_DEVICE_INFO);

    cJSON *root = cJSON_CreateObject();
    if (root == NULL) {
        LOG_E(LOG_TAG, "Failed to create cJSON object.");
        return -1;
    }

    /*
    {"sn":"33332513000021","androidId":"","boardName":"MZ803","devcie_con_num":0,
    "deviceBrand":"ZXIC","electric":"100","packageName":"",
    "seedCard":[{"iccid":"89861124207033765695","imsi":"***************","sequence":"2","quickNetStatus":"1","networkStatus":"0","csq":"","defUsed":false},{"iccid":"89860832132440307389","imsi":"***************","sequence":"1","quickNetStatus":"0","networkStatus":"1","csq":"-62","defUsed":true}],"ssid":"JH-000021","wifipwd":"12345678","wifiMac":"3C:68:01:6B:53:B3","systemVersion":"MZ803LDW3.2_KSBC_JH_VSIM_SL_V01.01.02P48U06_03","versionCode":"03","versionName":"MZ803LDW3.2_KSBC_JH_VSIM_SL_V01.01.02P48U06_03","seed_card_iccid":"89861124207033765695","seed_card_imei":"866733070000012","seed_card_imsi":"***************","phoneType":"GSM","cdma":"","gsm":"460,11,9561,127398418,-62"}
    */

    cJSON_AddStringToObject(root, "sn", g_qrzl_device_static_data.sn);
    cJSON_AddStringToObject(root, "androidId", "");
    cJSON_AddStringToObject(root, "boardName", "MZ803");
    cJSON_AddNumberToObject(root, "devcie_con_num", g_qrzl_device_dynamic_data.conn_num);
    cJSON_AddStringToObject(root, "deviceBrand", "ZXIC");
    cJSON_AddStringToObject(root, "electric", g_qrzl_device_dynamic_data.remain_power);
    cJSON_AddStringToObject(root, "packageName", "");
    cJSON_AddStringToObject(root, "ssid", g_qrzl_device_dynamic_data.wifi_ssid);
    cJSON_AddStringToObject(root, "wifipwd", g_qrzl_device_dynamic_data.wifi_key);
    cJSON_AddStringToObject(root, "wifiMac", g_qrzl_device_static_data.mac);
    cJSON_AddStringToObject(root, "systemVersion", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(root, "versionCode", get_version_code(g_qrzl_device_static_data.soft_version));
    cJSON_AddStringToObject(root, "versionName", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(root, "seed_card_iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "seed_card_imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(root, "seed_card_imsi", g_qrzl_device_dynamic_data.imsi);
    // 添加虚拟卡信息（根据协议文档5.1.4节）
    cJSON_AddStringToObject(root, "virtual_card_iccid", "");
    cJSON_AddStringToObject(root, "virtual_card_imei", "");
    cJSON_AddStringToObject(root, "virtual_card_imsi", "");
    cJSON_AddStringToObject(root, "phoneType", "GSM");
    cJSON_AddStringToObject(root, "cdma", "");

    char gsm_info[128];
    snprintf(gsm_info, sizeof(gsm_info), "%s,%s,%s,%s,%s",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mnc,
             g_qrzl_device_dynamic_data.lac,
             g_qrzl_device_dynamic_data.cid,
             g_qrzl_device_dynamic_data.rssi);
    cJSON_AddStringToObject(root, "gsm", gsm_info);

    cJSON *seedCard_array = cJSON_CreateArray();
    if (seedCard_array == NULL) {
        LOG_E(LOG_TAG, "Failed to create seedCard array.");
        cJSON_Delete(root);
        return -1;
    }
    cJSON_AddItemToObject(root, "seedCard", seedCard_array);

    // 添加卡1信息
    if (strlen(g_qrzl_device_static_data.nvro_esim1_iccid) > 0) {
        cJSON *card1 = cJSON_CreateObject();
        cJSON_AddStringToObject(card1, "iccid", g_qrzl_device_static_data.nvro_esim1_iccid);
        cJSON_AddStringToObject(card1, "imsi", g_qrzl_device_static_data.nvro_esim1_imsi);
        cJSON_AddStringToObject(card1, "sequence", "1");
        cJSON_AddStringToObject(card1, "quickNetStatus", g_qrzl_device_dynamic_data.esim1_net_status ? "1" : "0");
        cJSON_AddStringToObject(card1, "networkStatus", g_qrzl_device_dynamic_data.esim1_net_status ? "1" : "0");
        cJSON_AddStringToObject(card1, "csq", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim1_iccid) == 0 ? g_qrzl_device_dynamic_data.rssi : "");
        cJSON_AddBoolToObject(card1, "defUsed", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim1_iccid) == 0);
        cJSON_AddItemToArray(seedCard_array, card1);
    }

    // 添加卡2信息
    if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) > 0) {
        cJSON *card2 = cJSON_CreateObject();
        cJSON_AddStringToObject(card2, "iccid", g_qrzl_device_static_data.nvro_esim2_iccid);
        cJSON_AddStringToObject(card2, "imsi", g_qrzl_device_static_data.nvro_esim2_imsi);
        cJSON_AddStringToObject(card2, "sequence", "2");
        cJSON_AddStringToObject(card2, "quickNetStatus", g_qrzl_device_dynamic_data.esim2_net_status ? "1" : "0");
        cJSON_AddStringToObject(card2, "networkStatus", g_qrzl_device_dynamic_data.esim2_net_status ? "1" : "0");
        cJSON_AddStringToObject(card2, "csq", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim2_iccid) == 0 ? g_qrzl_device_dynamic_data.rssi : "");
        cJSON_AddBoolToObject(card2, "defUsed", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim2_iccid) == 0);
        cJSON_AddItemToArray(seedCard_array, card2);
    }
    
    // 添加卡3信息 (物理卡)
    if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) > 0) {
        cJSON *card3 = cJSON_CreateObject();
        cJSON_AddStringToObject(card3, "iccid", g_qrzl_device_static_data.nvro_esim3_iccid);
        cJSON_AddStringToObject(card3, "imsi", g_qrzl_device_static_data.nvro_esim3_imsi);
        cJSON_AddStringToObject(card3, "sequence", "3");
        cJSON_AddStringToObject(card3, "quickNetStatus", g_qrzl_device_dynamic_data.esim3_net_status ? "1" : "0");
        cJSON_AddStringToObject(card3, "networkStatus", g_qrzl_device_dynamic_data.esim3_net_status ? "1" : "0");
        cJSON_AddStringToObject(card3, "csq", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim3_iccid) == 0 ? g_qrzl_device_dynamic_data.rssi : "");
        cJSON_AddBoolToObject(card3, "defUsed", strcmp(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.nvro_esim3_iccid) == 0);
        cJSON_AddItemToArray(seedCard_array, card3);
    }

    char *post_data = cJSON_PrintUnformatted(root);
    if (post_data == NULL) {
        LOG_E(LOG_TAG, "Failed to print cJSON to string.");
        cJSON_Delete(root);
        return -1;
    }

    LOG_D(LOG_TAG, "Device info upload request data: %s", post_data);
    if (ito_cloud_http_request_with_retry(UPLOAD_DEVICE_INFO_PATH, g_cloud_config.http_ip,
                                                 g_cloud_config.http_port, post_data, NULL, 0, interface) != 0) {
        LOG_E(LOG_TAG, "Failed to upload device info");
        free(post_data);
        cJSON_Delete(root);
        return -1;
    }

    free(post_data);
    cJSON_Delete(root);
    return 0;
}

// 将SIM卡文件格式的IMSI转换为真实IMSI
static void convert_sim_imsi_to_real(const char* sim_imsi, char* real_imsi, size_t size) {
    if (!sim_imsi || !real_imsi || size < 16) return;
    int len = strlen(sim_imsi);
    if (len < 4) {  // 至少需要4字符（一个完整字节）
        strncpy(real_imsi, sim_imsi, size - 1);
        real_imsi[size - 1] = '\0';
        return;
    }

    // 根据实际测试案例重新实现
    // 输入: 084906113317297028
    // 期望: 460113371920782
    //
    // 让我们直接按照期望的映射来实现：
    // 跳过前两个字符（08），然后按照特定规律转换

    int real_idx = 0;

    // 跳过长度字节（前两个字符）
    const char* data_start = sim_imsi + 2;
    int data_len = len - 2;

    // 处理IMSI的特殊格式
    // 根据实际测试结果：946004941401512 -> 46004941401512
    // 需要去掉第一个9，保留最后一位
    //
    // IMSI存储格式分析：
    // 第一个数据字节(49)：高4位(4)可能是奇偶标志，低4位(9)需要跳过
    // 后续字节：正常的BCD编码，低4位在前，高4位在后

    if (data_len >= 1) {
        int i;
        for (i = 0; i < data_len && real_idx < size - 1; i += 2) {
            char high_char = data_start[i];
            char low_char = (i + 1 < data_len) ? data_start[i + 1] : 'F';

            if (i == 0) {
                // 第一个字节特殊处理：只输出高4位，跳过低4位
                if (high_char != 'F' && high_char != 'f' && real_idx < size - 1) {
                    real_imsi[real_idx++] = high_char;
                }
                // 跳过第一个字节的低4位(9)
            } else {
                // 后续字节：正常BCD解码，先低4位再高4位
                if (low_char != 'F' && low_char != 'f' && real_idx < size - 1) {
                    real_imsi[real_idx++] = low_char;
                }
                if (high_char != 'F' && high_char != 'f' && real_idx < size - 1) {
                    real_imsi[real_idx++] = high_char;
                }
            }

            // 如果是最后一个字符且没有配对，单独处理
            if (i + 1 >= data_len && i > 0) {
                // 最后一个单独的字符，直接输出
                if (high_char != 'F' && high_char != 'f' && real_idx < size - 1) {
                    real_imsi[real_idx++] = high_char;
                }
            }
        }
    }

    real_imsi[real_idx] = '\0';
}

// 将SIM卡文件格式的ICCID转换为真实ICCID
static void convert_sim_iccid_to_real(const char* sim_iccid, char* real_iccid, size_t size) {
    if (!sim_iccid || !real_iccid || size < 21) {
        return;
    }

    // SIM卡文件格式的ICCID也是BCD编码，需要转换
    // 例如: "9868404791520C200050" -> "89864047915202000050"

    int len = strlen(sim_iccid);
    if (len < 20) {
        // 如果长度不够，直接复制
        strncpy(real_iccid, sim_iccid, size - 1);
        real_iccid[size - 1] = '\0';
        return;
    }

    // BCD解码：交换每对数字的位置
    int real_idx = 0;
    int i;

    for (i = 0; i < len && real_idx < size - 1; i += 2) {
        if (i + 1 < len) {
            // 交换每对BCD数字
            real_iccid[real_idx++] = sim_iccid[i + 1];
            if (real_idx < size - 1) {
                real_iccid[real_idx++] = sim_iccid[i];
            }
        }
    }

    real_iccid[real_idx] = '\0';

    // 移除末尾的填充F和C
    int real_len = strlen(real_iccid);
    while (real_len > 0 && (real_iccid[real_len - 1] == 'F' ||
                           real_iccid[real_len - 1] == 'f' ||
                           real_iccid[real_len - 1] == 'C' ||
                           real_iccid[real_len - 1] == 'c')) {
        real_iccid[--real_len] = '\0';
    }
}

static int ito_cloud_login_get_card(const char *interface)
{
    char post_data[2048];
    char response_buffer[2048] = {0};

    // 确保静态数据已初始化
    ensure_static_data_initialized();

    // 更新动态数据以获取最新信息
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_LOGIN);

    // 根据vsim.log构建拿卡请求数据，使用真实设备数据
    const char* version_code = get_version_code(g_qrzl_device_static_data.soft_version);

    // 构建GSM网络信息字符串（使用动态数据）
    char gsm_info[128] = {0};
    snprintf(gsm_info, sizeof(gsm_info), "%s,%s,%s,%s,%s",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mnc,
             g_qrzl_device_dynamic_data.lac,
             g_qrzl_device_dynamic_data.cid,
             g_qrzl_device_dynamic_data.rssi);

    // 添加virtual_card_*参数（根据协议文档5.1.5节）
    snprintf(post_data, sizeof(post_data),
             "{\"sn\":\"%s\",\"androidId\":\"\",\"boardName\":\"MZ803\",\"device_con_num\":%d,\"deviceBrand\":\"ZXIC\",\"electric\":\"%s\",\"packageName\":\"\",\"getSimNum\":1,\"ssid\":\"%s\",\"wifipwd\":\"%s\",\"systemVersion\":\"%s\",\"versionCode\":\"%s\",\"versionName\":\"%s\",\"wifiMac\":\"%s\",\"seed_card_iccid\":\"%s\",\"seed_card_imei\":\"%s\",\"seed_card_imsi\":\"%s\",\"virtual_card_imei\":\"\",\"virtual_card_imsi\":\"\",\"virtual_card_iccid\":\"\",\"phoneType\":\"GSM\",\"cdma\":\"\",\"gsm\":\"%s\"}",
             g_qrzl_device_static_data.sn,
             g_qrzl_device_dynamic_data.conn_num,
             g_qrzl_device_dynamic_data.remain_power,
             g_qrzl_device_dynamic_data.wifi_ssid,
             g_qrzl_device_dynamic_data.wifi_key,
             g_qrzl_device_static_data.soft_version,
             version_code,
             g_qrzl_device_static_data.soft_version,
             g_qrzl_device_static_data.mac,
             g_qrzl_device_dynamic_data.iccid,
             g_qrzl_device_static_data.imei,
             g_qrzl_device_dynamic_data.imsi,
             gsm_info);

    // LOG_D(LOG_TAG, "使用真实设备数据发送登录请求:");
    // LOG_D(LOG_TAG, "  SN: %s", g_qrzl_device_static_data.sn);
    // LOG_D(LOG_TAG, "  IMEI: %s", g_qrzl_device_static_data.imei);
    // LOG_D(LOG_TAG, "  MAC: %s", g_qrzl_device_static_data.mac);
    // LOG_D(LOG_TAG, "  ICCID: %s", g_qrzl_device_dynamic_data.iccid);
    // LOG_D(LOG_TAG, "  IMSI: %s", g_qrzl_device_dynamic_data.imsi);
    // LOG_D(LOG_TAG, "  电量: %s%%", g_qrzl_device_dynamic_data.remain_power);
    // LOG_D(LOG_TAG, "  连接数: %d", g_qrzl_device_dynamic_data.conn_num);

    LOG_D(LOG_TAG, "Login request data: %s", post_data);
    int http_result = ito_cloud_http_request_with_retry(LOGIN_PATH, g_cloud_config.http_ip,
                                                       g_cloud_config.http_port, post_data, response_buffer, sizeof(response_buffer), interface);
    if (http_result != ITO_CLOUD_SUCCESS) {
        // HTTP函数已经返回了具体的错误码，直接传递
        LOG_E(LOG_TAG, "Failed to login and get card - error code: %d", http_result);
        return http_result; // 直接返回HTTP函数的错误码
    }

    // 解析响应，获取APDU响应数据
    if (strlen(response_buffer) > 0) {
        // Log login response with encoding conversion
        LOG_D(LOG_TAG, "login_get_card response: %s", response_buffer);


        cJSON *response_json = cJSON_Parse(response_buffer);
        if (response_json) {
            cJSON *success_field = cJSON_GetObjectItem(response_json, "success");
            if (success_field && success_field->type == cJSON_True) {
                cJSON *data = cJSON_GetObjectItem(response_json, "data");
                if (data) {
                    cJSON *imei = cJSON_GetObjectItem(data, "imei");
                    if (imei && imei->valuestring) {
                        LOG_D(LOG_TAG, "云卡 IMEI: %s", imei->valuestring);
                        snprintf(cloud_card_info.imei, sizeof(cloud_card_info.imei), "%s", imei->valuestring);

                    }

                    cJSON *iccid = cJSON_GetObjectItem(data, "iccid");
                    if (iccid && iccid->valuestring) {
                        char real_iccid[21] = {0};
                        snprintf(cloud_card_info.iccid, sizeof(cloud_card_info.iccid), "%s", iccid->valuestring);
                        convert_sim_iccid_to_real(iccid->valuestring, real_iccid, sizeof(real_iccid));
                        snprintf(cloud_card_info.real_iccid, sizeof(cloud_card_info.real_iccid), "%s", real_iccid);
                        LOG_D(LOG_TAG, "云卡 ICCID: 原始=%s, 转换后=%s", iccid->valuestring, real_iccid);
                    }

                    cJSON *imsi = cJSON_GetObjectItem(data, "imsi");
                    if (imsi && imsi->valuestring) {
                        char real_imsi[16] = {0};
                        snprintf(cloud_card_info.imsi, sizeof(cloud_card_info.imsi), "%s", imsi->valuestring);
                        convert_sim_imsi_to_real(imsi->valuestring, real_imsi, sizeof(real_imsi));
                        snprintf(cloud_card_info.real_imsi, sizeof(cloud_card_info.real_imsi), "%s", real_imsi);
                        LOG_D(LOG_TAG, "云卡 IMSI: 原始=%s, 转换后=%s", imsi->valuestring, real_imsi);
                    }

                    cJSON *apn = cJSON_GetObjectItem(data, "apn");
                    if (apn && apn->valuestring) {
                        snprintf(cloud_card_info.apn, sizeof(cloud_card_info.apn), "%s", apn->valuestring);
                        LOG_D(LOG_TAG, "云卡 APN: %s", apn->valuestring);
                    }

                    cJSON *ispId = cJSON_GetObjectItem(data, "ispId");
                    if (ispId && ispId->type == cJSON_Number) {
                        cloud_card_info.ispId = ispId->valueint;
                    }

                    cJSON *serialNum = cJSON_GetObjectItem(data, "serialNum");
                    if (serialNum && serialNum->valuestring) {
                        snprintf(cloud_card_info.serialNum, sizeof(cloud_card_info.serialNum), "%s", serialNum->valuestring);
                        LOG_D(LOG_TAG, "云卡 serialNum: %s", serialNum->valuestring);
                    }

                    // 标记云卡信息有效
                    cloud_card_info.is_valid = 1;

                } else {
                    LOG_E(LOG_TAG, "No data field in response");
                    cJSON_Delete(response_json);
                    return ITO_CLOUD_ERROR_PARSE; // 解析错误
                }
            } else {
                cJSON *error_code = cJSON_GetObjectItem(response_json, "errorCode");
                cJSON *error_msg = cJSON_GetObjectItem(response_json, "errorMessage");
                LOG_E(LOG_TAG, "login_get_card failed - errorCode: %s, errorMessage: %s",
                      error_code ? error_code->valuestring : "unknown",
                      error_msg ? error_msg->valuestring : "unknown");
                cJSON_Delete(response_json);
                return ITO_CLOUD_ERROR_AUTH; // 认证错误（SN不合法等）
            }
            cJSON_Delete(response_json);
        } else {
            LOG_E(LOG_TAG, "Failed to parse response JSON");
            return ITO_CLOUD_ERROR_PARSE; // 解析错误
        }
    } else {
        LOG_E(LOG_TAG, "Empty response from server");
        return ITO_CLOUD_ERROR_NETWORK; // 网络错误（空响应）
    }

    return ITO_CLOUD_SUCCESS; // 成功
}

int ito_cloud_auth_sync(char *apdu_auth, int len, char *apdu_rsp, int rsp_buf_size)
{
    char post_data[1024];
    char response_buffer[2048] = {0};

    // 根据协议文档构建鉴权请求数据
    if(len == 0) {
        LOG_E(LOG_TAG, "apdu_auth is null");
        return -1;
    }

    if(apdu_rsp == NULL || rsp_buf_size <= 0) {
        LOG_E(LOG_TAG, "apdu_rsp buffer is invalid");
        return -1;
    }

    // 确保种子卡wan接口名称已初始化
    init_seed_wan_name();

    // 确保静态数据已初始化
    ensure_static_data_initialized();

    // 更新动态数据以获取最新的ICCID和IMSI
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_AUTH_SYNC);

    // 使用真实的设备数据构建请求
    // 添加serialNum参数（根据协议文档5.1.6节）
    char count_str[5];
    g_auth_count++;
    snprintf(count_str, sizeof(count_str), "%04X", g_auth_count);

    snprintf(post_data, sizeof(post_data),
             "{\"sn\":\"%s\",\"apdu_req\":\"%s\",\"apdu_le\":%d,\"count\":\"%s\",\"iccid\":\"%s\",\"imsi\":\"%s\"}",
             g_qrzl_device_static_data.sn,
             apdu_auth,
             len / 2,  // 动态计算apdu_le
             count_str, // 使用递增的计数器
             cloud_card_info.iccid,
             cloud_card_info.imsi); 

    LOG_D(LOG_TAG, "使用真实设备数据发送鉴权请求:");
    LOG_D(LOG_TAG, "  SN: %s", g_qrzl_device_static_data.sn);
    LOG_D(LOG_TAG, "  云卡 ICCID: %s", cloud_card_info.iccid);
    LOG_D(LOG_TAG, "  云卡 IMSI: %s", cloud_card_info.imsi);

    LOG_D(LOG_TAG, "Auth sync request data: %s", post_data);
    // 鉴权请求需要绑定种子卡网卡
    const char* wan_interface = get_seed_wan_name();
    LOG_I(LOG_TAG, "鉴权请求绑定种子卡网卡: %s", wan_interface);
    if (ito_cloud_http_request_with_retry(AUTH_SYNC_PATH, g_cloud_config.http_ip,
                                         g_cloud_config.http_port, post_data,
                                         response_buffer, sizeof(response_buffer), wan_interface) != 0) {
        LOG_E(LOG_TAG, "Failed to auth sync");
        return -1;
    }

    // 解析响应，获取APDU响应数据
    if (strlen(response_buffer) > 0) {
        // Log auth sync response with encoding conversion
        LOG_D(LOG_TAG, "Auth sync response: %s", response_buffer);

        cJSON *response_json = cJSON_Parse(response_buffer);
        if (response_json) {
            cJSON *success_field = cJSON_GetObjectItem(response_json, "success");
            if (success_field && success_field->type == cJSON_True) {
                cJSON *data = cJSON_GetObjectItem(response_json, "data");
                if (data) {
                    cJSON *apdu_field = cJSON_GetObjectItem(data, "apdu");
                    if (apdu_field && apdu_field->valuestring) {
                        // 复制APDU响应数据
                        int apdu_len = strlen(apdu_field->valuestring);
                        if (apdu_len < rsp_buf_size) {
                            strcpy(apdu_rsp, apdu_field->valuestring);
                            LOG_D(LOG_TAG, "Got APDU response: %s", apdu_rsp);
                            cJSON_Delete(response_json);
                            return 0;
                        } else {
                            LOG_E(LOG_TAG, "APDU response too long: %d >= %d", apdu_len, rsp_buf_size);
                        }
                    } else {
                        LOG_E(LOG_TAG, "No apdu field in response data");
                    }
                } else {
                    LOG_E(LOG_TAG, "No data field in response");
                }
            } else {
                cJSON *error_code = cJSON_GetObjectItem(response_json, "errorCode");
                cJSON *error_msg = cJSON_GetObjectItem(response_json, "errorMessage");
                LOG_E(LOG_TAG, "Auth sync failed - errorCode: %s, errorMessage: %s",
                      error_code ? error_code->valuestring : "unknown",
                      error_msg ? error_msg->valuestring : "unknown");
            }
            cJSON_Delete(response_json);
        } else {
            LOG_E(LOG_TAG, "Failed to parse response JSON");
        }
    } else {
        LOG_E(LOG_TAG, "Empty response from server");
    }

    return -1;
}

static int ito_cloud_progress_upload(const char *interface)
{
    char post_data[2048];

    // 确保静态数据已初始化
    ensure_static_data_initialized();

    // 更新动态数据以获取最新的ICCID和IMSI
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_PROGRESS_UPLOAD);

    // 获取云卡信息
    extern CloudCardInfo cloud_card_info;
    const char* serialNum = (cloud_card_info.is_valid && strlen(cloud_card_info.serialNum) > 0) ?
                           cloud_card_info.serialNum : "";
    const char* vsim_iccid = (cloud_card_info.is_valid && strlen(cloud_card_info.real_iccid) > 0) ?
                            cloud_card_info.real_iccid : "";

    // 参考实际数据格式构造JSON数组
    time_t now = time(NULL);
    long long start_time_ms = (now - 10) * 1000LL; // 10秒前开始，转换为毫秒
    long long end_time_ms = now * 1000LL;          // 现在结束，转换为毫秒
    int duration_ms = 10000;                       // 持续时间10秒

    snprintf(post_data, sizeof(post_data),
             "["
             "{\"sn\":\"%s\",\"serialNum\":\"%s\","
             "\"progressId\":3,\"startTime\":\"%lld\",\"endTime\":\"%lld\","
             "\"duration\":%d,\"type\":3,\"success\":1,\"status\":0,"
             "\"msg\":\"switch to vsim success.\",\"iccid\":\"%s\"},"
             "{\"sn\":\"%s\",\"serialNum\":\"%s\","
             "\"progressId\":4,\"startTime\":\"%lld\",\"endTime\":\"%lld\","
             "\"duration\":%d,\"type\":0,\"success\":1,\"status\":0,"
             "\"msg\":\"network is ok.\",\"iccid\":\"%s\"}"
             "]",
             // 第一个进度项（切卡）
             g_qrzl_device_static_data.sn, serialNum,
             start_time_ms, start_time_ms + 5000, 5000, // 切卡耗时5秒
             strlen(vsim_iccid) > 0 ? vsim_iccid : g_qrzl_device_dynamic_data.iccid,
             // 第二个进度项（联网）
             g_qrzl_device_static_data.sn, serialNum,
             start_time_ms + 5000, end_time_ms, 5000, // 联网耗时5秒
             strlen(vsim_iccid) > 0 ? vsim_iccid : g_qrzl_device_dynamic_data.iccid);

    LOG_D(LOG_TAG, "进度上报数据:");
    LOG_D(LOG_TAG, "  SN: %s", g_qrzl_device_static_data.sn);
    LOG_D(LOG_TAG, "  serialNum: %s", serialNum);
    LOG_D(LOG_TAG, "  种子卡ICCID: %s", g_qrzl_device_dynamic_data.iccid);
    LOG_D(LOG_TAG, "  云卡ICCID: %s", vsim_iccid);

    LOG_D(LOG_TAG, "Progress upload request data: %s", post_data);
    if (ito_cloud_http_request_with_retry(PROGRESS_UPLOAD_PATH, g_cloud_config.http_ip,
                                         g_cloud_config.http_port, post_data, NULL, 0, interface) != 0) {
        LOG_E(LOG_TAG, "Failed to upload progress");
        return -1;
    }
    return 0;
}


/**
 * 处理服务器下发的响应消息
 * @param json JSON Body
 */
static void handler_device_state_report_msg(cJSON *json)
{   
    LOG_I(LOG_TAG, "\n\nbegin handler_device_state_report_msg...");

    // 初始化WIFI信息
    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    cJSON *json_data = cJSON_GetObjectItem(json, "data");
    if (json_data == NULL || json_data->type != cJSON_Object) {
        LOG_I(LOG_TAG, "handler report data is NULL or not Objact");
        return;
    }

    // isActive字段功能重叠，此处不做处理
    // cJSON *json_isActive = cJSON_GetObjectItem(json_data, "isActive");
    cJSON *json_openWifi = cJSON_GetObjectItem(json_data, "openWifi");
    if (json_openWifi != NULL && json_openWifi->type == cJSON_Number) {
        wifi_config.hide = json_openWifi->valueint;
        if (json_openWifi->valueint == 0) {
            set_network_br0_disconnect(1);
        } else if(json_openWifi->valueint == 1) {
            set_network_br0_disconnect(0);
        }
    } else {
        LOG_I(LOG_TAG, "openWifi is NULL or type error!");
    }

    cJSON *json_changeSimCard = cJSON_GetObjectItem(json_data, "changeSimCard");
    if (json_changeSimCard != NULL && json_changeSimCard->type == cJSON_Number) {
        // TODO 暂不知作用, 不做处理
    } else {
        LOG_I(LOG_TAG, "json_changeSimCard is NULL or type error!");
    }

    cJSON *json_wifiName = cJSON_GetObjectItem(json_data, "wifiName");
    if (json_wifiName != NULL && json_wifiName->type == cJSON_String) {
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", json_wifiName->valuestring);
    } else {
        LOG_I(LOG_TAG, "json_wifiName is NULL or type error!");
    }

    cJSON *json_wifiPwd = cJSON_GetObjectItem(json_data, "wifiPwd");
    if (json_wifiPwd != NULL && json_wifiPwd->type == cJSON_String) {
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", json_wifiPwd->valuestring);
    } else {
        LOG_I(LOG_TAG, "json_wifiPwd is NULL or type error!");
    }

    cJSON *json_hideWifi = cJSON_GetObjectItem(json_data, "hideWifi");
    if (json_hideWifi != NULL && json_hideWifi->type == cJSON_Number) {
        wifi_config.hide = json_hideWifi->valueint;
    } else {
        LOG_I(LOG_TAG, "json_hideWifi is NULL or type error!");
    }

    cJSON *json_wifiBand = cJSON_GetObjectItem(json_data, "wifiBand");
    if (json_wifiBand != NULL && json_wifiBand->type == cJSON_Number) {
        // TODO 待处理
    } else {
        LOG_I(LOG_TAG, "json_wifiBand is NULL or type error!");
    }

    cJSON *json_limitSpeed = cJSON_GetObjectItem(json_data, "limitSpeed");
    if (json_limitSpeed != NULL && json_limitSpeed->type == cJSON_Object) {
        // 限速值初始化
        uint64_t up_limit = 0L;
        uint64_t down_limit = 0L;
        // TODO SN 暂不知作用, 不做处理
        // cJSON *json_sn = cJSON_GetObjectItem(json_limitSpeed, "sn");
        cJSON *json_speedLimitSwitch = cJSON_GetObjectItem(json_limitSpeed, "speedLimitSwitch");
        if (json_speedLimitSwitch != NULL && json_speedLimitSwitch->type == cJSON_Number) {
            // 开启限速
            if (json_speedLimitSwitch->valueint == 1) {
                // 如果没走到这里，默认不限速(up_limit & down_limit为 0 不进行限速)
                // 获取限速值
                cJSON *json_speedLimitUpVal = cJSON_GetObjectItem(json_limitSpeed, "speedLimitUpVal");
                cJSON *json_speedLimitDownVal = cJSON_GetObjectItem(json_limitSpeed, "speedLimitDownVal");
                if (json_speedLimitUpVal && json_speedLimitDownVal && json_speedLimitUpVal->type == cJSON_Number && json_speedLimitDownVal->type == cJSON_Number) {
                    // 设置限速值
                    up_limit = json_speedLimitUpVal->valueint;
                    down_limit = json_speedLimitDownVal->valueint;
                }
            }
            // 执行限速脚本
            limit_net_speed(up_limit, down_limit);
        } else {
            LOG_I(LOG_TAG, "json_speedLimitSwitch is NULL or type error!");
        }
    } else {
        LOG_I(LOG_TAG, "json_limitSpeed is NULL or type error!");
    }

    cJSON *json_outCardOpen = cJSON_GetObjectItem(json_data, "outCardOpen");
    if (json_outCardOpen != NULL && json_outCardOpen->type == cJSON_Number) {
        // TODO 外卡开启状态 待处理
    } else {
        LOG_I(LOG_TAG, "json_outCardOpen is NULL or type error!");
    }

    cJSON *json_seedCard = cJSON_GetObjectItem(json_data, "seedCard");
    if (json_seedCard != NULL && cJSON_IsArray(json_seedCard)) {
        // 种子卡个数
        int seedCard_count = cJSON_GetArraySize(json_seedCard);
        if (seedCard_count == 0) {
            LOG_I(LOG_TAG, "    无种子卡信息");
        } else {
            int i;
            for (i = 0; i < seedCard_count; i++) {
                cJSON *card = cJSON_GetArrayItem(json_seedCard, i);
                if (card) {
                    cJSON *iccid = cJSON_GetObjectItem(card, "iccid");
                    cJSON *imsi = cJSON_GetObjectItem(card, "imsi");
                    cJSON *sequence = cJSON_GetObjectItem(card, "sequence");
                    cJSON *quickNetStatus = cJSON_GetObjectItem(card, "quickNetStatus");
                    cJSON *networkStatus = cJSON_GetObjectItem(card, "networkStatus");
                    cJSON *csq = cJSON_GetObjectItem(card, "csq");
                    cJSON *defUsed = cJSON_GetObjectItem(card, "defUsed");
                    cJSON *msg = cJSON_GetObjectItem(card, "msg");

                    // cJSON中没有封装布尔类型的判断 先使用-> (defUsed->type & 0xFF) == cJSON_False
                    if (defUsed != NULL && (defUsed->type & 0xFF) == cJSON_False) {
                        // 当前卡没有设为默认卡跳过此次循环
                        continue;
                    }

                    if (sequence && sequence->type == cJSON_Number) {
                        switch (sequence->valueint)
                        {
                        // esim1_net_status为卡是否可上网的标识，但不具备实时性
                        // 此标识初始化时认为全部卡都能上网，再切卡进行检测后才会初始真正的状态，如果卡又有变化就还是如前面所述，需要切卡重新检测。// TODO 后续考虑去掉这个
                        case 1:
                            if (strncmp(g_qrzl_device_static_data.nvro_esim1_iccid, iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim1_iccid)) == 0) {
                                if (g_qrzl_device_dynamic_data.esim1_net_status == 1)
                                {
                                    LOG_I(LOG_TAG, "esim1 可以上网，准备切卡esim1");
                                    switch_sim_card_not_restart(1);
                                }
                                else
                                {
                                    LOG_I(LOG_TAG, "esim1 不可以上网，不切卡");
                                }
                            }
                            break;
                        case 2:
                            if (strncmp(g_qrzl_device_static_data.nvro_esim2_iccid, iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim2_iccid)) == 0) {
                                if (g_qrzl_device_dynamic_data.esim2_net_status == 1)
                                {
                                    LOG_I(LOG_TAG, "esim2 可以上网，准备切卡esim1");
                                    switch_sim_card_not_restart(2);
                                }
                                else
                                {
                                    LOG_I(LOG_TAG, "esim2 不可以上网，不切卡");
                                }
                            }
                            break;
                        case 0:
                        case 3:
#ifdef QRZL_HAVE_3_ESIM_CARD
                            if (strncmp(g_qrzl_device_static_data.nvro_esim3_iccid, iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim3_iccid)) == 0) {
                                if (g_qrzl_device_dynamic_data.esim3_net_status == 1)
                                {
                                    LOG_I(LOG_TAG, "esim3 可以上网，准备切卡esim1");
                                    switch_sim_card_not_restart(0);
                                }
                                else
                                {
                                    LOG_I(LOG_TAG, "esim3 不可以上网，不切卡");
                                }
                            }
                            break;
#endif
                        default:
                            LOG_I(LOG_TAG, "错误的切卡序号....");
                            break;
                        }
                    }
                }
            }
        }
    } else {
        LOG_I(LOG_TAG, "json_seedCard is NULL or type error!");
    }

    // 本地卡网络配置
    cJSON *json_localCard = cJSON_GetObjectItem(json_data, "localCardNetWorkConfig");
    if (json_localCard != NULL && json_localCard->type == cJSON_Object){
        cJSON *json_isOpen = cJSON_GetObjectItem(json_localCard, "isOpen");
        cJSON *json_simId = cJSON_GetObjectItem(json_localCard, "simId");
        if (json_isOpen != NULL && json_isOpen->type == cJSON_Number && json_simId != NULL && json_simId->type == cJSON_Number){
            // TODO 作用不明，待处理
        } else {
            LOG_I(LOG_TAG, "json_isOpen & json_simId is NULL or type error!");
        }
    } else {
        LOG_I(LOG_TAG, "json_localCardNetWorkConfig is NULL or type error!");
    }


    cJSON *json_delayOffWifiTimes = cJSON_GetObjectItem(json_data, "delayOffWifiTimes");
    if (json_delayOffWifiTimes != NULL && json_delayOffWifiTimes->type == cJSON_Number) {
        // TODO 延迟关闭WIFI
    } else {
        LOG_I(LOG_TAG, "json_delayOffWifiTimes is NULL or type error!");
    }

    // 更新 WIFI 配置
    update_wifi_by_config(&wifi_config);

    return;
}

// ===================== 设备状态上报 =====================

static int ito_cloud_report_device_state(const char *interface)
{
    // 确保静态数据已初始化
    ensure_static_data_initialized();
    update_device_dynamic_data();

    // 确保所有必要数据不为空
    ensure_required_data_not_empty(REQUEST_TYPE_DEVICE_STATE);

    // 构造 GET 请求的查询参数
    char query_params[1024];
    snprintf(query_params, sizeof(query_params),
        "sn=%s&seedCardICCID=%s&phoneType=GSM&gsm=%s,%s,%s,%s,%s&cdma=&electric=%s",
        g_qrzl_device_static_data.sn,
        g_qrzl_device_dynamic_data.iccid,
        g_qrzl_device_dynamic_data.mcc,
        g_qrzl_device_dynamic_data.mnc,
        g_qrzl_device_dynamic_data.lac,
        g_qrzl_device_dynamic_data.cid,
        g_qrzl_device_dynamic_data.rssi,
        g_qrzl_device_dynamic_data.remain_power);

    LOG_D(LOG_TAG, "Device state report query params: %s", query_params);

    // 使用封装的 GET 请求函数获取响应
    char response_buffer[2048] = {0};
    if (ito_cloud_http_get_with_response(DEVICE_STATE_PATH, g_cloud_config.http_ip,
                                        g_cloud_config.http_port, query_params,
                                        response_buffer, sizeof(response_buffer), interface) != 0) {
        LOG_E(LOG_TAG, "Failed to report device state");
        return -1;
    }

    // 解析响应（实际格式：{"success":true,"errorCode":null,"errorMessage":null,"data":{...}}）
    cJSON *root = cJSON_Parse(response_buffer);
    if (!root) {
        LOG_E(LOG_TAG, "Failed to parse device state response JSON");
        return -1;
    }

    cJSON *success = cJSON_GetObjectItem(root, "success");
    if (!success || success->type != cJSON_True) {
        cJSON *errorCode = cJSON_GetObjectItem(root, "errorCode");
        cJSON *errorMessage = cJSON_GetObjectItem(root, "errorMessage");
        LOG_E(LOG_TAG, "Device state report failed: errorCode=%s, errorMessage=%s",
              errorCode && errorCode->valuestring ? errorCode->valuestring : "null",
              errorMessage && errorMessage->valuestring ? errorMessage->valuestring : "null");
        cJSON_Delete(root);
        return -1;
    }

    // 获取 data 对象
    cJSON *data = cJSON_GetObjectItem(root, "data");
    if (!data) {
        LOG_E(LOG_TAG, "Device state response missing data field");
        cJSON_Delete(root);
        return -1;
    }

    // 解析设备状态信息（从 data 对象中获取）
    cJSON *sn = cJSON_GetObjectItem(data, "sn");
    cJSON *isActive = cJSON_GetObjectItem(data, "isActive");
    cJSON *openWifi = cJSON_GetObjectItem(data, "openWifi");
    cJSON *openQuickNet = cJSON_GetObjectItem(data, "openQuickNet");
    cJSON *changeSimCard = cJSON_GetObjectItem(data, "changeSimCard");
    cJSON *wifiName = cJSON_GetObjectItem(data, "wifiName");
    cJSON *wifiPwd = cJSON_GetObjectItem(data, "wifiPwd");
    cJSON *delayOffWifiTimes = cJSON_GetObjectItem(data, "delayOffWifiTimes");
    cJSON *hideWifi = cJSON_GetObjectItem(data, "hideWifi");
    cJSON *wifiBand = cJSON_GetObjectItem(data, "wifiBand");
    cJSON *outCardOpen = cJSON_GetObjectItem(data, "outCardOpen");

    LOG_I(LOG_TAG, "设备状态上报成功:");
    if (sn && sn->valuestring) LOG_I(LOG_TAG, "  设备SN: %s", sn->valuestring);
    if (isActive) LOG_I(LOG_TAG, "  设备激活状态: %d", isActive->valueint);
    if (openWifi) LOG_I(LOG_TAG, "  WiFi开启状态: %d", openWifi->valueint);
    if (openQuickNet) LOG_I(LOG_TAG, "  极速上网状态: %d", openQuickNet->valueint);
    if (changeSimCard) LOG_I(LOG_TAG, "  切卡状态: %d", changeSimCard->valueint);
    if (wifiName && wifiName->valuestring) LOG_I(LOG_TAG, "  WiFi名称: %s", wifiName->valuestring);
    if (wifiPwd && wifiPwd->valuestring) LOG_I(LOG_TAG, "  WiFi密码: %s", wifiPwd->valuestring);
    if (delayOffWifiTimes) LOG_I(LOG_TAG, "  WiFi延迟关闭时间: %d秒", delayOffWifiTimes->valueint);
    if (hideWifi) LOG_I(LOG_TAG, "  WiFi隐藏状态: %d", hideWifi->valueint);
    if (wifiBand) LOG_I(LOG_TAG, "  WiFi频段: %d", wifiBand->valueint);
    if (outCardOpen) LOG_I(LOG_TAG, "  外卡开启状态: %d", outCardOpen->valueint);

    // 解析限速配置
    cJSON *limitSpeed = cJSON_GetObjectItem(data, "limitSpeed");
    if (limitSpeed) {
        cJSON *speedLimitSwitch = cJSON_GetObjectItem(limitSpeed, "speedLimitSwitch");
        cJSON *speedLimitUpVal = cJSON_GetObjectItem(limitSpeed, "speedLimitUpVal");
        cJSON *speedLimitDownVal = cJSON_GetObjectItem(limitSpeed, "speedLimitDownVal");

        LOG_I(LOG_TAG, "  限速配置:");
        if (speedLimitSwitch) LOG_I(LOG_TAG, "    限速开关: %d", speedLimitSwitch->valueint);
        if (speedLimitUpVal) LOG_I(LOG_TAG, "    上行限速: %d", speedLimitUpVal->valueint);
        if (speedLimitDownVal) LOG_I(LOG_TAG, "    下行限速: %d", speedLimitDownVal->valueint);
    }

    // 解析本地卡网络配置
    cJSON *localCardNetWorkConfig = cJSON_GetObjectItem(data, "localCardNetWorkConfig");
    if (localCardNetWorkConfig) {
        cJSON *isOpen = cJSON_GetObjectItem(localCardNetWorkConfig, "isOpen");
        cJSON *simId = cJSON_GetObjectItem(localCardNetWorkConfig, "simId");

        LOG_I(LOG_TAG, "  本地卡网络配置:");
        if (isOpen) LOG_I(LOG_TAG, "    开启状态: %d", isOpen->valueint);
        if (simId) LOG_I(LOG_TAG, "    SIM卡ID: %d", simId->valueint);
    }

    // 解析种子卡信息（seedCard 是数组）
    cJSON *seedCard = cJSON_GetObjectItem(data, "seedCard");
    if (seedCard && cJSON_IsArray(seedCard)) {
        int seedCardCount = cJSON_GetArraySize(seedCard);
        LOG_I(LOG_TAG, "  种子卡信息 (共%d张卡):", seedCardCount);

        if (seedCardCount == 0) {
            LOG_I(LOG_TAG, "    无种子卡信息");
        } else {
            int i;
            for (i = 0; i < seedCardCount; i++) {
                cJSON *card = cJSON_GetArrayItem(seedCard, i);
                if (card) {
                    cJSON *iccid = cJSON_GetObjectItem(card, "iccid");
                    cJSON *imsi = cJSON_GetObjectItem(card, "imsi");
                    cJSON *sequence = cJSON_GetObjectItem(card, "sequence");
                    cJSON *quickNetStatus = cJSON_GetObjectItem(card, "quickNetStatus");
                    cJSON *networkStatus = cJSON_GetObjectItem(card, "networkStatus");
                    cJSON *csq = cJSON_GetObjectItem(card, "csq");
                    cJSON *defUsed = cJSON_GetObjectItem(card, "defUsed");
                    cJSON *msg = cJSON_GetObjectItem(card, "msg");

                    LOG_I(LOG_TAG, "    卡片[%d]:", i + 1);
                    if (iccid && iccid->valuestring) LOG_I(LOG_TAG, "      ICCID: %s", iccid->valuestring);
                    if (imsi && imsi->valuestring) LOG_I(LOG_TAG, "      IMSI: %s", imsi->valuestring);
                    if (sequence) LOG_I(LOG_TAG, "      卡序号: %d", sequence->valueint);
                    if (quickNetStatus) LOG_I(LOG_TAG, "      极速上网支持: %d", quickNetStatus->valueint);
                    if (networkStatus) LOG_I(LOG_TAG, "      联网状态: %d", networkStatus->valueint);
                    if (csq) LOG_I(LOG_TAG, "      信号值: %d", csq->valueint);
                    if (defUsed) LOG_I(LOG_TAG, "      默认使用: %s", defUsed->type == cJSON_True ? "是" : "否");
                    if (msg && msg->valuestring) LOG_I(LOG_TAG, "      消息: %s", msg->valuestring);
                }
            }
        }
    }

    // 处理服务器下发的响应消息
    handler_device_state_report_msg(root);

    cJSON_Delete(root);
    return 0;
}

// ===================== 异常上报功能 =====================
int ito_cloud_report_error(const char *error_code, const char *error_message)
{
    char post_data[512];
    
    snprintf(post_data, sizeof(post_data),
             "sn=%s&errorCode=%s&errorMessage=%s",
             g_qrzl_device_static_data.sn,
             error_code,
             error_message ? error_message : "Unknown error");

    LOG_D(LOG_TAG, "Error report request data: %s", post_data);
    if (ito_cloud_http_request_with_retry_compat(ERROR_UPLOAD_PATH, g_cloud_config.http_ip,
                                                 g_cloud_config.http_port, post_data, NULL, 0) != 0) {
        LOG_E(LOG_TAG, "Failed to report error");
        return -1;
    }
    return 0;
}

// ===================== TCP连接管理 =====================

static int ito_cloud_tcp_connect(const char *ip, int port)
{
    int sockfd;
    struct sockaddr_in serv_addr;

    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        LOG_E(LOG_TAG, "TCP ERROR opening socket: %s", strerror(errno));
        return -1;
    }

    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(port);

    if (inet_pton(AF_INET, ip, &serv_addr.sin_addr) <= 0) {
        LOG_E(LOG_TAG, "Invalid address/ Address not supported: %s", ip);
        close(sockfd);
        return -1;
    }

    // 设置超时
    struct timeval timeout;
    timeout.tv_sec = 10;
    timeout.tv_usec = 0;
    setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
    setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));

    if (connect(sockfd, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        LOG_E(LOG_TAG, "TCP ERROR connecting to %s:%d: %s", ip, port, strerror(errno));
        close(sockfd);
        return -1;
    }

    LOG_I(LOG_TAG, "TCP connected to %s:%d", ip, port);
    return sockfd;
}

// ===================== 线程实现 =====================

/**
 * @brief 检查云卡是否成功注册到网络
 * @return 1 表示成功注册, 0 表示未注册或查询失败
 */
/**
 * @brief 启动VSIM相关服务（如果尚未启动）
 */
static void start_vsim_services_if_needed(void)
{
    if (!g_vsim_started && cloud_card_info.is_valid) {
        sc_cfg_set("cloud_imei", cloud_card_info.imei);
        sc_cfg_set("cloud_iccid", cloud_card_info.real_iccid);
        sc_cfg_set("vsim_remote_file_cfg", "1");
        sc_cfg_set("cloud_imsi", cloud_card_info.real_imsi);
        sc_cfg_set("cloud_apn", cloud_card_info.apn);

        char ispId_str[16];
        snprintf(ispId_str, sizeof(ispId_str), "%d", cloud_card_info.ispId);
        sc_cfg_set("cloud_ispId", ispId_str);
        sc_cfg_save();

        // 启动服务
#ifdef QRZL_CLOUD_VSIM_AUTH
        extern void start_vsim_thread(void *arg);
        start_vsim_thread(&cloud_card_info);
        LOG_I(LOG_TAG, "已启动 VSIM 线程，传入云卡信息");
#elif defined(QRZL_CLOUD_VSIM_SOFTSIM_MODE)
        int ret = system("/bin/uicc_agt_svr &");
        if (ret == 0) {
            LOG_D(LOG_TAG, "成功启动 uicc_agt_svr 进程");
            sleep(2);
            // 调用vsim_init进行初始化
            extern int vsim_init(CloudCardInfo *card_info);
            if (vsim_init(&cloud_card_info) == 0) {
                LOG_I(LOG_TAG, "VSIM初始化成功");
                extern void start_vsim_thread(void *arg);
                start_vsim_thread(&cloud_card_info);
                LOG_I(LOG_TAG, "已启动 VSIM 线程，传入云卡信息");
            } else {
                LOG_E(LOG_TAG, "VSIM初始化失败");
                return;
            }
        } else {
            LOG_D(LOG_TAG, "启动 uicc_agt_svr 进程失败，返回码: %d", ret);
            return;
        }
#else
        // 启动 atVsimInit 不需要设置 imei 在线程内部已经设置了
        int ret = zte_SetIMEIbySimId((uint8_t*)cloud_card_info.imei, strlen(cloud_card_info.imei), 1);
        if (ret == 0) {
            LOG_I(LOG_TAG, "成功设置 SIM1 IMEI: %s", cloud_card_info.imei);
        } else {
            LOG_E(LOG_TAG, "设置 SIM1 IMEI 失败，错误码: %d", ret);
        }

        char at_cmd[128] = {0};
        snprintf(at_cmd, sizeof(at_cmd), "AT+CGDCONT=5,\"IP\",\"%s\"\r\n", cloud_card_info.apn);
        LOG_D(LOG_TAG, "准备发送APN设置AT命令: %s", at_cmd);
        ret = zte_SendATbySimId(at_cmd, NULL, NULL, 1);
        if (ret == 0) {
            LOG_I(LOG_TAG, "成功设置 SIM1 APN (PDP Context 5): %s", cloud_card_info.apn);
        } else {
            LOG_E(LOG_TAG, "设置 SIM1 APN 失败，错误码: %d", ret);
        }

        // 应用配置
        LOG_I(LOG_TAG, "应用云卡配置...");
        ret = system("/bin/uicc_agt_svr &");
        if (ret == 0) {
            LOG_D(LOG_TAG, "成功启动 uicc_agt_svr 进程");
            sleep(2);
        } else {
            LOG_D(LOG_TAG, "启动 uicc_agt_svr 进程失败，返回码: %d", ret);
        }
#endif
        g_vsim_started = 1;
    }
}

static int check_cloud_sim_registration(void)
{
    // 首先检查网络附着事件标记
    if (g_network_attached) {
        LOG_I(LOG_TAG, "云卡网络已附着 (事件标记)");
    #ifdef DSDS_VSIM
        // 平台应用 不需要这个nv
        // LOG_I(LOG_TAG, "通知平台应用 云卡鉴权完成开始拨号");
        // sc_cfg_set("dial_mode", "auto_dial");
        // sc_cfg_save();
    #endif
        
        // 还原鉴权标志 方便下次鉴权识别
        g_network_attached = 0;
        return 1;
    }

    // 检查网络接口wan5和wan6是否分配了IP
    char *interfaces[] = {"wan5", "wan6"};
    int num_interfaces = sizeof(interfaces) / sizeof(interfaces[0]);
    FILE *fp;
    char command[64];
    char output[128];
    int i;

    for (i = 0; i < num_interfaces; i++) {
        snprintf(command, sizeof(command), "ifconfig %s", interfaces[i]);
        fp = popen(command, "r");
        if (fp == NULL) {
            LOG_E(LOG_TAG, "执行 ifconfig 失败: %s", strerror(errno));
            continue;
        }

        while (fgets(output, sizeof(output), fp) != NULL) {
            if (strstr(output, "inet addr:") != NULL) {
                pclose(fp);
                LOG_I(LOG_TAG, "云卡网络已注册 (接口 %s 有IP)", interfaces[i]);
                return 1;
            }
        }
        pclose(fp);
    }

#if 0
    // 如果事件标记和接口检查都失败，则使用AT+COPS?查询
    char cops_rsp[128] = {0};
    char raw_rsp[256] = {0};
    void *p[] = {cops_rsp};
    int ret;

    ret = zte_SendATbySimId("AT+COPS?\r", "+COPS: %*d,%*d,\"%[^\"]\"", (void**)p, 1);
    if (ret == 0 && strlen(cops_rsp) > 0) {
        LOG_I(LOG_TAG, "云卡网络已注册，运营商: %s", cops_rsp);
        return 1;
    }
    
    // 尝试获取原始响应
    void *p_raw[] = {raw_rsp};
    ret = zte_SendATbySimId("AT+COPS?\r", "%s", (void**)p_raw, 1);
    if (ret == 0 && strstr(raw_rsp, "+COPS:") != NULL) {
        int comma_count = 0;
        char *temp = raw_rsp;
        while((temp = strchr(temp, ',')) != NULL) {
            comma_count++;
            temp++;
        }
        if (comma_count > 1) {
            LOG_I(LOG_TAG, "云卡网络已注册 (raw response): %s", raw_rsp);
            return 1;
        }
    }

    LOG_W(LOG_TAG, "云卡网络未注册或查询失败");
#endif
    return 0;
}

#ifdef DSDS_VSIM
static int check_cloud_sim_dial_success(void)
{
    if(1 == g_cloud_dial_status) {
        return 1;
    }
    return 0;
}
#endif

/**
 * @brief 检查云卡是否掉线
 * @return 1 表示掉线, 0 表示在线
 */
static int check_cloud_sim_offline_status(void)
{
#ifdef DSDS_VSIM
    if(0 == g_cloud_dial_status) {
        LOG_W(LOG_TAG, "检测到收到平台应用去激活消息，判断为云卡掉线。");
        g_reauth_started = 0; // 重置标志
        return 1;
    }
#endif
    
    // 1. 检查是否重新触发了鉴权
    if (g_reauth_started) {
        LOG_W(LOG_TAG, "检测到重新鉴权流程，判断为云卡掉线。");
        g_reauth_started = 0; // 重置标志
        return 1;
    }

    // 2. 检查wan6接口是否还有IP地址
    FILE *fp;
    char command[64];
    char output[128];

    snprintf(command, sizeof(command), "ifconfig wan6");
    fp = popen(command, "r");
    if (fp == NULL) {
        LOG_E(LOG_TAG, "执行 ifconfig wan6 失败: %s", strerror(errno));
        return 0; // 无法确定状态，暂时认为在线
    }

    int has_ip = 0;
    while (fgets(output, sizeof(output), fp) != NULL) {
        if (strstr(output, "inet addr:") != NULL) {
            has_ip = 1;
            break;
        }
    }
    pclose(fp);

    if (!has_ip) {
        LOG_W(LOG_TAG, "检测到wan6接口无IP地址，判断为云卡掉线。");
        return 1;
    }

    return 0; // 在线
}

/**
 * @brief 检查种子卡网络连通性（支持指定网卡接口）
 * @param interface 网卡接口名称，如果为NULL则使用全局变量，如果为""则不检查IP地址
 * @return 0 表示网络连通, -1 表示网络不通
 */
static int check_seed_network_with_interface(const char *interface)
{
    const char *wan_interface = interface;

    if (wan_interface && strlen(wan_interface) > 0) {
        // 1. 检查网卡接口是否有IP地址
        FILE *fp;
        char command[64];
        char output[128];
        int has_ip = 0;

        snprintf(command, sizeof(command), "ifconfig %s", wan_interface);
        fp = popen(command, "r");
        if (fp == NULL) {
            LOG_E(LOG_TAG, "执行 ifconfig %s 失败: %s", wan_interface, strerror(errno));
            return -1;
        }

        while (fgets(output, sizeof(output), fp) != NULL) {
            if (strstr(output, "inet addr:") != NULL) {
                has_ip = 1;
                LOG_D(LOG_TAG, "接口 %s 有IP地址", wan_interface);
                break;
            }
        }
        pclose(fp);

        if (!has_ip) {
            LOG_W(LOG_TAG, "接口 %s 没有IP地址", wan_interface);
            return -1;
        }
    }

    // 2. 使用支持接口参数的check_network_with_interface函数进行网络连通性测试
    return check_network_with_interface(wan_interface);
}

/**
 * @brief 检查接口是否有IP地址
 * @param interface 网络接口名称
 * @return 1 表示有IP地址, 0 表示没有IP地址
 */
static int check_interface_has_ip(const char *interface)
{
    FILE *fp;
    char command[64];
    char output[128];
    int has_ip = 0;

    snprintf(command, sizeof(command), "ifconfig %s", interface);
    fp = popen(command, "r");
    if (fp == NULL) {
        LOG_E(LOG_TAG, "执行 ifconfig %s 失败: %s", interface, strerror(errno));
        return 0;
    }

    while (fgets(output, sizeof(output), fp) != NULL) {
        if (strstr(output, "inet addr:") != NULL) {
            has_ip = 1;
            break;
        }
    }
    pclose(fp);

    return has_ip;
}

/**
 * @brief 将UTF-8编码的字符串转换为GB2312编码或进行显示处理
 * @param utf8_str UTF-8编码的输入字符串
 * @return 处理后的字符串，需要调用者释放内存，失败返回NULL
 */
static char* convert_utf8_to_gb2312(const char* utf8_str)
{
    if (!utf8_str) {
        return NULL;
    }

#if USE_ICONV_CONVERSION
    // 使用iconv进行真正的编码转换（需要系统支持）
    #include <iconv.h>

    iconv_t cd = iconv_open("GB2312", "UTF-8");
    if (cd == (iconv_t)-1) {
        LOG_W(LOG_TAG, "iconv_open failed, using fallback method");
        // 转换失败，使用下面的fallback方法
    } else {
        size_t inlen = strlen(utf8_str);
        size_t outlen = inlen * 2; // GB2312可能需要更多空间
        char* outbuf = malloc(outlen + 1);
        if (outbuf) {
            char* inptr = (char*)utf8_str;
            char* outptr = outbuf;
            size_t inleft = inlen;
            size_t outleft = outlen;

            if (iconv(cd, &inptr, &inleft, &outptr, &outleft) != (size_t)-1) {
                *outptr = '\0';
                iconv_close(cd);
                return outbuf;
            }
            free(outbuf);
        }
        iconv_close(cd);
    }
#endif

    // for(int i = 0; i < strlen(utf8_str); i++) {
    //     LOG_V(LOG_TAG, "0x%02x ", utf8_str[i]);
    // }
    // LOG_V(LOG_TAG, " strlen(utf8_str)=%d\n", strlen(utf8_str));

    // Fallback方法：增强的UTF-8字符处理
    size_t len = strlen(utf8_str);
    // 预分配更大的缓冲区，因为16进制显示需要更多空间
    // 中文字符：3字节UTF-8 -> 12字符16进制(\xE7\x99\xBB)，扩展4倍
    // 为安全起见，分配5倍空间
    char* result = malloc(len * 5 + 1);
    if (!result) {
        return strdup(utf8_str); // 内存分配失败，返回原字符串
    }

    size_t i, j = 0;
    for (i = 0; i < len && utf8_str[i]; i++) {
        unsigned char c = (unsigned char)utf8_str[i];

        if (c < 0x80) {
            // ASCII字符，直接复制
            result[j++] = utf8_str[i];
        } else if (c < 0xC0) {
            // UTF-8续字节，跳过
            continue;
        } else if (c < 0xE0) {
            // 2字节UTF-8字符
            if (i + 1 < len) {
#if USE_ENHANCED_UTF8_DISPLAY
                // 增强模式：显示十六进制表示
                // 确保有足够空间：\xXXXX = 8字符 + 终止符
                if (j + 9 < len * 5) {
                    j += snprintf(result + j, 9, "\\x%02X%02X", c, (unsigned char)utf8_str[i + 1]);
                }
#else
                // 简单模式：替换为可读标识
                if (j + 3 < len * 5) {
                    result[j++] = '[';
                    result[j++] = '?';
                    result[j++] = ']';
                }
#endif
                i++; // 跳过下一个续字节
            }
        } else if (c < 0xF0) {
            // 3字节UTF-8字符（大部分中文）
            if (i + 2 < len) {
                unsigned char c1 = (unsigned char)utf8_str[i + 1];
                unsigned char c2 = (unsigned char)utf8_str[i + 2];

#if USE_ENHANCED_UTF8_DISPLAY
                // 增强模式：检测中文字符并显示16进制
                // 确保有足够空间：\xXXXXXX = 12字符 + 终止符
                if (j + 13 < len * 5) {
                    if (is_chinese_utf8(c, c1, c2)) {
                        // 检测到中文字符，显示16进制
                        j += snprintf(result + j, 13, "\\x%02X%02X%02X", c, c1, c2);
                    } else {
                        // 其他3字节UTF-8字符，也显示16进制
                        j += snprintf(result + j, 13, "\\x%02X%02X%02X", c, c1, c2);
                    }
                }
#else
                // 简单模式：替换为可读标识
                if (j + 4 < len * 5) {
                    if (is_chinese_utf8(c, c1, c2)) {
                        result[j++] = '[';
                        result[j++] = '中';
                        result[j++] = ']';
                    } else {
                        result[j++] = '[';
                        result[j++] = 'U';
                        result[j++] = '8';
                        result[j++] = ']';
                    }
                }
#endif
                i += 2; // 跳过后续2个续字节
            }
        } else {
            // 4字节UTF-8字符
            if (i + 3 < len) {
                unsigned char c1 = (unsigned char)utf8_str[i + 1];
                unsigned char c2 = (unsigned char)utf8_str[i + 2];
                unsigned char c3 = (unsigned char)utf8_str[i + 3];

#if USE_ENHANCED_UTF8_DISPLAY
                // 增强模式：检测4字节中文字符并显示16进制
                // 确保有足够空间：\xXXXXXXXX = 16字符 + 终止符
                if (j + 17 < len * 5) {
                    if (is_chinese_utf8_4byte(c, c1, c2, c3)) {
                        // 检测到4字节中文字符，显示16进制
                        j += snprintf(result + j, 17, "\\x%02X%02X%02X%02X", c, c1, c2, c3);
                    } else {
                        // 其他4字节UTF-8字符，也显示16进制
                        j += snprintf(result + j, 17, "\\x%02X%02X%02X%02X", c, c1, c2, c3);
                    }
                }
#else
                // 简单模式：替换为可读标识
                if (j + 4 < len * 5) {
                    if (is_chinese_utf8_4byte(c, c1, c2, c3)) {
                        result[j++] = '[';
                        result[j++] = '汉';
                        result[j++] = '4';
                        result[j++] = ']';
                    } else {
                        result[j++] = '[';
                        result[j++] = 'U';
                        result[j++] = '8';
                        result[j++] = ']';
                    }
                }
#endif
                i += 3; // 跳过后续3个续字节
            }
        }
    }

    result[j] = '\0';
    return result;
}

/**
 * @brief 检测是否为中文UTF-8字符
 * @param c1 UTF-8第一个字节
 * @param c2 UTF-8第二个字节
 * @param c3 UTF-8第三个字节
 * @return 1表示是中文字符，0表示不是
 */
static int is_chinese_utf8(unsigned char c1, unsigned char c2, unsigned char c3)
{
    // 中文字符的UTF-8编码范围
    // 基本汉字：U+4E00-U+9FFF -> UTF-8: E4B880-E9BFBF
    // 扩展A：U+3400-U+4DBF -> UTF-8: E38080-E4B6BF
    // 符号：U+3000-U+303F -> UTF-8: E38080-E380BF

    if (c1 == 0xE4) {
        // E4B880-E4BFBF (U+4E00-U+4FFF)
        return (c2 >= 0xB8 && c2 <= 0xBF);
    } else if (c1 >= 0xE5 && c1 <= 0xE8) {
        // E58080-E8BFBF (U+5000-U+8FFF)
        return 1;
    } else if (c1 == 0xE9) {
        // E98080-E9BFBF (U+9000-U+9FFF)
        return (c2 >= 0x80 && c2 <= 0xBF);
    } else if (c1 == 0xE3) {
        // 中文标点符号等
        return (c2 >= 0x80 && c2 <= 0xBF);
    }

    return 0;
}

/**
 * @brief 检测是否为4字节中文UTF-8字符
 * @param c1 UTF-8第一个字节
 * @param c2 UTF-8第二个字节
 * @param c3 UTF-8第三个字节
 * @param c4 UTF-8第四个字节
 * @return 1表示是4字节中文字符，0表示不是
 */
static int is_chinese_utf8_4byte(unsigned char c1, unsigned char c2, unsigned char c3, unsigned char c4)
{
    // 4字节中文字符的UTF-8编码范围
    // 扩展B区：U+20000-U+2A6DF -> UTF-8: F0A08080-F0AA9BBF
    // 扩展C区：U+2A700-U+2B73F -> UTF-8: F0AA9C80-F0AB9CBF
    // 扩展D区：U+2B740-U+2B81F -> UTF-8: F0AB9D80-F0ABA09F
    // 扩展E区：U+2B820-U+2CEAF -> UTF-8: F0ABA0A0-F0ACB9AF

    if (c1 == 0xF0) {
        if (c2 == 0xA0) {
            // F0A08080-F0A0BFBF (U+20000-U+23FFF) 扩展B区前部分
            return (c3 >= 0x80 && c3 <= 0xBF);
        } else if (c2 >= 0xA1 && c2 <= 0xA9) {
            // F0A18080-F0A9BFBF (U+24000-U+29FFF) 扩展B区中部分
            return 1;
        } else if (c2 == 0xAA) {
            if (c3 <= 0x9B) {
                // F0AA8080-F0AA9BBF (U+2A000-U+2A6DF) 扩展B区后部分
                return 1;
            } else if (c3 >= 0x9C) {
                // F0AA9C80-F0AABFBF (U+2A700-U+2ABFF) 扩展C区前部分
                return 1;
            }
        } else if (c2 == 0xAB) {
            if (c3 <= 0x9C) {
                // F0AB8080-F0AB9CBF (U+2AC00-U+2B73F) 扩展C区后部分
                return 1;
            } else if (c3 >= 0x9D && c3 <= 0xA0) {
                // F0AB9D80-F0ABA09F (U+2B740-U+2B81F) 扩展D区
                return 1;
            } else if (c3 >= 0xA0) {
                // F0ABA0A0-F0ABBFBF (U+2B820-U+2BFFF) 扩展E区前部分
                return 1;
            }
        } else if (c2 == 0xAC) {
            // F0AC8080-F0ACB9AF (U+2C000-U+2CEAF) 扩展E区后部分
            return (c3 <= 0xB9 || (c3 == 0xB9 && c4 <= 0xAF));
        }
    }

    return 0;
}

/**
 * @brief 处理种子卡检查结果
 * @param check_passed 检查是否通过
 * @param consecutive_failures 连续失败次数指针
 * @param max_failures 最大失败次数
 * @return 1 表示掉线, 0 表示在线
 */
static int handle_seed_check_result(int check_passed, int *consecutive_failures, int max_failures)
{
    if (!check_passed) {
        (*consecutive_failures)++;
        LOG_W(LOG_TAG, "种子卡网络检查失败 (连续失败次数: %d/%d)", *consecutive_failures, max_failures);

        if (*consecutive_failures >= max_failures) {
            LOG_E(LOG_TAG, "种子卡网络连续 %d 次检查失败，判断为掉线", *consecutive_failures);
            *consecutive_failures = 0; // 重置计数器
            return 1; // 掉线
        }
        return 0; // 暂时认为在线，等待下次检查
    } else {
        // 网络正常，重置失败计数器
        if (*consecutive_failures > 0) {
            LOG_I(LOG_TAG, "种子卡网络恢复正常，重置失败计数器 (之前连续失败 %d 次)", *consecutive_failures);
            *consecutive_failures = 0;
        }
        return 0; // 在线
    }
}

/**
 * @brief 检查种子卡是否掉线
 * @return 1 表示掉线, 0 表示在线
 */
static int check_seed_sim_offline_status(void)
{
    const char *wan_interface = get_seed_wan_name();
    static int consecutive_failures = 0;
    const int max_failures = 3; // 连续3次失败才认为掉线

    LOG_D(LOG_TAG, "检查种子卡网络状态，使用接口: %s", wan_interface);

    // 1. 检查种子卡网卡接口是否有IP地址
    int has_ip = check_interface_has_ip(wan_interface);
    if (has_ip) {
        LOG_D(LOG_TAG, "种子卡接口 %s 有IP地址", wan_interface);
    } else {
        LOG_W(LOG_TAG, "种子卡接口 %s 没有IP地址", wan_interface);
    }

    // 2. 检查网络连通性（无论IP地址检查结果如何都要执行）
    int network_ok = (check_seed_network_with_interface(wan_interface) == 0);
    if (network_ok) {
        LOG_D(LOG_TAG, "种子卡网络连通性检查成功");
    } else {
        LOG_W(LOG_TAG, "种子卡网络连通性检查失败");
    }

    // 3. 综合评估：只有IP地址和网络连通性都正常才认为检查通过
    int check_passed = (has_ip && network_ok);

    return handle_seed_check_result(check_passed, &consecutive_failures, max_failures);
}

static void *ito_http_client_thread(void *arg)
{
    LOG_D(LOG_TAG, "HTTP client thread started");

    // 初始化种子卡wan接口名称
    init_seed_wan_name();

    // 确保设备静态数据已加载
    update_device_static_data();
    update_device_dynamic_data();

    // 从NVRAM读取初始服务器地址
    char initial_host[64] = {0};
    char port_str[16] = {0};
    char resolved_ip[64] = {0};
    int initial_port = 0;

    if (cfg_get_item("vsim_cloud_ip", initial_host, sizeof(initial_host)) != 0 || strlen(initial_host) == 0) {
        LOG_W(LOG_TAG, "Failed to get 'vsim_cloud_ip' from NV, using default.");
        strncpy(initial_host, "dmp.momi-iot.com", sizeof(initial_host) - 1);
    }

    if (cfg_get_item("vsim_cloud_port", port_str, sizeof(port_str)) != 0 || (initial_port = atoi(port_str)) <= 0) {
        LOG_W(LOG_TAG, "Failed to get 'vsim_cloud_port' from NV, using default.");
        initial_port = 80;
    }
    
    // 如果是域名，则解析
    if (valid_ipv4(initial_host) == 0) {
        if (resolve_hostname(initial_host, resolved_ip, sizeof(resolved_ip), get_seed_wan_name()) != 0) {
            LOG_E(LOG_TAG, "Failed to resolve initial host '%s'. HTTP thread exit.", initial_host);
            return NULL;
        }
        strncpy(g_cloud_config.http_ip, resolved_ip, sizeof(g_cloud_config.http_ip) - 1);
    } else {
        strncpy(g_cloud_config.http_ip, initial_host, sizeof(g_cloud_config.http_ip) - 1);
    }
    g_cloud_config.http_port = initial_port;


    // 然后从云端获取并更新详细业务配置
    if (ito_cloud_get_config(&g_cloud_config, get_seed_wan_name()) != 0) {
        LOG_E(LOG_TAG, "Failed to get cloud config, HTTP thread exit.");
        return NULL;
    }

    // 1. 循环直到认证成功 - 变量声明移到函数开头
    static int get_card_consecutive_failures = 0; // 拿卡连续失败次数
    static int network_error_consecutive_failures = 0; // 网络错误连续失败次数
    const int max_network_failures = 3; // 连续3次网络错误才退出线程

login_get_card:
    ; // 空语句，标签后必须跟语句而不是声明

    while (g_running && !g_auth_successful) {
        LOG_I(LOG_TAG, "开始设备认证和登录流程...");

        // 种子卡认证阶段：执行完整的HTTP任务
        LOG_I(LOG_TAG, "种子卡认证阶段：执行流量上报、设备信息上传和设备状态上报");
        ito_cloud_report_flow(get_seed_wan_name());
        ito_cloud_upload_device_info(get_seed_wan_name());
        ito_cloud_report_device_state(get_seed_wan_name());

        // 调用登录/获取云卡接口
        int get_card_result = ito_cloud_login_get_card(get_seed_wan_name());
        if (get_card_result == ITO_CLOUD_SUCCESS) {
            LOG_I(LOG_TAG, "获取云卡信息成功，启动VSIM服务并检查网络注册...");

            // 拿卡成功，重置所有失败计数器
            if (get_card_consecutive_failures > 0 || network_error_consecutive_failures > 0) {
                LOG_I(LOG_TAG, "拿卡成功，重置失败计数器 (认证失败 %d 次, 网络错误 %d 次)",
                      get_card_consecutive_failures, network_error_consecutive_failures);
                get_card_consecutive_failures = 0;
                network_error_consecutive_failures = 0;
            }
            
        #ifdef DSDS_VSIM
            if(cloud_vsim_init(&cloud_card_info) != 0) {
                g_cloud_vsim_initialized = 0; // 初始化失败
                LOG_E(LOG_TAG, "云卡VSIM初始化失败 等待3秒 重新拿卡");
                sleep(3); // 优化：减少到3秒，VSIM初始化失败通常是配置问题，快速重试
                goto login_get_card;
            }
            g_cloud_vsim_initialized = 1; // 标记初始化成功
            LOG_D(LOG_TAG, "云卡VSIM初始化成功");
        #else
            // 启动VSIM相关服务
            start_vsim_services_if_needed();
        #endif

            int registration_attempts = 0;
            while(g_running && registration_attempts < 120) { // 优化：增加尝试次数，减少单次等待时间
                sleep(3); // 优化：减少到3秒，提高响应速度
                if (check_cloud_sim_registration()) {
                    LOG_I(LOG_TAG, "云卡网络注册成功！");
                    g_auth_successful = 1; // 设置标志位
                    g_reauth_started = 0;  // 清除重新鉴权标志

                #ifdef DSDS_VSIM
                    //云卡拨号 由平台处理 拨号成功会发送消息
                    for(int i = 0; i < 10; i++) { // 优化：增加尝试次数
                        LOG_I(LOG_TAG, "等待云卡拨号成功...%d\n", i);
                        if (check_cloud_sim_dial_success()) {
                            LOG_I(LOG_TAG, "云卡拨号成功... %d\n", i);
                            break;
                        }
                        sleep(3); // 优化：减少等待时间到3秒
                    }
                #endif
                    
                    // 认证成功后，执行一次进度上报
                    ito_cloud_progress_upload(NULL);

                    // 进度上报后，立即执行一次流量上报
                    LOG_I(LOG_TAG, "认证成功后执行流量上报");
                    ito_cloud_report_flow(NULL);

                    break; // 跳出注册检查循环
                }
                registration_attempts++;
                LOG_W(LOG_TAG, "云卡网络未注册，等待3秒后重试... (%d/120)", registration_attempts);
                sleep(3); // 优化：减少等待时间
            }

            if (!g_auth_successful) {
                LOG_E(LOG_TAG, "云卡网络注册超时，鉴权失败。");

                // 清理VSIM资源
                #ifdef DSDS_VSIM
                    // 只有在云卡VSIM已初始化的情况下才调用deinit
                    if (g_cloud_vsim_initialized) {
                        LOG_D(LOG_TAG, "云卡认证失败，清理VSIM资源");
                        cloud_vsim_deinit(&cloud_card_info);
                        g_cloud_vsim_initialized = 0; // 重置初始化标志
                    } else {
                        LOG_D(LOG_TAG, "云卡认证失败，但VSIM未初始化，无需清理");
                    }
                #endif
                sleep(5); // 优化：减少到5秒，认证失败后快速重试
                // 这里可能需要处理种子掉线的情况 不过这种概率偏低 (主流程之前已经判断过了，除非上了一下子就掉了)
                // 就算鉴权的时候种子卡掉线 -> 重新开始拿卡流程 -> 种子卡网络不通会退出主流程
                goto login_get_card; // 重新开始拿卡流程
            }

        } else {
            // 根据不同的错误码进行不同的处理
            if (get_card_result == ITO_CLOUD_ERROR_AUTH) {
                // SN号不合法，使用递增等待时间
                get_card_consecutive_failures++;
                // 重置网络错误计数器（因为这次是认证问题，不是网络问题）
                network_error_consecutive_failures = 0;

                // 优化：计算等待时间：15秒 * 2^(failures-1)，最大不超过5分钟
                int wait_time = 15 * (1 << (get_card_consecutive_failures - 1));
                if (wait_time > 300) { // 优化：最大等待5分钟，提高用户体验
                    wait_time = 300;
                }

                LOG_W(LOG_TAG, "登录获取云卡失败 - SN号不合法，连续失败%d次，将在%d秒后重试...",
                      get_card_consecutive_failures, wait_time);
                sleep(wait_time);

            } else if (get_card_result == ITO_CLOUD_ERROR_NETWORK || get_card_result == ITO_CLOUD_ERROR_HTTP) {
                // 网络错误，使用连续失败计数机制
                network_error_consecutive_failures++;
                // 重置认证失败计数器（因为这次是网络问题，不是认证问题）
                get_card_consecutive_failures = 0;

                LOG_W(LOG_TAG, "登录获取云卡失败 - 网络错误，连续网络错误次数: %d/%d",
                      network_error_consecutive_failures, max_network_failures);

                if (network_error_consecutive_failures >= max_network_failures) {
                    // 连续多次网络错误，退出线程释放资源
                    LOG_E(LOG_TAG, "连续 %d 次网络错误，发送CMD_VSIM_AUTH_NET_DISCONNETED 到平台应用 然后关闭整个云卡业务流程",
                          network_error_consecutive_failures);

                    // 1. 停止TCP线程（如果已启动）
                    if (g_ito_tcp_thread != 0) {
                        LOG_I(LOG_TAG, "停止TCP线程...");
                        pthread_cancel(g_ito_tcp_thread);
                        pthread_join(g_ito_tcp_thread, NULL);
                        g_ito_tcp_thread = 0;
                        LOG_I(LOG_TAG, "TCP线程已停止");
                    }

                    // 2. 重置线程状态
                    g_main_threads_running = 0;

                    // 3. 发送消息到平台应用的消息队列进行处理
                    if (ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_AT_CTL, CMD_VSIM_AUTH_NET_DISCONNETED,
                                        0, NULL, 0) == -1) {
                        LOG_E(LOG_TAG, "发送CMD_VSIM_AUTH_NET_DISCONNETED消息失败: %s", strerror(errno));
                    } else {
                        LOG_I(LOG_TAG, "发送CMD_VSIM_AUTH_NET_DISCONNETED消息成功");
                    }

                    // 4. 重置网络错误计数器并退出线程
                    network_error_consecutive_failures = 0;
                    LOG_I(LOG_TAG, "登录获取云卡失败 - 网络连续错误，HTTP线程即将退出");
                    return NULL;
                } else {
                    // 网络错误但未达到阈值，等待后重试
                    LOG_W(LOG_TAG, "网络连接有问题，将在15秒后重试...");
                    sleep(15); // 优化：减少到15秒，网络问题可能快速恢复
                }

            } else {
                // 其他错误（解析错误等），固定等待30秒
                // 重置网络错误计数器（因为这次不是网络问题）
                network_error_consecutive_failures = 0;

                const char* error_desc = (get_card_result == ITO_CLOUD_ERROR_PARSE) ? "响应解析错误" : "未知错误";
                LOG_W(LOG_TAG, "登录获取云卡失败 - %s，将在15秒后重试...", error_desc);
                sleep(15); // 优化：减少到15秒，解析错误等问题可能是临时的
            }
        }
    }

    // 2. 认证成功后的主操作循环
    time_t last_http_time = time(NULL);

    while (g_running) {
        time_t now = time(NULL);
        int flow_interval = g_cloud_config.flow_upload_times > 0 ? g_cloud_config.flow_upload_times : 60;

        // 检查云卡是否掉线
        if (check_cloud_sim_offline_status()) {
            LOG_W(LOG_TAG, "检测到云卡掉线，尝试重新注册网络...");
            g_auth_successful = 0; // 标记为未认证状态

        #if !defined(DSDS_VSIM) || defined(UNIT_TEST)
            // 平台应用处理不需要 这些处理
            // 云卡上去后，种子卡就再上不了网 需要切到通道 (平台应用需要优化) 但是切通道后，云卡鉴权会很慢? 是个问题
            //LOG_I(LOG_TAG, "设置wan1为默认路由...");
            //system("ip route replace default dev wan1 scope link");
            LOG_I(LOG_TAG, "切到种子卡通道...");

            // 查询当前DATASIM状态
            uint8_t current_datasim;
            zte_GetGTDATASIM(&current_datasim);
            LOG_I(LOG_TAG, "当前DATASIM状态: %d", current_datasim);
            if (current_datasim != 0) {
                LOG_I(LOG_TAG, "设置DATASIM为0");
                zte_SetGTDATASIM(0);
            } else {
                LOG_I(LOG_TAG, "DATASIM已经是0，无需设置");
            }

            // 查询当前DUALSIM状态
            uint8_t current_dualsim;
            zte_GetGTDUALSIM(&current_dualsim);
            LOG_I(LOG_TAG, "当前DUALSIM状态: %d", current_dualsim);
            if (current_dualsim != 0) {
                LOG_I(LOG_TAG, "设置DUALSIM为0");
                zte_SetGTDUALSIM(0);
            } else {
                LOG_I(LOG_TAG, "DUALSIM已经是0，无需设置");
            }

            sleep(1); // 等待网络切换完成
        #endif

            int re_registration_attempts = 0;
            int max_attempts = 20; // 优化：增加尝试次数，等待 20 * 3 = 60 秒
            int re_registered = 0;

            while (g_running && re_registration_attempts < max_attempts) {
                sleep(3); // 优化：减少到3秒，提高检测频率
                if (check_cloud_sim_registration()) {
                    LOG_I(LOG_TAG, "云卡网络重新注册成功！");
                    g_auth_successful = 1; // 重新认证成功
                    re_registered = 1;

                    // 重新注册成功后，上报进度
                    ito_cloud_progress_upload(NULL);

                    // 进度上报后，立即执行一次流量上报
                    LOG_I(LOG_TAG, "重新注册成功后执行流量上报");
                    ito_cloud_report_flow(NULL);

                    break;
                }
                re_registration_attempts++;
                LOG_W(LOG_TAG, "云卡网络重新注册中，等待3秒后重试... (%d/%d)", re_registration_attempts, max_attempts);
            }

            if (!re_registered) {
                LOG_E(LOG_TAG, "云卡网络重新注册失败，将重新获取云卡。");
                // g_auth_successful 已经是 0
                if (g_tcp_sockfd >= 0) {
                    close(g_tcp_sockfd);
                    g_tcp_sockfd = -1;
                }

                // 关掉云卡
                #ifdef DSDS_VSIM
                    if (g_cloud_vsim_initialized) {
                        LOG_D(LOG_TAG, "云卡重新注册失败，清理VSIM资源");
                        cloud_vsim_deinit(&cloud_card_info);
                        g_cloud_vsim_initialized = 0; // 重置初始化标志
                    }
                #endif

                // 判断种子卡是否掉线
                if (check_seed_sim_offline_status()) {
                    LOG_E(LOG_TAG, "种子卡网络掉线,发送CMD_VSIM_AUTH_NET_DISCONNETED 到平台应用 然后关闭整个云卡业务流程");

                    // 1. 停止TCP线程
                    if (g_ito_tcp_thread != 0) {
                        LOG_I(LOG_TAG, "停止TCP线程...");
                        pthread_cancel(g_ito_tcp_thread);
                        pthread_join(g_ito_tcp_thread, NULL);
                        g_ito_tcp_thread = 0;
                        LOG_I(LOG_TAG, "TCP线程已停止");
                    }

                    // 2. 重置线程状态
                    g_main_threads_running = 0;

                    // 3. 发送消息到平台应用的消息队列进行处理
                    if (ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_AT_CTL, CMD_VSIM_AUTH_NET_DISCONNETED,
                                        0, NULL, 0) == -1) {
                        LOG_E(LOG_TAG, "发送CMD_VSIM_AUTH_NET_DISCONNETED消息失败: %s", strerror(errno));
                    } else {
                        LOG_I(LOG_TAG, "发送CMD_VSIM_AUTH_NET_DISCONNETED消息成功");
                    }

                    // 4. 种子卡掉线，HTTP线程退出
                    LOG_I(LOG_TAG, "种子卡掉线，HTTP线程即将退出");
                    return NULL;
                } else {
                    // 种子卡正常，但云卡重新注册失败，跳出当前循环重新获取云卡
                    LOG_I(LOG_TAG, "种子卡正常，云卡重新注册失败，将重新获取云卡");
                    goto login_get_card;
                }
            }
            // 如果重新注册成功，则重置HTTP任务计时器，立即执行一次
            last_http_time = 0;
        }

        // 定期HTTP任务 (仅流量上报)
        if (now - last_http_time >= flow_interval) {
            LOG_I(LOG_TAG, "执行周期性HTTP任务：流量上报");
            ito_cloud_report_flow(NULL);
            last_http_time = now;
        }
        
        sleep(3); // 优化：减少到3秒，提高响应速度
    }

    LOG_D(LOG_TAG, "HTTP client thread stopped");
    return NULL;
}

static void *ito_tcp_client_thread(void *arg)
{
    LOG_D(LOG_TAG, "TCP client thread started");

    // 初始化消息流水号
    g_msg_sequence = time(NULL) % 65536;

    // 等待HTTP线程获取到配置
    int wait_count = 0;
    while (g_running && strlen(g_cloud_config.tcp_ip) == 0 && wait_count < 30) {
        LOG_I(LOG_TAG, "TCP thread waiting for cloud config (%d/30)...", wait_count+1);
        sleep(1);
        wait_count++;
    }
    
    if (strlen(g_cloud_config.tcp_ip) == 0) {
        LOG_E(LOG_TAG, "Timeout waiting for cloud config. TCP thread exit.");
        return NULL;
    }

    // 等待认证成功
    wait_count = 0;
    while (g_running && !g_auth_successful && wait_count < 300) { // 最多等待5分钟
        LOG_I(LOG_TAG, "TCP线程等待认证完成 (%d/300)...", wait_count+1);
        sleep(1);
        wait_count++;
    }

    if (!g_auth_successful) {
        LOG_E(LOG_TAG, "等待认证超时，TCP线程退出。");
        return NULL;
    }

    char resolved_ip[64];
    int use_resolved_ip = 0;

    if (valid_ipv4(g_cloud_config.tcp_ip) == 0 && strlen(g_cloud_config.tcp_ip) > 0) {
        LOG_I(LOG_TAG, "TCP host '%s' is a domain, resolving...", g_cloud_config.tcp_ip);
        if (resolve_hostname(g_cloud_config.tcp_ip, resolved_ip, sizeof(resolved_ip), NULL) == 0) {
            use_resolved_ip = 1;
        } else {
            LOG_E(LOG_TAG, "Failed to resolve TCP host '%s'. TCP thread will not start.", g_cloud_config.tcp_ip);
            return NULL;
        }
    }

    time_t last_heartbeat_time = time(NULL);
    int reconnect_attempt = 0;

    while (g_running) {
        time_t now = time(NULL);
        int heartbeat_interval = g_cloud_config.heart_beat_times > 0 ? 
                                g_cloud_config.heart_beat_times : 30;

        // 连接管理
        if (g_tcp_sockfd < 0) {
            LOG_I(LOG_TAG, "TCP connection down, attempting to reconnect...");
            const char* target_ip = use_resolved_ip ? resolved_ip : g_cloud_config.tcp_ip;
            g_tcp_sockfd = ito_cloud_tcp_connect(target_ip, g_cloud_config.tcp_port);
            
            if (g_tcp_sockfd < 0) {
                // 指数退避重连
                int delay = 1 << reconnect_attempt;
                if (delay > 60) delay = 60; // 最大60秒
                sleep(delay);
                reconnect_attempt++;
                continue;
            }
            reconnect_attempt = 0;
            last_heartbeat_time = now;
        }

        // 心跳发送
        if (now - last_heartbeat_time >= heartbeat_interval) {
            if (ito_cloud_send_tcp_heartbeat() != 0) {
                close(g_tcp_sockfd);
                g_tcp_sockfd = -1;
                LOG_W(LOG_TAG, "Heartbeat failed, reconnecting...");
                sleep(1);
            }
            last_heartbeat_time = now;
        }

        // 接收处理消息
        if (g_tcp_sockfd >= 0) {
            unsigned char buffer[1024];
            fd_set read_fds;
            struct timeval tv = {.tv_sec = 1, .tv_usec = 0};
            
            FD_ZERO(&read_fds);
            FD_SET(g_tcp_sockfd, &read_fds);
            
            if (select(g_tcp_sockfd + 1, &read_fds, NULL, NULL, &tv) > 0) {
                if (FD_ISSET(g_tcp_sockfd, &read_fds)) {
                    ssize_t n = recv(g_tcp_sockfd, buffer, sizeof(buffer), 0);
                    if (n > 0) {
                        common_tcp_rev_message(buffer, n);
                    } else if (n == 0) {
                        LOG_W(LOG_TAG, "TCP connection closed by peer");
                        close(g_tcp_sockfd);
                        g_tcp_sockfd = -1;
                    } else {
                        LOG_E(LOG_TAG, "TCP recv error: %s", strerror(errno));
                        close(g_tcp_sockfd);
                        g_tcp_sockfd = -1;
                    }
                }
            }
        }
        
        sleep(1);
    }

    if (g_tcp_sockfd >= 0) {
        close(g_tcp_sockfd);
        g_tcp_sockfd = -1;
        g_heartbeat_ok = 0;
    }

    LOG_D(LOG_TAG, "TCP client thread stopped");
    return NULL;
}

// ===================== 客户端管理 =====================

/**
 * 种子卡拨号上网
 * 先实现单卡的逻辑
 */
static int seed_card_init() {
    int ret;
    int cfun_status = 0;
    char cpin_status[32] = {0};
    char imsi[32] = {0};
    char iccid[32] = {0};
    char cops_rsp[128] = {0};

    LOG_I(LOG_TAG, "开始种子卡初始化流程...");

    // 1. 检查 AT+CFUN?
    void *p_cfun[] = {&cfun_status};
    ret = get_modem_info2("AT+CFUN?\r", "%d", p_cfun, 0, 5);
    if (ret != 0 || cfun_status != 1) {
        LOG_W(LOG_TAG, "CFUN 状态不是 1 (当前是 %d), 设置为 1...", cfun_status);
        ret = get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10);
        if (ret != 0) {
            LOG_E(LOG_TAG, "设置 AT+CFUN=1 失败");
            return 0;
        }
        sleep(2); // 优化：减少到2秒，模块重启检查快速重试
    }
    LOG_I(LOG_TAG, "CFUN 状态正常 (1)");

    // 2. 检查 AT+CPIN?
    int cpin_retries = 0;
    while (cpin_retries < 10) { // 最多等待 20 秒
        void *p_cpin[] = {cpin_status};
        ret = get_modem_info2("AT+CPIN?\r", "%s", p_cpin, 0, 5);
        if (ret == 0 && strstr(cpin_status, "READY") != NULL) {
            LOG_I(LOG_TAG, "SIM 卡状态正常: %s", cpin_status);
            break;
        }
        LOG_W(LOG_TAG, "SIM 卡未就绪 (状态: %s), 等待 2 秒后重试...", cpin_status);
        sleep(2);
        cpin_retries++;
    }
    if (cpin_retries >= 10) {
        LOG_E(LOG_TAG, "SIM 卡状态超时，初始化失败");
        return 0;
    }

    // 3. 获取 AT+CIMI
    void *p_imsi[] = {imsi};
    ret = get_modem_info2("AT+CIMI\r", "%s", p_imsi, 0, 5);
    if (ret == 0 && strlen(imsi) > 5) {
        LOG_I(LOG_TAG, "获取到 IMSI: %s", imsi);
        sc_cfg_set("seed_imsi", imsi);
    } else {
        LOG_E(LOG_TAG, "获取 IMSI 失败");
        return 0;
    }

    // 4. 获取 AT+ZICCID
    void *p_iccid[] = {iccid};
    ret = get_modem_info2("AT+ZICCID?\r", "%s", p_iccid, 0, 5);
    if (ret == 0 && strlen(iccid) > 10) {
        LOG_I(LOG_TAG, "获取到 ICCID: %s", iccid);
        sc_cfg_set("seed_iccid", iccid);
    } else {
        LOG_E(LOG_TAG, "获取 ICCID 失败");
        return 0;
    }

    // 5. 检查网络注册状态 AT+COPS?
    int cops_retries = 0;
    while (cops_retries < 30) { // 最多等待 60 秒
        void *p_cops[] = {cops_rsp};
        ret = get_modem_info2("AT+COPS?\r", "%s", p_cops, 0, 5);
        if (ret == 0 && cops_rsp[0] == '0' && strchr(cops_rsp, ',') != NULL) {
            LOG_I(LOG_TAG, "网络已注册，响应: %s", cops_rsp);
            break;
        }
        LOG_W(LOG_TAG, "网络未注册 (响应: %s), 等待 2 秒后重试...", cops_rsp);
        sleep(2);
        cops_retries++;
    }
    if (cops_retries >= 30) {
        LOG_E(LOG_TAG, "网络注册超时，初始化失败");
        return 0;
    }

    // 6. 拨号
    LOG_I(LOG_TAG, "开始拨号...");
    ret = get_modem_info2("AT+CGACT=1,1\r", NULL, NULL, 0, 20);
    if (ret != 0) {
        LOG_W(LOG_TAG, "AT+CGACT=1,1 失败");
    }
    ret = get_modem_info2("AT+ZGACT=1,1\r", NULL, NULL, 0, 20);
    if (ret != 0) {
        LOG_E(LOG_TAG, "AT+ZGACT=1,1 失败，拨号失败");
        return 0;
    }
    LOG_I(LOG_TAG, "拨号指令发送成功");

    // 7. 保存配置
    sc_cfg_save();
    LOG_I(LOG_TAG, "种子卡信息已保存");

    return 1; // 初始化成功
}


static int start_main_client_threads(void)
{
    // 如果线程已经在运行，先停止它们
    if (g_main_threads_running) {
        LOG_W(LOG_TAG, "主要客户端线程已在运行，先停止TCP线程");
        stop_main_client_threads();
    }

    if (pthread_create(&g_ito_http_thread, NULL, ito_http_client_thread, NULL) != 0) {
        LOG_E(LOG_TAG, "Failed to create ito_http_client_thread");
        return -1;
    }

    if (pthread_create(&g_ito_tcp_thread, NULL, ito_tcp_client_thread, NULL) != 0) {
        LOG_E(LOG_TAG, "Failed to create ito_tcp_client_thread");
        pthread_cancel(g_ito_http_thread); // Clean up the already created thread
        return -1;
    }

    g_main_threads_running = 1; // 标记线程已启动
    LOG_I(LOG_TAG, "主要客户端线程已成功启动");
    return 0;
}

/**
 * @brief 停止主要的客户端线程（从外部线程调用）
 */
static void stop_main_client_threads(void)
{
    if (!g_main_threads_running) {
        LOG_D(LOG_TAG, "主要客户端线程未运行，无需停止");
        return;
    }

    LOG_I(LOG_TAG, "正在停止主要客户端线程...");

    // 设置运行标志为0，通知线程退出
    g_running = 0;

    // 关闭TCP连接
    if (g_tcp_sockfd != -1) {
        LOG_I(LOG_TAG, "关闭TCP连接");
        close(g_tcp_sockfd);
        g_tcp_sockfd = -1;
    }

    // 等待HTTP线程结束
    if (g_ito_http_thread != 0) {
        LOG_I(LOG_TAG, "等待HTTP线程结束...");
        pthread_cancel(g_ito_http_thread);
        pthread_join(g_ito_http_thread, NULL);
        g_ito_http_thread = 0;
        LOG_I(LOG_TAG, "HTTP线程已结束");
    }

    // 等待TCP线程结束
    if (g_ito_tcp_thread != 0) {
        LOG_I(LOG_TAG, "等待TCP线程结束...");
        pthread_cancel(g_ito_tcp_thread);
        pthread_join(g_ito_tcp_thread, NULL);
        g_ito_tcp_thread = 0;
        LOG_I(LOG_TAG, "TCP线程已结束");
    }

    // 重置相关状态
    g_heartbeat_ok = 0;
    g_main_threads_running = 0; // 标记线程已停止

    LOG_I(LOG_TAG, "主要客户端线程已全部停止");
}

static void *seed_card_dial_thread(void *arg)
{
    LOG_I(LOG_TAG, "Seed card dial thread started.");

#if 1
    // 种子卡拨号由 中兴微平台拨号 不需要自己拨号
    g_seed_card_initialized = 1;
#endif

    while (g_running) {
        if (!g_seed_card_initialized) {
            LOG_I(LOG_TAG, "种子卡未初始化，开始执行初始化和拨号...");
            if (seed_card_init()) {
                LOG_I(LOG_TAG, "种子卡初始化和拨号成功。");
                g_seed_card_initialized = 1;
            } else {
                LOG_E(LOG_TAG, "种子卡初始化失败，将在5秒后重试...");
                sleep(5); // 优化：减少到5秒，种子卡初始化失败快速重试
                continue; // 继续下一次循环以重试初始化
            }
        }

        // 种子卡拨号成功后 获取wan 接口名称
        init_seed_wan_name();

        // 如果已经初始化，则检查网络
        if (g_seed_card_initialized) {
            LOG_I(LOG_TAG, "种子卡已初始化，检查网络连通性...");
            if (check_seed_network_with_interface(get_seed_wan_name()) == 0) {
                LOG_I(LOG_TAG, "种子卡已初始化, 主线程启动之前 网络已连接。");
                if (start_main_client_threads() == 0) {
                    LOG_I(LOG_TAG, "主业务线程已成功启动。");
                    break; // 成功启动，退出拨号线程循环
                } else {
                    LOG_E(LOG_TAG, "启动主业务线程失败，将重试...");
                    // 注意：这里不重置 g_seed_card_initialized，因为拨号仍然是成功的
                }
            } else {
                LOG_W(LOG_TAG, "种子卡已初始化, 主线程启动启动之前 网络未连通，将在5秒后重试检查...");
                sleep(5); // 优化：减少到5秒，网络检查快速重试
            }
        }
        
        
    }

    LOG_I(LOG_TAG, "Seed card dial thread finished.");
    return NULL;
}


int ito_cloud_vsim_client_init(void)
{
    g_running = 1;
    memset(&g_cloud_config, 0, sizeof(g_cloud_config));

    if (pthread_create(&g_seed_dial_thread, NULL, seed_card_dial_thread, NULL) != 0) {
        LOG_E(LOG_TAG, "Failed to create seed_card_dial_thread");
        g_running = 0;
        return -1;
    }

    LOG_I(LOG_TAG, "ito_cloud_vsim_client_init success, dial thread started.");
    return 0;
}

void ito_cloud_vsim_client_deinit(void)
{
    if (g_running) {
        g_running = 0;

        // Join all threads. It's safe to join a thread that has already exited.
        // Check if the thread IDs are valid before joining.
        if (g_seed_dial_thread) {
            pthread_join(g_seed_dial_thread, NULL);
        }
        if (g_ito_http_thread) {
            pthread_join(g_ito_http_thread, NULL);
        }
        if (g_ito_tcp_thread) {
            pthread_join(g_ito_tcp_thread, NULL);
        }
        
        LOG_I(LOG_TAG, "ito_cloud_vsim_client_deinit success");
    }
}

