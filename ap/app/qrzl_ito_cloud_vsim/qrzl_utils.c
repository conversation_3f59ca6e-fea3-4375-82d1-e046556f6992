#include <sys/prctl.h>
#include <sys/time.h>
#include <string.h>
#include <unistd.h>
#include <ctype.h>
#include <stdlib.h>
#include <pthread.h>
#include <arpa/inet.h>
#include <time.h>

#include "nv_api.h"

#include "qrzl_utils.h"


static const unsigned char base64_table[65] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
/**
 * base64_decode - Base64 decode
 * @src: Data to be decoded
 * @len: Length of the data to be decoded
 * @out_len: Pointer to output length variable
 * Returns: Allocated buffer of out_len bytes of decoded data,
 * or %NULL on failure
 *
 * Caller is responsible for freeing the returned buffer.
 */
static unsigned char * qrzl_base64_decode(const unsigned char *src, size_t len,
                                  size_t *out_len)
{
	unsigned char dtable[256], *out, *pos, in[4], block[4], tmp;
	size_t i, count, olen;

	memset(dtable, 0x80, 256);
	for (i = 0; i < sizeof(base64_table) - 1; i++)
		dtable[base64_table[i]] = (unsigned char) i;
	dtable['='] = 0;

	count = 0;
	for (i = 0; i < len; i++) {
		if (dtable[src[i]] != 0x80)
			count++;
	}

	if (count == 0 || count % 4) 
        return NULL;
		
	olen = count / 4 * 3;
	pos = out = malloc(olen);
	if (out == NULL)
        return NULL;
		
	memset(pos, 0, olen);

	count = 0;
	for (i = 0; i < len; i++) {
		tmp = dtable[src[i]];
		if (tmp == 0x80)
			continue;

		in[count] = src[i];
		block[count] = tmp;
		count++;
		if (count == 4) {
			*pos++ = (block[0] << 2) | (block[1] >> 4);
			*pos++ = (block[1] << 4) | (block[2] >> 2);
			*pos++ = (block[2] << 6) | block[3];
			count = 0;
		}
	}

	if (pos > out) {
		if (in[2] == '=')
			pos -= 2;
		else if (in[3] == '=')
			pos--;
	}

	*out_len = pos - out;
	return out;
}

char *qrzl_base64_encode(const char *data, int data_len)
{ 
	int prepare = 0; 
	int ret_len; 
	int temp = 0; 
	char *ret = NULL; 
	char *f = NULL; 
	int tmp = 0; 
	char changed[4]; 
	int i = 0; 
	const char base[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; 

	if(data == NULL)
	{
		return NULL;
	}
	if(data_len == 0)
	{
		return NULL;
	}
	ret_len = data_len / 3; 
	temp = data_len % 3; 
	if (temp > 0) 
	{ 
	ret_len += 1; 
	} 
	ret_len = ret_len*4 + 1; 
	ret = (char *)malloc(ret_len); 

	if (ret == NULL) 
	{ 
		qrzl_err("No enough memory.\n"); 
		return NULL;
	} 
	memset(ret, 0, ret_len); 
	f = ret; 
	while (tmp < data_len) 
	{ 
		temp = 0; 
		prepare = 0; 
		memset(changed, '\0', 4); 
		while (temp < 3) 
		{ 
			//printf("tmp = %d\n", tmp); 
			if (tmp >= data_len) 
			{ 
				break; 
			} 
			prepare = ((prepare << 8) | (data[tmp] & 0xFF)); 
			tmp++; 
			temp++; 
		} 
		prepare = (prepare<<((3-temp)*8)); 
		//printf("before for : temp = %d, prepare = %d\n", temp, prepare); 
		for (i = 0; i < 4 ;i++ ) 
		{ 
			if (temp < i) 
			{ 
				changed[i] = 0x40; 
			} 
			else 
			{ 
				changed[i] = (prepare>>((3-i)*6)) & 0x3F; 
			} 
			*f = base[changed[i]]; 
			//printf("%.2X", changed[i]); 
			f++; 	
		} 
	} 
	*f = '\0'; 
	return ret; 
}

int qrzl_base64_encode_safe(const char *data, int data_len, char *dest, size_t dest_len)
{
    char* encode = qrzl_base64_encode(data, data_len);
    if (encode == NULL)
    {
        return -1;
    }
    snprintf(dest, dest_len, "%s", encode);
    free(encode);
    return 0;
}

/**
 * 判断一个字符串是否是只包含英文字母加数字
 */
int is_alphanumeric(const char *str) {
    while (*str) {
        if (!isalnum((unsigned char)*str)) {
            return -1; // 如果字符不是字母或数字，返回0
        }
        str++;
    }
    return 0; // 如果所有字符都是字母或数字，返回1
}

static pthread_mutex_t switch_sim_card_lock;  // 定义切卡互斥锁，防止多线程同时切卡
static pthread_mutex_t update_device_dynamic_data_lock; // 更新设备动态数据锁，防止多线程环境下同时更新造成的异常

/* 全局变量 不会改变的设备数据 */
struct device_static_data_t g_qrzl_device_static_data = {};
/* 全局变量，可变的设备数据 */
struct device_dynamic_data_t g_qrzl_device_dynamic_data = {};

void remove_spaces(char *str) {
    int i = 0, j = 0;
    while (str[i]) {
        if (str[i] != ' ') {
            str[j++] = str[i]; // 将非空格字符移动到前面
        }
        i++;
    }
    str[j] = '\0'; // 添加字符串结束符
}

/*set process name*/
void qrzl_set_process_name(const char* name)
{
    if((name == NULL) || (*name == '\0'))
        return;
    prctl(PR_SET_NAME, name, 0, 0, 0);
}
/*set thread name*/
void qrzl_set_thread_name(const char*name)
{
    /*we should use pthread_setname_np, but it is not portable, user prctl instead*/
    qrzl_set_process_name(name);
}

void qrzl_utils_init()
{
    if (pthread_mutex_init(&switch_sim_card_lock, NULL) != 0) {
        qrzl_log("switch_sim_card_lock init failed\n");
    }
    if (pthread_mutex_init(&update_device_dynamic_data_lock, NULL) != 0) {
        qrzl_log("update_device_dynamic_data_lock init failed\n");
    }

}

void qrzl_utils_destroy()
{
    pthread_mutex_destroy(&switch_sim_card_lock);
    pthread_mutex_destroy(&update_device_dynamic_data_lock);
}

int nv_update_net_band()
{
    int ret = 0;
	int act = 0;
	int band = 0;
	char *p[] = {&act, &band};
	ret = get_modem_info2("AT+ZBAND?\r", "%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get modem info err: %d\n", ret);
        return ret;
	}
	else 
	{
		qrzl_log("act = %d, band = %d\n", act, band);
		if (act == 3 && band != 0 && band != 255) 
		{
			char tmpstr[10] = {0};
			sprintf(tmpstr, "B %d", band);
			cfg_set(NV_CURRENT_NET_BAND, tmpstr);
		}
		else
		{
			cfg_set(NV_CURRENT_NET_BAND, "--");
		}
	}

    int pa_adc = 0;
    char *PATEMP_p[] = {&pa_adc};
    ret = get_modem_info2("AT+GETPATEMP?\r", "%d", (void**)PATEMP_p,0,10);
    if (ret == 0) {
        qrzl_log("pa_adc: %d", pa_adc);
    }

    return 0;
}

int nv_update_net_cesq_info()
{
    int ret = 0;
	int rxlev, ber, rscp, ecno, rsrq, rsrp; 
	char *p[] = {&rxlev, &ber, &rscp, &ecno, &rsrq, &rsrp};
	ret = get_modem_info2("AT+CESQ\r", "%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get cesq err: %d\n", ret);
        return ret;
	}
	else 
	{
        char rsrq_str[10] = {0};
        snprintf(rsrq_str, sizeof(rsrq_str), "%d", rsrq);
        cfg_set("rsrq", rsrq_str);
	}

    return 0;
}

int nv_update_sn()
{
    int ret = 0;
    char sn[21] = {0};
    void *p[] = {sn};
    ret = get_modem_info2("AT+BOARDNUM?\r", "%s",  p,0,5);
    if (ret != 0) 
	{
		qrzl_err("get sn err: %d\n", ret);
        return ret;
	}
    if (ret == 0 && strlen(sn) >= 12) {
        qrzl_log("sn = %s", sn);
        if (is_alphanumeric(sn) != 0)
        {
            return -1;
        }
        snprintf(g_qrzl_device_static_data.sn, sizeof(g_qrzl_device_static_data.sn), "%s", sn);
        cfg_set(NV_SN, sn);
    }
    return 0;
}

void get_lte_band(char *band)
{
    int ret;
    int b1, b2, b3, b4, b5, b6, b7, b8 = 0;
    char *p[] = {&b1, &b2, &b3, &b4, &b5, &b6, &b7, &b8};
    ret = get_modem_info2("AT+ZLTEBAND?\r", "%d,%d,%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get ZLTEBAND info err: %d\n", ret);
	}
    else
    {
        sprintf(band, "%d,%d,%d,%d,%d,%d,%d,%d", b1, b2, b3, b4, b5, b6, b7, b8);
    }
}

void update_device_static_data()
{
    nv_update_sn();
    
    cfg_get_item(NV_IMEI, g_qrzl_device_static_data.imei, 64);
    cfg_get_item(NV_MAC1, g_qrzl_device_static_data.mac, 32);
    cfg_get_item(NV_SOFT_VERSION, g_qrzl_device_static_data.soft_version, 64);
    cfg_get_item("hw_version", g_qrzl_device_static_data.hw_version, sizeof(g_qrzl_device_static_data.hw_version));
    cfg_get_item(NV_SN, g_qrzl_device_static_data.sn, 32); // sn号也由nv 初始化 sn时更新到 g_qrzl_device_static_data.sn
#ifdef JCV_HW_UZ901_V1_4
    strcpy(g_qrzl_device_static_data.device_type, "UFI");
#else 
    strcpy(g_qrzl_device_static_data.device_type, "MIFI");
#endif
    get_lte_band(g_qrzl_device_static_data.adjust_band);
    strcpy(g_qrzl_device_static_data.dual_sim, "1");
    cfg_get_item("esim1_iccid", g_qrzl_device_static_data.nvro_esim1_iccid, sizeof(g_qrzl_device_static_data.nvro_esim1_iccid));
    cfg_get_item("esim1_imsi", g_qrzl_device_static_data.nvro_esim1_imsi, sizeof(g_qrzl_device_static_data.nvro_esim1_imsi));

    cfg_get_item("esim2_iccid", g_qrzl_device_static_data.nvro_esim2_iccid, sizeof(g_qrzl_device_static_data.nvro_esim2_iccid));
    cfg_get_item("esim2_imsi", g_qrzl_device_static_data.nvro_esim2_imsi, sizeof(g_qrzl_device_static_data.nvro_esim2_imsi));

    cfg_get_item("esim3_iccid", g_qrzl_device_static_data.nvro_esim3_iccid, sizeof(g_qrzl_device_static_data.nvro_esim3_iccid));
    cfg_get_item("esim3_imsi", g_qrzl_device_static_data.nvro_esim3_imsi, sizeof(g_qrzl_device_static_data.nvro_esim3_imsi));

    cfg_get_item("esim1_mno", g_qrzl_device_static_data.esim1_mno, sizeof(g_qrzl_device_static_data.esim1_mno));
    cfg_get_item("esim2_mno", g_qrzl_device_static_data.esim2_mno, sizeof(g_qrzl_device_static_data.esim2_mno));
    cfg_get_item("esim3_mno", g_qrzl_device_static_data.esim3_mno, sizeof(g_qrzl_device_static_data.esim3_mno));
}

/**
 * 更新电量百分比的值
 */
static int update_battery_vol_percent()
{
    int ret;
    FILE *fp;
    int voltage_now;
    int percentage = 0;
    char voltage_str[128] = {0};
    ret = cfg_get_item("mmi_battery_voltage_line", voltage_str, sizeof(voltage_str));
    if (ret != 0)
    {
        qrzl_err("Failed to get mmi_battery_voltage_line");
        return -1;
    }

    // 读取电压值
    fp = fopen("/sys/class/power_supply/battery/voltage_now", "r");
    if (!fp) {
        qrzl_err("Failed to open voltage file");
        cfg_set("battery_vol_percent", "100");
        return -1;
    }
    if (fscanf(fp, "%d", &voltage_now) != 1) {
        qrzl_err("Failed to read voltage value");
        cfg_set("battery_vol_percent", "100");
        fclose(fp);
        return -1;
    }
    fclose(fp);

    // 分割电压字符串并比较
    char *token;
    char voltage_copy[256];
    strncpy(voltage_copy, voltage_str, sizeof(voltage_copy) - 1);
    voltage_copy[sizeof(voltage_copy) - 1] = '\0';
    token = strtok(voltage_copy, "+");
    int current_voltage;
    int step = 5;  // 每个区间的百分比增量

    while (token) {
        current_voltage = atoi(token);
        if (voltage_now < current_voltage) {
            break;
        }
        percentage += step;
        token = strtok(NULL, "+");
    }
    percentage -= 5;
    if (percentage < 0) {
        percentage = 0;
    }
    char percentage_str[4] = {0};
    snprintf(percentage_str, sizeof(percentage_str), "%d", percentage);
    return cfg_set("battery_vol_percent", percentage_str);
}

/**
 * 判断一个字符串是否在另一个字符串中
 * 例如：ESIM1_only,ESIM2_only,RSIM_only， 判断 ESIM1_only 是否存在
 * 找到返回 1，未找到返回 0
 */
int contains_type(const char *input, const char *target) {
    char temp[128];
    strncpy(temp, input, sizeof(temp));
    temp[sizeof(temp) - 1] = '\0';  // 保证末尾有 '\0'

    char *token = strtok(temp, ",");
    while (token != NULL) {
        if (strcmp(token, target) == 0) {
            return 1; // 找到了
        }
        token = strtok(NULL, ",");
    }
    return 0; // 没找到
}

/**
 * 移除字符串中指定项（保留逗号分隔格式）
 */
void remove_item_from_csv(char *input, const char *target) {
    char result[256] = {0};
    char *token = strtok(input, ",");
    int first = 1;

    while (token != NULL) {
        if (strcmp(token, target) != 0) {
            if (!first) {
                strcat(result, ",");
            }
            strcat(result, token);
            first = 0;
        }
        token = strtok(NULL, ",");
    }

    // 将结果写回原始 input
    strcpy(input, result);
}

/**
 * 向 CSV 字符串中添加子字符串（防止重复）
 */
void add_item_to_csv(char *csv, const char *item) {
    if (!contains_type(csv, item)) {
        if (strlen(csv) > 0) {
            strcat(csv, ",");
        }
        strcat(csv, item);
    }
}

/**
 * 更新主板温度
 */
static int update_board_temperature()
{
    int ret;
    char pa_temperature_str[1024] = {0};
    int percentage = 90;

    ret = cfg_get_item("board_pa_temperature_line", pa_temperature_str, sizeof(pa_temperature_str));
    if (ret != 0)
    {
        qrzl_err("Failed to get board_pa_temperature_line");
        return -1;
    }

    // 发送at获取当前pa温度
    int pa_adc = 0;
    char *PATEMP_p[] = {&pa_adc};
    ret = get_modem_info2("AT+GETPATEMP?\r", "%d", (void**)PATEMP_p,0,10);
    if (ret == 0) {
        qrzl_log("pa_adc: %d", pa_adc);
    }

    // 分割温度字符串并比较
    char *token;
    char pa_temperature_copy[256];
    strncpy(pa_temperature_copy, pa_temperature_str, sizeof(pa_temperature_copy) - 1);
    pa_temperature_copy[sizeof(pa_temperature_copy) - 1] = '\0';
    token = strtok(pa_temperature_copy, "+");
    int current_temperature;
    int step = 5;  // 每个区间的百分比增量

    while (token) {
        current_temperature = atoi(token);
        if (pa_adc < current_temperature) {
            break;
        }
        percentage -= step;
        token = strtok(NULL, "+");
    }
    char percentage_str[4] = {0};
    snprintf(percentage_str, sizeof(percentage_str), "%d", percentage);
    return cfg_set("board_temperature", percentage_str);
}

/**
 * 获取当前SIM卡的卡槽号
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index()
{
    int ret;
    int card_switch_type = -1;
    char *p[] = {&card_switch_type};
    ret = get_modem_info2("AT+ZCARDSWITCH?\r", "%d", (void**)p, 0, 10);
	if (ret != 0) 
	{
		qrzl_err("获取当前卡通道失败 err: %d\n", ret);
        return -1;
	}

    /**
     * DZ802的卡通道和之前的是相反的，这里就做一下简单处理
     */
#if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5)
    if (card_switch_type == 0) {
        card_switch_type = 3;
    } else if(card_switch_type == 3) {
        card_switch_type = 0;
    }
#endif

/**
 * 请注意，qrzl_app程序中的current_sim代表是逻辑上的RSIM ESIM1 ESIM2，NV中的sim_select表示的是板子上实际的物理通道
 */
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (card_switch_type == 0) {
        return 2;
    } else if (card_switch_type == 3) {
        return 1;
    }
    return -1;
#else
    if (card_switch_type == 0) {
        return 0;
    } else if (card_switch_type == 3) {

        FILE *sim_siwtch_fp;
        int esim_num = -1;

        // 打开 sysfs 文件
        sim_siwtch_fp = fopen("/sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness", "r");
        if (sim_siwtch_fp != NULL) {
            // 读取数值
            if (fscanf(sim_siwtch_fp, "%d", &esim_num) != 1) {
                qrzl_err("Failed to read /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness value");
            }
            // 关闭文件
            fclose(sim_siwtch_fp);
        } else {
            qrzl_err("Failed to open /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        if (esim_num == 0) {
            return 1;
        } else if (esim_num == 1) {
            return 2;
        }
        return -1;
    }
#endif
}

void update_device_dynamic_data()
{
    pthread_mutex_lock(&update_device_dynamic_data_lock);  // 加锁，进入临界区
    qrzl_log("update_device_dynamic_data: 开始更新动态数据");
    int ret;

    //nv_update_net_cesq_info();

    char net_disconn[2] = {0};
    ret = cfg_get_item("qrzl_user_net_disconn", net_disconn, sizeof(net_disconn));
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.user_net_disconn = atoi(net_disconn);
    }

    // update_battery_vol_percent();
    // cfg_get_item("battery_vol_percent", g_qrzl_device_dynamic_data.remain_power, sizeof(g_qrzl_device_dynamic_data.remain_power));
    // update_board_temperature();
    // cfg_get_item("board_temperature", g_qrzl_device_dynamic_data.board_temperature, sizeof(g_qrzl_device_dynamic_data.board_temperature));
    // qrzl_log("board_temperature: %s", g_qrzl_device_dynamic_data.board_temperature);

    /* 下面是流量相关的 */
    char realtime_tx_thrpt[21] = {0};
    ret = cfg_get_item("realtime_tx_thrpt", realtime_tx_thrpt, 21);
    if (ret == 0)
    {
        uint64_t tx_bytes = 0L;
        tx_bytes = atoll(realtime_tx_thrpt);
        g_qrzl_device_dynamic_data.up_speed_bps = (tx_bytes * 8);
    }
    qrzl_log("up_speed_bps: %lld", g_qrzl_device_dynamic_data.up_speed_bps);

    char realtime_rx_thrpt[21] = {0};
    ret = cfg_get_item("realtime_rx_thrpt", realtime_rx_thrpt, 21);
    if (ret == 0)
    {
        uint64_t rx_bytes = 0L;
        rx_bytes = atoll(realtime_tx_thrpt);
        g_qrzl_device_dynamic_data.down_speed_bps = (rx_bytes * 8);
    }
    qrzl_log("down_speed_bps: %lld", g_qrzl_device_dynamic_data.down_speed_bps);

    char flux_day_total_bytes[21] = {0};
    ret = cfg_get_item("flux_day_total", flux_day_total_bytes, 21);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.flux_day_total_bytes = atoll(flux_day_total_bytes);
    }
    qrzl_log("flux_day_total_bytes: %lld", g_qrzl_device_dynamic_data.flux_day_total_bytes);

    char flux_month_total_bytes[21] = {0};
    ret = cfg_get_item("flux_month_total", flux_month_total_bytes, 21);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.flux_month_total_bytes = atoll(flux_month_total_bytes);
    }
    qrzl_log("flux_month_total_bytes: %lld", g_qrzl_device_dynamic_data.flux_month_total_bytes);

    char realtime_tx_bytes[21] = {0};
    ret = cfg_get_item("realtime_tx_bytes", realtime_tx_bytes, 21);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.realtime_tx_bytes = atoll(realtime_tx_bytes);
    }
    qrzl_log("realtime_tx_bytes: %lld", g_qrzl_device_dynamic_data.realtime_tx_bytes);

    char realtime_rx_bytes[21] = {0};
    ret = cfg_get_item("realtime_rx_bytes", realtime_rx_bytes, 21);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.realtime_rx_bytes = atoll(realtime_rx_bytes);
    }
    qrzl_log("realtime_rx_bytes: %lld", g_qrzl_device_dynamic_data.realtime_rx_bytes);

    char realtime_total_bytes[21] = {0};
    ret = cfg_get_item("CTotal_vol", realtime_total_bytes, 21);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.realtime_total_bytes = atoll(realtime_total_bytes);
    }
    qrzl_log("realtime_total_bytes: %lld", g_qrzl_device_dynamic_data.realtime_total_bytes);

    // /* SIM卡相关 */
    // int current_sim_num = get_device_current_sim_index();
    // if (current_sim_num == 0) {
    //     snprintf(g_qrzl_device_dynamic_data.current_sim, sizeof(g_qrzl_device_dynamic_data.current_sim), "%s", "RSIM_only");
    // } else if (current_sim_num == 1) {
    //     snprintf(g_qrzl_device_dynamic_data.current_sim, sizeof(g_qrzl_device_dynamic_data.current_sim), "%s", "ESIM1_only");
    // } else if (current_sim_num == 2) {
    //     snprintf(g_qrzl_device_dynamic_data.current_sim, sizeof(g_qrzl_device_dynamic_data.current_sim), "%s", "ESIM2_only");
    // } else {
    //     snprintf(g_qrzl_device_dynamic_data.current_sim, sizeof(g_qrzl_device_dynamic_data.current_sim), "%s", "");
    // }
    
    qrzl_log("current_sim: %s", g_qrzl_device_dynamic_data.current_sim);

    /* WiFi相关 */
    char wifi_enabled[2];
    ret = cfg_get_item("wifiEnabled", wifi_enabled, 2);
    if (ret == 0 && strcmp(wifi_enabled, "1") == 0)
    {
        g_qrzl_device_dynamic_data.wifi_enable = 1;
    }
    else
    {
        g_qrzl_device_dynamic_data.wifi_enable = 0;
    }
    qrzl_log("wifi_enable: %d", g_qrzl_device_dynamic_data.wifi_enable);

    char wifi_hide[2];
    ret = cfg_get_item("HideSSID", wifi_hide, 2);
    if (ret == 0 && strcmp(wifi_hide, "1") == 0)
    {
        g_qrzl_device_dynamic_data.wifi_hide = 1;
    }
    else
    {
        g_qrzl_device_dynamic_data.wifi_hide = 0;
    }
    qrzl_log("wifi_hide: %d", g_qrzl_device_dynamic_data.wifi_hide);

    cfg_get_item("SSID1", g_qrzl_device_dynamic_data.wifi_ssid, 64);
    qrzl_log("wifi_ssid: %s", g_qrzl_device_dynamic_data.wifi_ssid);

    cfg_get_item("WPAPSK1", g_qrzl_device_dynamic_data.wifi_key, 64);
    qrzl_log("wifi_key: %s", g_qrzl_device_dynamic_data.wifi_key);

    cfg_get_item("WPAPSK1_encode", g_qrzl_device_dynamic_data.wifi_key_base64, 256);
    qrzl_log("wifi_key_base64: %s", g_qrzl_device_dynamic_data.wifi_key_base64);

    cfg_get_item("EncrypType", g_qrzl_device_dynamic_data.wifi_encryp_type, 32);
    qrzl_log("wifi_encryp_type: %s", g_qrzl_device_dynamic_data.wifi_encryp_type);

    cfg_get_item("AuthMode", g_qrzl_device_dynamic_data.wifi_auth_mode, 32);
    qrzl_log("wifi_auth_mode: %s", g_qrzl_device_dynamic_data.wifi_auth_mode);

    char acl_mode[2] = {0};
    ret = cfg_get_item("ACL_mode", acl_mode, 2);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.wifi_filter_type = atoi(acl_mode);
    }
    qrzl_log("wifi_filter_type: %d", g_qrzl_device_dynamic_data.wifi_filter_type);

    cfg_get_item("wifi_mac_black_list", g_qrzl_device_dynamic_data.mac_black_list, 180);
    qrzl_log("mac_black_list: %s", g_qrzl_device_dynamic_data.mac_black_list);
    cfg_get_item("wifi_mac_white_list", g_qrzl_device_dynamic_data.mac_white_list, 180);
    qrzl_log("mac_white_list: %s", g_qrzl_device_dynamic_data.mac_white_list);

    char sta_count[3] = {0};
    ret = cfg_get_item("sta_count", sta_count, 3);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.conn_num = atoi(sta_count);
    }
    qrzl_log("conn_num: %d", g_qrzl_device_dynamic_data.conn_num);

    char max_access_num_c[3] = {0};
    ret = cfg_get_item("MAX_Access_num", max_access_num_c, 3);
    if (ret == 0)
    {
        g_qrzl_device_dynamic_data.max_access_num = atoi(max_access_num_c);
    }
    qrzl_log("max_access_num: %d", g_qrzl_device_dynamic_data.max_access_num);


    /* web相关 */
    cfg_get_item("admin_Password", g_qrzl_device_dynamic_data.web_password, 64);
    qrzl_log("web_password: %s", g_qrzl_device_dynamic_data.web_password);

    /* 网络、LTE 相关 */
    cfg_get_item("wan_ipaddr", g_qrzl_device_dynamic_data.current_wan_ip, 16);
    qrzl_log("current_wan_ip: %s", g_qrzl_device_dynamic_data.current_wan_ip);

    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);
    qrzl_log("iccid: %s", g_qrzl_device_dynamic_data.iccid);
    cfg_get_item("mcc", g_qrzl_device_dynamic_data.mcc, 4);
    qrzl_log("mcc: %s", g_qrzl_device_dynamic_data.mcc);
    cfg_get_item("mnc", g_qrzl_device_dynamic_data.mnc, 4);
    qrzl_log("mnc: %s", g_qrzl_device_dynamic_data.mnc);
    cfg_get_item("sim_imsi", g_qrzl_device_dynamic_data.imsi, 32);
    qrzl_log("imsi: %s", g_qrzl_device_dynamic_data.imsi);

    cfg_get_item("cell_id", g_qrzl_device_dynamic_data.cid, 16);
    qrzl_log("cid: %s", g_qrzl_device_dynamic_data.cid);
    cfg_get_item("tac_code", g_qrzl_device_dynamic_data.tac, sizeof(g_qrzl_device_dynamic_data.tac));
    qrzl_log("tac: %s", g_qrzl_device_dynamic_data.tac);
    cfg_get_item("lac_code", g_qrzl_device_dynamic_data.lac, sizeof(g_qrzl_device_dynamic_data.lac));
    qrzl_log("lac: %s", g_qrzl_device_dynamic_data.lac);
    cfg_get_item("sinr", g_qrzl_device_dynamic_data.sinr, sizeof(g_qrzl_device_dynamic_data.sinr));
    qrzl_log("sinr: %s", g_qrzl_device_dynamic_data.sinr);
    cfg_get_item("rssi", g_qrzl_device_dynamic_data.rssi, 16);
    qrzl_log("rssi: %s", g_qrzl_device_dynamic_data.rssi);

    ret = cfg_get_item("current_net_band", g_qrzl_device_dynamic_data.net_band, 10);
    if (ret == 0)
    {
        remove_spaces(g_qrzl_device_dynamic_data.net_band);
    }
    qrzl_log("net_band: %s", g_qrzl_device_dynamic_data.net_band);

    cfg_get_item("lte_rsrp", g_qrzl_device_dynamic_data.lte_rsrp, sizeof(g_qrzl_device_dynamic_data.lte_rsrp));
    qrzl_log("lte_rsrp: %s", g_qrzl_device_dynamic_data.lte_rsrp);

    cfg_get_item("rsrq", g_qrzl_device_dynamic_data.rsrq, sizeof(g_qrzl_device_dynamic_data.lte_rsrp));
    qrzl_log("rsrq: %s", g_qrzl_device_dynamic_data.rsrq);
    
    char slot_esim1_is_enable[2] = {0};
    char slot_esim2_is_enable[2] = {0};
    char slot_esim3_is_enable[2] = {0};

    if( 0 == cfg_get_item("slot_esim1_is_enable", slot_esim1_is_enable, sizeof(slot_esim1_is_enable)) ) {
        g_qrzl_device_dynamic_data.slot_esim1_is_enable = atoi(slot_esim1_is_enable);
        qrzl_log("slot_esim1_is_enable: %d", g_qrzl_device_dynamic_data.slot_esim1_is_enable);
    }

    if( 0 == cfg_get_item("slot_esim2_is_enable", slot_esim2_is_enable, sizeof(slot_esim2_is_enable)) ) {
        g_qrzl_device_dynamic_data.slot_esim2_is_enable = atoi(slot_esim2_is_enable);
        qrzl_log("slot_esim2_is_enable: %d", g_qrzl_device_dynamic_data.slot_esim2_is_enable);
    }

    if( 0 == cfg_get_item("slot_esim3_is_enable", slot_esim3_is_enable, sizeof(slot_esim3_is_enable)) ) {
        g_qrzl_device_dynamic_data.slot_esim3_is_enable = atoi(slot_esim3_is_enable);
        qrzl_log("slot_esim3_is_enable: %d", g_qrzl_device_dynamic_data.slot_esim3_is_enable);
    }

    pthread_mutex_unlock(&update_device_dynamic_data_lock);  // 解锁，离开临界区
}

EsimFluxStat get_esim_fluxstat()
{
    EsimFluxStat esim_flux_stat = {};
    memset(&esim_flux_stat, 0, sizeof(EsimFluxStat));
    
    char rsim_flux_total[128] = {0};
    char rsim_flux_day_total[128] = {0};
    char rsim_flux_month_total[128] = {0};

	char esim1_flux_total[128] = {0};
    char esim1_flux_day_total[128] = {0};
    char esim1_flux_month_total[128] = {0};

	char esim2_flux_total[128] = {0};
	char esim2_flux_day_total[128] = {0};
	char esim2_flux_month_total[128] = {0};

    cfg_get_item("rsim_flux_total", rsim_flux_total, 128);
    cfg_get_item("rsim_flux_day_total", rsim_flux_day_total, 128);
    cfg_get_item("rsim_flux_month_total", rsim_flux_month_total, 128);

    cfg_get_item("esim1_flux_total", esim1_flux_total, 128);
    cfg_get_item("esim1_flux_day_total", esim1_flux_day_total, 128);
    cfg_get_item("esim1_flux_month_total", esim1_flux_month_total, 128);

    cfg_get_item("esim2_flux_total", esim2_flux_total, 128);
    cfg_get_item("esim2_flux_day_total", esim2_flux_day_total, 128);
    cfg_get_item("esim2_flux_month_total", esim2_flux_month_total, 128);

    esim_flux_stat.esim1_flux_total = atoll(esim1_flux_total);
    esim_flux_stat.esim1_flux_day_total = atoll(esim1_flux_day_total);
    esim_flux_stat.esim1_flux_month_total = atoll(esim1_flux_month_total);
    
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    
    esim_flux_stat.esim2_flux_total = atoll(rsim_flux_total);
    esim_flux_stat.esim2_flux_day_total = atoll(rsim_flux_day_total);
    esim_flux_stat.esim2_flux_month_total = atoll(rsim_flux_month_total);
#else
    esim_flux_stat.rsim_flux_total = atoll(rsim_flux_total);
    esim_flux_stat.rsim_flux_day_total = atoll(rsim_flux_day_total);
    esim_flux_stat.rsim_flux_month_total = atoll(rsim_flux_month_total);

    esim_flux_stat.esim2_flux_total = atoll(esim2_flux_total);
    esim_flux_stat.esim2_flux_day_total = atoll(esim2_flux_day_total);
    esim_flux_stat.esim2_flux_month_total = atoll(esim2_flux_month_total);
#endif
    

    



    qrzl_log("rsim_flux_total: %lld, esim1_flux_total: %lld, esim2_flux_total: %lld, rsim_flux_day_total: %lld, esim1_flux_day_total: %lld, esim2_flux_day_total: %lld, rsim_flux_month_total: %lld, esim1_flux_month_total: %lld, esim2_flux_month_total: %lld",
             esim_flux_stat.rsim_flux_total, esim_flux_stat.esim1_flux_total, esim_flux_stat.esim2_flux_total,
             esim_flux_stat.rsim_flux_day_total, esim_flux_stat.esim1_flux_day_total, esim_flux_stat.esim2_flux_day_total,
             esim_flux_stat.rsim_flux_month_total, esim_flux_stat.esim1_flux_month_total, esim_flux_stat.esim2_flux_month_total);
    return esim_flux_stat;
}

/**
 * 获取当前SIM卡的卡槽号，是通过全局变量 g_qrzl_device_dynamic_data.current_sim 中的值来直接判断，不具备实时性，谨慎使用
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index_by_data()
{
    if (strcmp("ESIM1_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 1;
    } 
    else if (strcmp("ESIM2_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 2;
    }
    else if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 0;
    }
    return -1;
}

/**
 * 限制上下行网速, 单位Kbps
 */
int limit_net_speed(uint64_t up_limit, uint64_t down_limit)
{
#ifdef QRZL_DEVICE_CONTROL_ENABLE
    char qrzl_limit_down_speed[21] = {0};
    char qrzl_limit_up_speed[21] = {0};
    cfg_get_item("qrzl_limit_down_speed", qrzl_limit_down_speed, sizeof(qrzl_limit_down_speed));
    cfg_get_item("qrzl_limit_up_speed", qrzl_limit_up_speed, sizeof(qrzl_limit_up_speed));
    uint64_t limit_down_speed_num = strtoull(qrzl_limit_down_speed, NULL, 10);
    uint64_t limit_up_speed_num = strtoull(qrzl_limit_up_speed, NULL, 10);
    if (limit_down_speed_num > 0 || limit_up_speed_num > 0) {
        up_limit = limit_up_speed_num;
        down_limit = limit_down_speed_num;
    }
#endif
    if (up_limit < 0 || down_limit < 0)
    {
        qrzl_err("limit_net_speed vaule error");
        return -1;
    }
    // 利用/sbin/tc_tbf.sh脚本限速，nv的单位是byte 
    uint64_t tc_uplink_byte = 0L;
    uint64_t tc_downlink_byte = 0L;
    cfg_set("tc_enable", "1");
    tc_uplink_byte = (up_limit * 1000) / 8; 
    tc_downlink_byte = (down_limit * 1000) / 8;
    
    char tc_uplink[21] = {0};
    char tc_downlink[21] = {0};
    snprintf(tc_uplink, sizeof(tc_uplink), "%lld", tc_uplink_byte);
    snprintf(tc_downlink, sizeof(tc_downlink), "%lld", tc_downlink_byte);
    cfg_set("tc_uplink", tc_uplink);
    cfg_set("tc_downlink", tc_downlink);
    system("/sbin/tc_tbf.sh");
    return 0;
}

/**
 * 获取上行限速值，单位Kbps
 */
uint64_t get_up_limit_net_speed()
{
    int ret;
    uint64_t up_limit = 0L;
    char up_limit_str[21] = {0};
    ret = cfg_get_item("tc_uplink", up_limit_str, sizeof(up_limit_str));
    if (ret != 0)
    {
        return up_limit;
    }
    up_limit = atoll(up_limit_str);
    return (up_limit * 8) / 1000;
}

/**
 * 获取上行限速值，单位Kbps
 */
uint64_t get_down_limit_net_speed()
{
    int ret;
    uint64_t down_limit = 0L;
    char down_limit_str[21] = {0};
    ret = cfg_get_item("tc_downlink", down_limit_str, sizeof(down_limit_str));
    if (ret != 0)
    {
        return down_limit;
    }
    down_limit = atoll(down_limit_str);
    return (down_limit * 8) / 1000;
}

int init_wifi_config_value(struct wifi_config_t *wifi_config)
{
    wifi_config->max_access_num = g_qrzl_device_dynamic_data.max_access_num;
    wifi_config->enable = g_qrzl_device_dynamic_data.wifi_enable;
    wifi_config->hide = g_qrzl_device_dynamic_data.wifi_hide;
    strlcpy(wifi_config->ssid, g_qrzl_device_dynamic_data.wifi_ssid, sizeof(wifi_config->ssid));
    strlcpy(wifi_config->key, g_qrzl_device_dynamic_data.wifi_key, sizeof(wifi_config->key));
    strlcpy(wifi_config->auth_mode, g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(wifi_config->auth_mode));
    return 0;
}

int update_wifi_by_config(const struct wifi_config_t *wifi_config)
{   
    qrzl_log("start update_wifi_by_config");
    unsigned int wifi_set_flags = 0;
    char wifi_set_flags_str[8] = {0};
    if (wifi_config->enable != g_qrzl_device_dynamic_data.wifi_enable)
    {
        char wifi_enable_c[2] = {0};
        snprintf(wifi_enable_c, sizeof(wifi_enable_c), "%d", wifi_config->enable);
        cfg_set("wifiEnabled", wifi_enable_c);
        char flag_str[8]={0}; 
        if (wifi_config->enable)
        {
            qrzl_log("enable wifi");
            snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_OPEN); 
        }
        else
        {
            qrzl_log("disable WiFi");
            snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_CLOSE); 
            ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, flag_str, 0); 
            return 0;
        }
        
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, flag_str, 0); 
    }

    if (strlen(wifi_config->ssid) > 0 && strcmp(wifi_config->ssid, g_qrzl_device_dynamic_data.wifi_ssid) != 0) 
    {
        qrzl_log("update wifi ssid");
        wifi_set_flags |= ZTE_WLAN_SSID_SET;
        cfg_set("SSID1", wifi_config->ssid);
        wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET; // ssid更新，WiFi密码也一定要更新
    }

    if (strcmp(wifi_config->auth_mode, g_qrzl_device_dynamic_data.wifi_auth_mode) != 0)
    {
        qrzl_log("update wifi auth");
        if (strlen(wifi_config->key) == 0 && strcmp(wifi_config->auth_mode, "OPEN") != 0)
        {
            qrzl_log("在WiFi非开放模式下，密码不能为空");
        }
        else
        {
            wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET;
            if (strcmp(wifi_config->auth_mode, "OPEN") == 0)
            {
                cfg_set("AuthMode", "OPEN");
                cfg_set("EncrypType", "NONE");
                cfg_set("WPAPSK1", "");
                cfg_set("WPAPSK1_encode", "");
                cfg_set("cipher_str", "2");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA2PSK") == 0)
            {
                cfg_set("AuthMode", "WPA2PSK");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
            else if (strcmp(wifi_config->auth_mode, "WPAPSKWPA2PSK") == 0)
            {
                cfg_set("AuthMode", "WPAPSKWPA2PSK");
                cfg_set("EncrypType", "TKIPAES");
                cfg_set("cipher_str", "2");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA3Personal") == 0)
            {
                cfg_set("AuthMode", "WPA3Personal");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA2WPA3") == 0)
            {
                cfg_set("AuthMode", "WPA2WPA3");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
        }
    }

    if (strlen(wifi_config->key) > 7 && strcmp(wifi_config->key, g_qrzl_device_dynamic_data.wifi_key) != 0) 
    {
        qrzl_log("update wifi key");
        qrzl_log("wifi_config->key: %s", wifi_config->key);
        char *key_encode_str = NULL;

        key_encode_str = qrzl_base64_encode(wifi_config->key, strlen(wifi_config->key));
        if (NULL == key_encode_str)
        {
            qrzl_err("key_encode is NULL");
            free(key_encode_str);
        } 
        else
        {
            qrzl_log("encode key: %s", key_encode_str);
            cfg_set("WPAPSK1_encode", key_encode_str);
            free(key_encode_str);

            wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET;
            cfg_set("WPAPSK1", wifi_config->key);
        }
    }

    if (wifi_config->max_access_num > 0 && wifi_config->max_access_num != g_qrzl_device_dynamic_data.max_access_num)
    {
        qrzl_log("update wifi max access");
        char max_access_num[3] = {0};
        snprintf(max_access_num, sizeof(max_access_num), "%d", wifi_config->max_access_num);
        cfg_set("MAX_Access_num", max_access_num);
        cfg_set("MAX_Access_num_user_set", "1");
        wifi_set_flags |= ZTE_WLAN_MAX_ACCESS_NUM_SET;
    }
    if (wifi_config->hide != g_qrzl_device_dynamic_data.wifi_hide && wifi_config->hide >= 0 && wifi_config->hide <= 1)
    {
        qrzl_log("update wifi is hide");
        char wifi_hide_c[2] = {0};
        snprintf(wifi_hide_c, sizeof(wifi_hide_c), "%d", wifi_config->hide);
        cfg_set("HideSSID", wifi_hide_c);
        wifi_set_flags |= ZTE_WLAN_BROADCAST_SET;
    }

    if (wifi_set_flags != 0) {
        qrzl_log("WiFi参数有修改，开始更新WiFi");
        snprintf(wifi_set_flags_str, sizeof(wifi_set_flags_str) - 1, "%u", wifi_set_flags); 
        cfg_set("wifi_set_flags", wifi_set_flags_str);
 
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_CFG_AP, 0, NULL, 0);
    #ifdef JCV_HW_MZ801_V1_2
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_MMI, MSG_CMD_MODIFY_SSID_KEY, 0, NULL, 0);
    #endif
    }
    return 0;
}

int wifi_switch(int status)
{
    int ret;
    char wifi_enabled[2];
    ret = cfg_get_item("wifiEnabled", wifi_enabled, 2);
    int wifi_enabled_num = atoi(wifi_enabled);
    if (status == wifi_enabled_num)
    {
        qrzl_log("WiFi状态已经是目标状态");
        return 1;
    }
    char flag_str[8]={0}; 
    if (status == 1)
    {
        qrzl_log("开启wifi");
        cfg_set("wifiEnabled", "1");
        snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_OPEN); 
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, flag_str, 0); 
    }
    else
    {
        qrzl_log("关闭WiFi");
        cfg_set("wifiEnabled", "0");
        snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_CLOSE); 
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, flag_str, 0); 
        
    }
    return 0;
}

int restart_device()
{
    cfg_save();
    return restart_request(MODULE_ID_CLOUD_VSIM_APP);
}

int reset_device()
{
    return reset_request(MODULE_ID_CLOUD_VSIM_APP);
}

int shutdown_device()
{
    cfg_save();
    return poweroff_request(MODULE_ID_CLOUD_VSIM_APP);
}

int update_web_password(const char *password)
{
    if (password != NULL && strlen(password) > 3)
    {
        return cfg_set("admin_Password", password);
    }
    return 0;
}

int init_mac_filter_config_value(struct mac_filter_config_t *mac_filter_config)
{
    mac_filter_config->wifi_filter_type = g_qrzl_device_dynamic_data.wifi_filter_type;
    strlcpy(mac_filter_config->mac_black_list, g_qrzl_device_dynamic_data.mac_black_list, sizeof(mac_filter_config->mac_black_list));
    strlcpy(mac_filter_config->mac_white_list, g_qrzl_device_dynamic_data.mac_white_list, sizeof(mac_filter_config->mac_white_list));
    return 0;
}

int update_mac_filter_by_config(const struct mac_filter_config_t *mac_filter_config)
{
    qrzl_log("start update_mac_filter_by_config");
    unsigned int wifi_set_flags = 0;
	char wifi_set_flags_str[20] = {0};
    char acl_mode[2] = {0};
    if (mac_filter_config->wifi_filter_type != g_qrzl_device_dynamic_data.wifi_filter_type)
    {
        if (mac_filter_config->wifi_filter_type >= 0 && mac_filter_config->wifi_filter_type <= 2)
        {
            snprintf(acl_mode, sizeof(acl_mode), "%d", mac_filter_config->wifi_filter_type);
            cfg_set("ACL_mode", acl_mode);
            wifi_set_flags |= ZTE_WLAN_ACL_SET;
        }
    }
    if (strcmp(mac_filter_config->mac_black_list, g_qrzl_device_dynamic_data.mac_black_list) != 0)
    {
        cfg_set("wifi_mac_black_list", mac_filter_config->mac_black_list);
        wifi_set_flags |= ZTE_WLAN_ACL_SET;
    }
    if (strcmp(mac_filter_config->mac_white_list, g_qrzl_device_dynamic_data.mac_white_list) != 0)
    {
        cfg_set("wifi_mac_white_list", mac_filter_config->mac_white_list);
        wifi_set_flags |= ZTE_WLAN_ACL_SET;
    }
    if (wifi_set_flags != 0)
    {
        snprintf(wifi_set_flags_str, sizeof(wifi_set_flags_str) - 1, "%u", wifi_set_flags);
        cfg_set("wifi_set_flags", wifi_set_flags_str);
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_MAC, 0, NULL, 0);
    }
    return 0;
}

int set_lte_net_band(int band)
{
	int band_bitsp[8] = {0};
    qrzl_log("准备设置BAND = %d\n", band);
	char at_str_tmp[128] = {0};
	if (band <= 0)
	{
		// 自动
		sprintf(at_str_tmp, "AT+ZLTEBAND=\r");
	} 
	else 
	{
		int jump_num = (band - 1) / 8; // 由于设置band是按bit位设置，并且是每8位分割，这个变量是表面要跳过多少个分割
		int bit_num = (band - 1) % 8; // 这个是每8位后，bit位最后要设置的band
		band_bitsp[jump_num] = 1;
		band_bitsp[jump_num] = band_bitsp[jump_num] << bit_num;
		sprintf(at_str_tmp, "AT+ZLTEBAND=%d,%d,%d,%d,%d,%d,%d,%d\r\n",
			band_bitsp[0], band_bitsp[1], band_bitsp[2], band_bitsp[3],
			band_bitsp[4], band_bitsp[5], band_bitsp[6], band_bitsp[7]);
		qrzl_log("at_str_tmp = %s\n", at_str_tmp);
	}
	int ret = get_modem_info2(at_str_tmp, NULL, NULL, 0, 10);
	if (ret != 0) {
		qrzl_err("set net band err\n");
		return -1;
	}
	char cfg_str_tmp[128] = {0};
	sprintf(cfg_str_tmp, "%d", band);
	cfg_set(NV_SELECTED_NET_BAND, cfg_str_tmp);
	cfg_save();
    return 0;
}

/**
 * 这个函数的切卡sim_type代表实际物理通道对应的卡，与逻辑切卡可能存在不一样，对接客户的时候时候尽量不要使用这个函数，
 * 而是使用switch_sim_card 或者 switch_sim_card_not_restart
 * sim_type: 0 外卡，1 esim1， 2esim2
 * 
 * 0 切卡成功
 * 2 切卡失败
 * -1 AT失败，NV设置为目标值后，重启设备
 * 
 * 这个方法的代码可能会看着有点奇怪，因为在执行切卡操作时会涉及到AT命令的执行，而AT命令的执行有可能会失败
 * 所以如果AT命令失败，会重试3次，如果3次都是失败，那么会将NV设置为目标值，然后重启设备
 */
int switch_sim_card_core(int sim_type)
{
    qrzl_log("开始切卡至物理通道卡: %d", sim_type);
    pthread_mutex_lock(&switch_sim_card_lock);  // 加锁，进入临界区
    int ret;
    char now_nv_sim_select[16] = {0};
    ret = cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
    if (ret != 0)
    {
        qrzl_err("无法获取 sim_select value");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 2;
    }

    // 中芯微卡有两个通道，分别对应实体卡通道和ESIM卡通道,在当前设备对应的版本 0 是实体卡, 3是ESIM卡
    int card_switch_type = 0;
    char *p[] = {&card_switch_type};
    ret = get_modem_info2("AT+ZCARDSWITCH?\r", "%d", (void**)p, 0, 10);
	if (ret != 0) 
	{
		qrzl_err("获取当前卡通道失败 err: %d\n", ret);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return -1;
	}
    qrzl_log("当前sim卡通道值: %d", card_switch_type);

    // DZ802 卡通道 0 是esim卡，3是sim卡
#if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5)
    int i;
    if (sim_type == 0) {
        if (card_switch_type != 3) {
            qrzl_log("准备切换到实卡");

            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=3\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", "RSIM_only");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;
    }
    else if (sim_type == 1 || sim_type == 2)
    {
        char esim_nv_value[64] = {0};
        if (sim_type == 1)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM1_only");
        }
        else if (sim_type == 2)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM2_only");
        }
        qrzl_log("要设置的ESIM卡为: %s", esim_nv_value);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        qrzl_log("准备切换到ESIM卡通道");
        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+ZCARDSWITCH=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        if (sim_type == 1) {
            qrzl_log("准备切到ESIM1");
            system("/bin/echo 0 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        else if (sim_type == 2) {
            qrzl_log("准备切到ESIM2");
            system("/bin/echo 1 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", esim_nv_value);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;    
    }
#else
    int i;
    if (sim_type == 0) {
        if (card_switch_type != 0) {
            qrzl_log("准备切换到实卡");

            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", "RSIM_only");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;
    }
    else if (sim_type == 1 || sim_type == 2)
    {
        char esim_nv_value[64] = {0};
        if (sim_type == 1)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM1_only");
        }
        else if (sim_type == 2)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM2_only");
        }
        qrzl_log("要设置的ESIM卡为: %s", esim_nv_value);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);


        if (card_switch_type != 3) {
            qrzl_log("准备切换到ESIM卡通道");
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=3\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", esim_nv_value);
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
        }

        if (sim_type == 1) {
            qrzl_log("准备切到ESIM1");
            system("/bin/echo 0 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        else if (sim_type == 2) {
            qrzl_log("准备切到ESIM2");
            system("/bin/echo 1 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", esim_nv_value);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;    
    }
#endif

    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
    return 2;
}

/**
 * 切卡
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card(int sim_type)
{
    int current_sim_index = get_device_current_sim_index();
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (sim_type == 2) {
        sim_type = 0;
    }
    if (current_sim_index == 2) {
        current_sim_index = 0;
    }
#endif

    if (current_sim_index != sim_type) {
        if (switch_sim_card_core(sim_type) == 0)
        {
            qrzl_log("切卡成功,准备重启");
            restart_device();
        }
    } else {
        qrzl_log("已是目标卡，无需切卡");
        char now_nv_sim_select[16] = {0};
        cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
        if (current_sim_index == 0 && strcmp(RSIM_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", RSIM_ONLY_STR);
        } else if (current_sim_index == 1 && strcmp(ESIM1_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM1_ONLY_STR);
        } else if (current_sim_index == 2 && strcmp(ESIM2_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM2_ONLY_STR);
        }
    }
    
    return 0;
}

/**
 * 切卡
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card_not_restart(int sim_type)
{
    int current_sim_index = get_device_current_sim_index();
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (sim_type == 2) {
        sim_type = 0;
    }
    if (current_sim_index == 2) {
        current_sim_index = 0;
    }
#endif
    
    qrzl_log("current_sim_index: %d, sim_type: %d", current_sim_index, sim_type);
    if (current_sim_index != sim_type) {
        return switch_sim_card_core(sim_type);
    } else {
        qrzl_log("已是目标卡，无需切卡");
        char now_nv_sim_select[16] = {0};
        cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
        if (current_sim_index == 0 && strcmp(RSIM_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", RSIM_ONLY_STR);
        } else if (current_sim_index == 1 && strcmp(ESIM1_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM1_ONLY_STR);
        } else if (current_sim_index == 2 && strcmp(ESIM2_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM2_ONLY_STR);
        }
    }
    return 0;
}

int get_csq()
{
    int rssi = atoi(g_qrzl_device_dynamic_data.rssi);
    if (rssi == 0)
    {
        return 99;
    }
    int csq;
    if(rssi < -113)
        csq = 0;
    else if(rssi == -111)
        csq = 1;
    else if((rssi>=-109)&&(rssi<=-53))
    {
        csq = (rssi + 113)/2;
    }
    else if((rssi>= -51)&&(rssi<0))
    {
        csq = 31;
    }
    else
        csq = 99;
    return csq;
}

// 0没有匹配的运营商, 1 china_mobile, 2 china_united, 3 china_telecom
int get_isp_by_imsi(const char *imsi)
{
    // 检查IMSI是否为空或长度是否不足
    if (imsi == NULL || strlen(imsi) < 5) {
        return 0;
    }

    // 根据IMSI的前缀判断运营商
    if (strncmp(imsi, "46000", 5) == 0 || strncmp(imsi, "46002", 5) == 0 || strncmp(imsi, "46004", 5) == 0 || strncmp(imsi, "46007", 5) == 0
        || strncmp(imsi, "46008", 5) == 0 || strncmp(imsi, "460013", 5) == 0 || strncmp(imsi, "46024", 5) == 0) {
        return 1; // 中国移动
    } else if (strncmp(imsi, "46001", 5) == 0 || strncmp(imsi, "46006", 5) == 0 || strncmp(imsi, "46009", 5) == 0 || strncmp(imsi, "46010", 5) == 0) {
        return 2; // 中国联通
    } else if (strncmp(imsi, "46003", 5) == 0 || strncmp(imsi, "46005", 5) == 0 || strncmp(imsi, "46011", 5) == 0 || strncmp(imsi, "46012", 5) == 0) {
        return 3; // 中国电信
    } else {
        return 0; // 未匹配
    }
}

int get_isp_name_cn_by_imsi(const char *imsi, char *mno, size_t mno_size)
{
    int isp = get_isp_by_imsi(imsi);
    if (isp == 1)
    {
        snprintf(mno, mno_size, "%s", "中国移动");
    }
    else if (isp == 2)
    {
        snprintf(mno, mno_size, "%s", "中国联通");
    }
    else if (isp == 3)
    {
        snprintf(mno, mno_size, "%s", "中国电信");
    }
    else 
    {
        snprintf(mno, mno_size, "%s", "未知");
    }
    return 0;
}

int nv_set_esim_mno(int esim_num, const char *imsi)
{
    char mno[20] = {0};
    int isp = get_isp_by_imsi(imsi);
    if (isp == 1)
    {
        snprintf(mno, sizeof(mno), "%s", "china_mobile");
    }
    else if (isp == 2)
    {
        snprintf(mno, sizeof(mno), "%s", "china_united");
    }
    else if (isp == 3)
    {
        snprintf(mno, sizeof(mno), "%s", "china_telecom");
    }
    else 
    {
        return -1;    
    }

    if (esim_num == 1) {
        return cfg_set("esim1_mno", mno);
    } else if (esim_num == 2) {
        return cfg_set("esim2_mno", mno);
    } else if (esim_num == 3) {
        return cfg_set("esim3_mno", mno);
    }
    
    
    return -1;
}

// 检测单个 IP 地址是否可达
static int check_network_with_ping(const char *host, const char *interface) {
    char command[256];
    if (interface != NULL) {
        // 指定了接口，使用 -I 参数
        snprintf(command, sizeof(command), "ping -I %s -c 10 -W 1 %s > /dev/null 2>&1", interface, host);
    } else {
        // 没有指定接口，使用默认路由，不使用 -I 参数
        snprintf(command, sizeof(command), "ping -c 10 -W 1 %s > /dev/null 2>&1", host);
    }
    int ret = system(command);
    return ret == 0; // 返回 0 表示网络可达
}

// 检测多个 IP 地址，只要一个可达则网络正常
static int check_multiple_ips(const char **hosts, int num_hosts, const char *interface) {
    int i;
    for (i = 0; i < num_hosts; ++i) {
        if (interface != NULL) {
            qrzl_log("start ping %s using interface %s", hosts[i], interface);
        } else {
            qrzl_log("start ping %s using default route", hosts[i]);
        }
        if (check_network_with_ping(hosts[i], interface)) {
            return 1; // 如果任意 IP 可达，则返回 1
        }
        sleep(1);
    }
    return 0; // 所有 IP 都不可达，返回 0
}

/**
 * 获取当前上网状态
 */
char *get_current_net_status() 
{
    int res;
    char ppp_status[32] = {0};
    memset(ppp_status, 0, sizeof(ppp_status));
    res = cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
    if (res != 0) {
        return "0";
    }
    if (strcmp(ppp_status, "ppp_connected") != 0) {
        return "0";
    }
    return "1";
}

/**
 * 设置卡槽状态
 * disable  0：禁用, 1: 启用
 * slot_number  卡槽号：1->ESIM1 2->ESIM2 3->外置卡
 */
void set_slot_state(int disable, int slot_number)
{
    switch (slot_number)
    {
    case 1:
        cfg_set("slot_esim1_is_enable", disable == 0 ? "0" : "1");
        break;
    case 2:
        cfg_set("slot_esim2_is_enable", disable == 0 ? "0" : "1");
        break;
    case 0:
        cfg_set("slot_esim3_is_enable", disable == 0 ? "0" : "1");
        break;
    default:
        break;
    }
}

/**
 * 循环1次通过PING检测网络是否可达（支持指定网卡接口）
 * @param interface 网卡接口名称，如果为NULL则使用默认值
 * @return 0 表示网络可达, -1 表示网络不可达
 */
int check_network_with_interface(const char *interface) {
    const char *dns_hosts[] = {
        "*********", // 阿里云dns
        "************",   // 腾讯dns
        "*********",      // 阿里云备用dns
        "************"      // 腾讯云备用dns
    };
    int i;

    if (interface != NULL) {
        qrzl_log("检查网络连通性，使用接口: %s", interface);
    } else {
        qrzl_log("检查网络连通性，使用默认路由");
    }

    for (i = 0; i < 1; i++) {
        if (check_multiple_ips(dns_hosts, 4, interface) == 1) {
            return 0;
        }
        sleep(1);
    }
    return -1;
}

/**
 * 循环1次通过PING检测网络是否可达（保持向后兼容）
 */
int check_network() {
    return check_network_with_interface(NULL);
}

static void* auto_switch_esim_handler() 
{
    char usb_modetype[32] = {0};
    nv_get_item(NV_RO, "usb_modetype", usb_modetype, sizeof(usb_modetype));
    if (strcmp("user", usb_modetype) != 0)
    {
        qrzl_log("当前不是用户模式，不进行自动切卡");
        return NULL;
    }

	int ret;
	int switch_esim_detection_interval = 30; // 是否切卡检测间隔，单位秒
	char detection_interval_str[21] = {0};
	ret = cfg_get_item("switch_esim_detection_interval", detection_interval_str, sizeof(detection_interval_str));
	if (ret == 0)
	{
		int detection_interval = atoi(detection_interval_str);
		if (detection_interval > 0)
		{
			switch_esim_detection_interval = detection_interval;
		}
	}

	int esim1_status = 1;
	int esim2_status = 1;
	int next_esim_num = 1;
	char ziccid[21] = {0};
    char auto_switch_esim_type[3] = {0};
    int current_sim_index = -1;

    int kuyu_switch_out_card_flag = 0; // 【客户变量】- 酷鱼 切外卡标识

    sleep(20); // 等待网络初始化完成
	while (1)
	{
		sleep(switch_esim_detection_interval);
        memset(auto_switch_esim_type, 0, sizeof(auto_switch_esim_type));
        cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
        if (strcmp("0", auto_switch_esim_type) == 0)
        {
            qrzl_log("auto_switch_esim_type: %s 不开启断网自动切卡服务，开始下一次检测", auto_switch_esim_type);
            continue;
        }

#ifdef QRZL_KUYU_LOGIC_CODE
        qrzl_log("Kuyu -> into out_card priori logic...");
        if((get_device_current_sim_index() != 0) && kuyu_switch_out_card_flag == 0) {
            // 切换至外插卡
            qrzl_log("Kuyu -> current not out_card, begin switch out card.");
            switch_sim_card_not_restart(0);
            kuyu_switch_out_card_flag = 1; // 切过外插卡，每次开机都会默认寻外插卡是否可以上网
            sleep(5);
        }
#endif

		qrzl_log("开始检测esim卡是否有网");
        // 通过PING来检测
		if (check_network() == 0)
		{
            current_sim_index = get_device_current_sim_index();
			if (current_sim_index == 1) {
				g_qrzl_device_dynamic_data.esim1_net_status = 1;
			} else if (current_sim_index == 2) {
				g_qrzl_device_dynamic_data.esim2_net_status = 1;
			} else if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 1;
            }
			continue;
		}

        memset(auto_switch_esim_type, 0, sizeof(auto_switch_esim_type));
        cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
        if (strcmp("0", auto_switch_esim_type) == 0)
        {
            qrzl_log("已关闭自动切卡，无需切卡");
            continue;
        }

        // ping 需要时间，如何云端下发切卡到不能上网的卡，会导致跳过一个卡去切，比如在卡1，云端切到外卡。这个时候ping的话，会以为是卡一没网，然后切到卡2
        current_sim_index = get_device_current_sim_index();

		qrzl_log("当前 %d: 无网络准备切卡", current_sim_index);

        if (strncmp(auto_switch_esim_type, "1", sizeof(auto_switch_esim_type)) == 0) {
            if (current_sim_index == 1) {
                next_esim_num = 2;
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
            } else if(current_sim_index == 2) {
                next_esim_num = 1;
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
            }
        } else if (strncmp(auto_switch_esim_type, "2", sizeof(auto_switch_esim_type)) == 0) {
#ifndef QRZL_CUSTOM_YIMING_LOGIC
            if (current_sim_index == 1) {
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0) {
                    next_esim_num = 2;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0) {
                    next_esim_num = 0;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if(current_sim_index == 2) {
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0) {
                    next_esim_num = 0;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0) {
                    next_esim_num = 1;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0) {
                    next_esim_num = 1;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0) {
                    next_esim_num = 2;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            }
#else 
            /**
             * 伊鸣 自动寻网逻辑 
             *  被禁用的卡不进入寻网逻辑
             */
            if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim1_is_enable == 1) {
                    next_esim_num = 1;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim2_is_enable == 1) {
                    next_esim_num = 2;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if (current_sim_index == 1) {
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim2_is_enable == 1) {
                    next_esim_num = 2;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable == 1) {
                    next_esim_num = 0;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if(current_sim_index == 2) {
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable == 1) {
                    next_esim_num = 0;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim1_is_enable == 1) {
                    next_esim_num = 1;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } 
#endif
        }
        

		switch_sim_card_not_restart(next_esim_num);
		sleep(1);

        sleep(3);
		memset(ziccid, 0, sizeof(ziccid));
		cfg_get_item("ziccid", ziccid, sizeof(ziccid));
        if (strncmp("00000000000000000000", ziccid, sizeof(ziccid))  != 0) {
            cfg_set("modem_main_state", "modem_init_complete");
        }

		cfg_set("user_initiate_disconnect", "0");  //用户主动连接状态，退出干预模式
		ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_AT_CTL, MSG_CMD_PDP_ACT_REQ, 0, NULL, 0);

        char ppp_status[32] = {0};
        cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
        int iConnectStatus = 0;
        if(0 == strcmp(ppp_status, "ppp_disconnected"))
        {
                iConnectStatus = 0;
        }
        else if(0 == strcmp(ppp_status, "ppp_connected"))
        {
                iConnectStatus = 1;
        }
        else if(0 == strcmp(ppp_status, "ppp_connecting"))
        {
                iConnectStatus = 2;
        }
        else if(0 == strcmp(ppp_status, "ppp_disconnecting"))
        {
                iConnectStatus = 3;
        }
        ipc_send_message(MODULE_ID_CLOUD_VSIM_APP, MODULE_ID_MMI, MSG_CMD_CHANNEL_CONNECT_STATUS, sizeof(iConnectStatus), (UCHAR *)&iConnectStatus,0);


		if (strncmp("00000000000000000000", ziccid, sizeof(ziccid))  == 0)
		{
			if (next_esim_num == 1)
			{
				qrzl_log("esim %d iccid = %s, esim1_status=0", next_esim_num, ziccid);
				esim1_status = 0;
			}
			else if (next_esim_num == 2)
			{
				qrzl_log("esim %d iccid = %s, esim2_status=0", next_esim_num, ziccid);
				esim2_status = 0;
			}
		}
		else
		{
            if (strncmp(auto_switch_esim_type, "1", sizeof(auto_switch_esim_type)) == 0)
            {
                if (esim1_status == 0 || esim2_status == 0)
                {
                    qrzl_log("有一个卡读不到，无需再切卡");
                    break;
                }
            }
            else if (strncmp(auto_switch_esim_type, "2", sizeof(auto_switch_esim_type)) == 0)
            {
                if (esim1_status == 0 && esim2_status == 0)
                {
                    qrzl_log("两个卡读不到，无需再切卡");
                    break;
                }
            }
		}
	}
	return NULL;
}


/**
 * 客户定制需求的初始化
 */
void customer_customization_requirements_init()
{
    int ret;
    char qrzl_user_net_disconn[2] = {0};
    ret = cfg_get_item("qrzl_user_net_disconn", qrzl_user_net_disconn, sizeof(qrzl_user_net_disconn));
    if (ret == 0 && strcmp("1", qrzl_user_net_disconn) == 0)
    {
        // 防火墙会在每次开机的时候充值，所以只需要禁用网络时才需要设置
        set_network_br0_disconnect(1);
    }

	// 设备启动时，假定两张卡都有网
	g_qrzl_device_dynamic_data.esim1_net_status = 1;
	g_qrzl_device_dynamic_data.esim2_net_status = 1;
    g_qrzl_device_dynamic_data.esim3_net_status = 1;

    // 启动自动切卡线程，但实际要不要切卡，在线程里面再处理
    int err;
    pthread_t auto_switch_esim_tid;
    err = pthread_create(&auto_switch_esim_tid, NULL, auto_switch_esim_handler, NULL);
    if (err != 0)
    {
        qrzl_err("create auto_switch_esim_handler pthread error code: %d", err);
    }

}

/**
 * 获取本地时间并根据指定格式返回
 * 参数:
 * format: 时间格式字符串 (如 "%Y-%m-%d %H:%M:%S")
 * buffer: 用于存储时间字符串的缓冲区
 * buffer_size: 缓冲区大小
 * 返回值:
 * 0: 成功
 * -1: 失败
*/
int get_local_time(const char *format, char *buffer, size_t buffer_size)
{
    if (format == NULL || buffer == NULL || buffer_size == 0) {
        return -1;
    }

    time_t raw_time;
    struct tm *time_info;

    // 获取当前时间
    time(&raw_time);

    // 转换为本地时间
    time_info = localtime(&raw_time);
    if (time_info == NULL) {
        return -1;
    }

    // 格式化时间字符串
    if (strftime(buffer, buffer_size, format, time_info) == 0) {
        // 缓冲区不足或格式化失败
        return -1;
    }

    return 0;
}

int set_network_br0_disconnect(int status)
{
    char nv_user_net_disconn[] = "qrzl_user_net_disconn";
    
    if (status == 1)
    {
        qrzl_log("set_network_br0_disconnect disable br0 network");
        // 添加规则：禁止 br0 -> wan1 转发
        system("iptables-save | grep -q -- '-A FORWARD -i br0 -o wan1 -j DROP' || iptables -A FORWARD -i br0 -o wan1 -j DROP");
        // 添加规则：禁止 wan1 -> br0 转发
        system("iptables-save | grep -q -- '-A FORWARD -i wan1 -o br0 -j DROP' || iptables -A FORWARD -i wan1 -o br0 -j DROP");

#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
#ifdef QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK
        system("echo timer > /sys/class/leds/modem_g_led/trigger"); // 打开网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#else
        system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
        system("echo 0 > /sys/class/leds/modem_g_led/brightness"); // 关闭网络绿灯
        system("echo none > /sys/class/leds/modem_r_led/trigger"); // 关闭网络红灯定时器
        system("echo 1 > /sys/class/leds/modem_r_led/brightness"); // 打开网络红灯
#endif
#endif
        cfg_set(nv_user_net_disconn, "1");
    }
    else
    {
        qrzl_log("set_network_br0_disconnect enable br0 network");
        system("iptables -D FORWARD -i br0 -o wan1 -j DROP");
        system("iptables -D FORWARD -i wan1 -o br0 -j DROP");

#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
#if defined(QRZL_APP_CUSTOMIZATION_MY) || defined(QRZL_NET_CONNECTED_ALWAYS_GREEN) || defined(QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK)
        system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#else 
        system("echo timer > /sys/class/leds/modem_g_led/trigger"); // 打开网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#endif
        system("echo none > /sys/class/leds/modem_r_led/trigger"); // 关闭网络红灯定时器
        system("echo 0 > /sys/class/leds/modem_r_led/brightness"); // 关闭网络红灯
#endif
        cfg_set(nv_user_net_disconn, "0");
    }
    return 0;
}

int url_encode(const char *str, char *encoded)
{
    if (str == NULL || encoded == NULL) {
        return -1;
    }

    size_t len = strlen(str);

    char *p = encoded;
    size_t i;
    for (i = 0; i < len; i++) {
        unsigned char c = (unsigned char)str[i];
        // 如果字符是字母、数字或安全符号，则无需编码
        if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            *p++ = c;
        } else {
            // 非安全字符，编码为%HH
            sprintf(p, "%%%02X", c);
            p += 3;
        }
    }

    *p = '\0'; // 确保字符串以'\0'结尾
    return 0;
}

int valid_ipv4(const char *ip)
{
    struct in_addr addr;
    // 使用inet_pton来判断是否是有效的IPv4地址
    return inet_pton(AF_INET, ip, &addr) == 1;
}

void generate_random_string(char *dest, size_t dest_len) {
    // 随机数初始化
    srand(time(NULL));

    // 确保传入的字符串长度至少为1
    if (dest_len == 0) {
        dest[0] = '\0';
        return;
    }

    // 随机生成每个字符
    size_t i;
    for (i = 0; i < dest_len - 1; i++) {  // 留出一个位置给'\0'
        int rand_char = rand() % 62;  // 26大写字母 + 26小写字母 + 10数字
        if (rand_char < 26) {
            dest[i] = 'a' + rand_char;  // 小写字母
        } else if (rand_char < 52) {
            dest[i] = 'A' + rand_char - 26;  // 大写字母
        } else {
            dest[i] = '0' + rand_char - 52;  // 数字
        }
    }

    // 添加字符串结束符
    dest[dest_len] = '\0';
}

double get_device_uptime()
{
    FILE *fp;
    double uptime_seconds;

    // 打开 /proc/uptime 文件
    fp = fopen("/proc/uptime", "r");
    if (fp == NULL) {
        qrzl_err("Error opening /proc/uptime");
        return 0L;
    }

    // 读取系统的总运行时间（单位：秒）
    if (fscanf(fp, "%lf", &uptime_seconds) != 1) {
        qrzl_err("Error reading uptime");
        fclose(fp);
        return 0L;
    }
    fclose(fp);
    return uptime_seconds;
}

int get_device_charge_status()
{
    FILE *fp;
    char status[32];  // 用于存储充电状态

    int ret = 0;

    // 打开 /sys/class/power_supply/charger/status 文件
    fp = fopen("/sys/class/power_supply/charger/status", "r");
    if (fp == NULL) {
        qrzl_err("Error opening charging status file");
        return 0;
    }

    // 读取文件内容
    if (fgets(status, sizeof(status), fp) != NULL) {
        // 根据内容判断是否在充电
        qrzl_log("Charging status: %s", status);
        if (strncmp("Charging", status, 8) == 0) {
            ret = 1;
        }
    }
    // 关闭文件
    fclose(fp);

    return ret;
}

void sleep_ms(uint32_t milliseconds) {
    struct timespec ts;
    ts.tv_sec = milliseconds / 1000;
    ts.tv_nsec = (milliseconds % 1000) * 1000000;
    nanosleep(&ts, NULL);
}

uint8_t get_rsrp_percentage()
{
    char lte_rsrp[10] = {0};
    cfg_get_item("lte_rsrp", lte_rsrp, sizeof(lte_rsrp));
    int rsrp = atoi(lte_rsrp);
    
    uint8_t sim_signal_level = 0;
    if (rsrp >= -85) {
        sim_signal_level = 5;
    } else if (rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    sim_signal_level = sim_signal_level * 20;
    return sim_signal_level;
}

uint32_t get_now_utc_sec()
{
    struct timeval tv;
    struct tm *tm_info;

    // 获取当前的时间
    if (gettimeofday(&tv, NULL) != 0) {
        perror("gettimeofday");
        return 1;
    }

    return tv.tv_sec;
}


uint8_t get_remain_power()
{
    update_battery_vol_percent();
    char battery_vol_percent[10] = {0};
    cfg_get_item("battery_vol_percent", battery_vol_percent, sizeof(battery_vol_percent));
    return atoi(battery_vol_percent);
}

/**
 * 根据卡号设置该卡本月或今日的流量
 * flow: 用户传入的流量值（bytes）
 * sim_id: 用户传入要设置的卡（逻辑卡号）
 * m_or_d:【0代表day】【1代表month】
 */
void set_month_day_flow_by_sim(uint64_t flow, int sim_id, int m_or_d) {
    qrzl_log("qrzl_utils -> into set_month_day_flow_by_sim function.");
    switch (sim_id)
    {
        case 1:
            // ESIM1
            if(!m_or_d) {
                char esim1_flux_day_total[128] = {0};
                snprintf(esim1_flux_day_total, sizeof(esim1_flux_day_total), "%lld", flow);
                cfg_set("esim1_flux_day_total", esim1_flux_day_total);
            } else {
                char esim1_flux_month_total[128] = {0};
                snprintf(esim1_flux_month_total, sizeof(esim1_flux_month_total), "%lld", flow);
                cfg_set("esim1_flux_month_total", esim1_flux_month_total);
            }
            break;
        case 2:
            // ESIM2
#ifdef QRZL_ESIM2_ON_SIM_SLOT
            if(!m_or_d) {
                char rsim_flux_day_total[128] = {0};
                snprintf(rsim_flux_day_total, sizeof(rsim_flux_day_total), "%lld", flow);
                cfg_set("rsim_flux_day_total", rsim_flux_day_total);
            } else {
                char rsim_flux_month_total[128] = {0};
                snprintf(rsim_flux_month_total, sizeof(rsim_flux_month_total), "%lld", flow);
                cfg_set("rsim_flux_month_total", rsim_flux_month_total);
            }
            break;
#else
            if(!m_or_d) {
                char esim2_flux_day_total[128] = {0};
                snprintf(esim2_flux_day_total, sizeof(esim2_flux_day_total), "%lld", flow);
                cfg_set("esim2_flux_day_total", esim2_flux_day_total);
            } else {
                char esim2_flux_month_total[128] = {0};
                snprintf(esim2_flux_month_total, sizeof(esim2_flux_month_total), "%lld", flow);
                cfg_set("esim2_flux_month_total", esim2_flux_month_total);
            }
            break;
        case 0:
            // RSIM
            if(!m_or_d) {
                char rsim_flux_day_total[128] = {0};
                snprintf(rsim_flux_day_total, sizeof(rsim_flux_day_total), "%lld", flow);
                cfg_set("rsim_flux_day_total", rsim_flux_day_total);
            } else {
                char rsim_flux_month_total[128] = {0};
                snprintf(rsim_flux_month_total, sizeof(rsim_flux_month_total), "%lld", flow);
                cfg_set("rsim_flux_month_total", rsim_flux_month_total);
            }
            break;
#endif
    }
}


/**
 * 将 MAC 地址中的 : 替换为 -
 * 输入示例: "00:1A:2B:3C:4D:5E"
 * 输出示例: "00-1A-2B-3C-4D-5E"
 */
void convert_mac_colon_to_dash(const char *mac_input, char *mac_output, size_t output_size) {
    if (!mac_input || !mac_output || output_size < strlen(mac_input) + 1) {
        return;
    }

    size_t i;
    for (i = 0; i < strlen(mac_input) && i < output_size - 1; i++) {
        mac_output[i] = (mac_input[i] == ':') ? '-' : mac_input[i];
    }
    mac_output[i] = '\0';
}

/**
 * 将 MAC 地址中的 - 替换为 :
 * 输入示例: "00-1A-2B-3C-4D-5E"
 * 输出示例: "00:1A:2B:3C:4D:5E"
 */
void convert_mac_dash_to_colon(const char *mac_input, char *mac_output, size_t output_size) {
    if (!mac_input || !mac_output || output_size < strlen(mac_input) + 1) {
        return;
    }

    size_t i;
    for (i = 0; i < strlen(mac_input) && i < output_size - 1; i++) {
        mac_output[i] = (mac_input[i] == '-') ? ':' : mac_input[i];
    }
    mac_output[i] = '\0';
}


/**
 * 校验手机号
 */
int is_valid_phone(const char *phone) {
    if (strlen(phone) != 11)
        return 0;

    if (phone[0] != '1')
        return 0;

    if (phone[1] < '3' || phone[1] > '9')
        return 0;
    int i = 0;
    for (i ; i < 11; i++) {
        if (!isdigit(phone[i]))
            return 0;
    }

    return 1;
}