流量上报
上报接口
地址：http://address:port/iotHub/api/device/v2/flowUpload
请求方式：POST， JSON
请求方式：GET，Query params
请求参数：
参数名称	长度	必填	数据类型	值域	备注
uuid	36	是	String		UUID
标识一次流量上报周期的唯一性，上报成功后重新生成，未成功不更新，以接口返回字段success为true判断
格式：
3ef4f82e-7ecd-b189-e0f1-a98c2920e6a2
device_total_flow	11	是	String		设备使用总流量，单位字节
seed_total_flow	11	是	String		种子卡使用总流量，单位字节
signal	11	是	String		信号强度
示例：
86
devcie_con_num	1	是	Integer		设备连接数
isp	1	是	Integer		运营商
1：移动
2：联通 
3：电信
4：广电
sn	18	是	String		设备 SN
using_iccid	20	是	String		当前使用的卡 ICCID
seed_card_iccid	20	是	String		种子卡 ICCID
vsim_total_flow	20
	是	String		VSIM 卡使用总流量，单位字节

vsim_iccid	20	是	String		VSIM 卡 ICCID
virtual_card_csq	15	是	String		VSIM 卡信号值
示例：
98
phoneType	5	是	String		移动无线网络类型
CDMA---电信
GSM---移动、联通
cdma	15	是	String		CDMA格式为：sid,nid,bid,lon,lat,signal
其中lon,lat可为空
格式为：sid,nid,bid,,,signal
gsm	15	是	String		格式：
mcc,mnc,lac,cellid,signal;
其中lac,cellid必须 填写，signal 如无法获 
取请填写50，前两位 
mcc,mnc如无法获取，请填写-1
sinr	15	是	String		信号值
示例：
16
baseStationList	15	是	String		基站信息，GET请求需做URLEncode
示例：
["460,11,46292,51255349,28,16","460,11,46292,261164305,41,16"]
electric	15	是	String		设备电量
示例：
80
sync_time	18	是	String
		时间戳，单位毫秒
首次上报传0，后续上报为接口返回的值
示例：
1744877144254
接口返回值示例（服务器可能返回多余字段可忽略）
JSON
{
    "success": true,
    "errorCode": 0,
    "errorMessage": "",
    "data": {
        "syncTime": "1745304080164"
    }
}
上报逻辑
首次流量上报时机
设备信息上报接口（uploadDeviceInfo）调用成功之后
周期性上报间隔
findConfig接口返回的flowUploadTimes，单位秒
注意事项
1.设备端流量上报参数字段uuid标识一次流量上报周期的唯一性，用于后台做因某些原因收到相同的流量数据做去重，示例（3ef4f82e-7ecd-b189-e0f1-a98c2920e6a2）
2.设备端流量上报参数新增字段sync_time（首次上报为0、后续为调用流量上报HTTP接口服务器返回的值）
3.如果调用上报接口调用成功流量清零，调用失败，判断HTTP CODE不为200，则重试3次，每次delay1.5s以上，3次全失败后，流量不清零保留到下一次上报周期上报
4.流量上报过程中增加信号量防止并发导致重复上报