
#ifndef   __WIFI_DRV_KO__

#define __WIFI_DRV_KO__

#include "wifi_util.h"

#define  SSV6X5X_INSMODE_STACMD  	 "/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/ssv6x5x/ssv6x5x_host/ssv6x5x.ko stacfgpath=/etc/firmware/ssv6x5x-wifi.cfg"
#define  SSV6X5X_RMMOD_STACMD		 "/sbin/rmmod ssv6x5x"
#define  SSV6X5X_PREALLOC_INSMODE  	 "/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/ssv6x5x/prealloc/ssv6xxx_prealloc_skb.ko"

#define  AIC8800DW_INSMODE_STACMD  	 "/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/aic8800/aic8818_fdrv.ko"
#define  AIC8800DW_RMMOD_STACMD		 "/sbin/rmmod aic8818_fdrv"


#define  XR819_INSMODE_STACMD  	 "/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/xradio/wlan/xradio_wlan.ko"
#define  XR819_RMMOD_STACMD		 "/sbin/rmmod xradio_wlan"

#define  ESP8089_INSMODE_STACMD  	 "/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/esp8089/eagle.ko"
#define  ESP8089_RMMOD_STACMD		 "/sbin/rmmod eagle"


#define  RDA5995_INSMODE_APCMD   	"/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/rdaw5995/rdawlan/rdawfmac.ko firmware_path=ap"
#define  RDA5995_INSMODE_STACMD  	"/sbin/insmod /lib/modules/3.4.110-rt140/kernel/drivers/net/wireless/rdaw5995/rdawlan/rdawfmac.ko firmware_path=sta"
#define  RDA5995_RMMOD_CMD			"/sbin/rmmod rdawfmac"


//#define  RDA5995_INSMODE_APCMD   	"insmod /lib/modules/3.4.110/kernel/drivers/net/wireless/rdaw5995/rdawlan/rdawfmac.ko firmware_path=ap"
//#define  RDA5995_INSMODE_STACMD  	"insmod /lib/modules/3.4.110/kernel/drivers/net/wireless/rdaw5995/rdawlan/rdawfmac.ko firmware_path=sta"
//#define  RDA5995_RMMOD_CMD			"rmmod rdawfmac"



struct wlan_drv_proxy {
	int		drv_init_flag;
	char *	iface_name;
	char*	insmod_cmd;
	char* 	rmmod_cmd;
	int 		(*drv_init)(struct   wlan_drv_proxy *drv_proxy);
	int 		(*drv_deinit)(struct   wlan_drv_proxy *drv_proxy);
};

int   wlan_drv_deinit(struct  wlan_drv_proxy * proxy);

int   wlan_drv_init(struct  wlan_drv_proxy * proxy);

int   wlan_drv_pre_init(struct  wlan_drv_proxy * proxy);

#endif
