
#include <errno.h>
#include <sys/msg.h>
#include <semaphore.h>
#include "pb_com.h"
typedef VOID(*pAtWeb_PbMsgProc)(UINT8*pDatabuf);typedef struct{UINT32 msg_id;
VOID(*func_ptr)(UINT8*pDatabuf);BOOL need_block;}T_zPbHandleTable;VOID 
atWeb_AddOneContact(UINT8*pDatabuf);VOID atWeb_DelOneContact(UINT8*pDatabuf);
VOID atWeb_DelMultiContact(UINT8*pDatabuf);VOID atWeb_DelAllContact(UINT8*
pDatabuf);T_zPb_optRsp g_PbOptRsp={(0xc32+1292-0x113e)};sem_t g_Pb_sem_id={
(0xfe1+2014-0x17bf)};int g_zPb_MsqId=-(0x1312+4064-0x22f1);int g_zPb_LocalMsqId=
-(0xd5+7099-0x1c8f);VOID atWeb_AddOneContact(UINT8*pDatabuf){T_zPb_WebContact*
webPbContact=NULL;if(pDatabuf==NULL){printf(
"\x5b\x70\x62\x5d\x2c\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x43\x6f\x6e\x74\x61\x63\x74\x20\x70\x61\x72\x61\x20\x69\x73\x20\x6e\x75\x6c\x6c" "\n"
);return;}webPbContact=(T_zPb_WebContact*)pDatabuf;atWeb_AddOnePb(webPbContact,
g_Pb_sem_id);}VOID atWeb_DelOneContact(UINT8*pDatabuf){T_zPb_DelInfo*delRecord=
NULL;if(pDatabuf==NULL){printf(
"\x5b\x70\x62\x5d\x2c\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4f\x6e\x65\x43\x6f\x6e\x74\x61\x63\x74\x20\x70\x61\x72\x61\x20\x69\x73\x20\x6e\x75\x6c\x6c" "\n"
);return;}delRecord=(T_zPb_DelInfo*)pDatabuf;atWeb_DelOnepb(delRecord,
g_Pb_sem_id);}VOID atWeb_DelMultiContact(UINT8*pDatabuf){T_zPb_DelInfo*delRecord
=NULL;if(pDatabuf==NULL){printf(
"\x5b\x70\x62\x5d\x2c\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4f\x6e\x65\x43\x6f\x6e\x74\x61\x63\x74\x20\x70\x61\x72\x61\x20\x69\x73\x20\x6e\x75\x6c\x6c" "\n"
);return;}delRecord=(T_zPb_DelInfo*)pDatabuf;atWeb_DelMultPb(delRecord,
g_Pb_sem_id);}VOID atWeb_DelAllContact(UINT8*pDatabuf){T_zPb_DelInfo*delRecord=
NULL;if(pDatabuf==NULL){printf(
"\x5b\x70\x62\x5d\x2c\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4f\x6e\x65\x43\x6f\x6e\x74\x61\x63\x74\x20\x70\x61\x72\x61\x20\x69\x73\x20\x6e\x75\x6c\x6c" "\n"
);return;}delRecord=(T_zPb_DelInfo*)pDatabuf;atWeb_DelAllpb(delRecord,
g_Pb_sem_id);}VOID zPb_RecvScpbrInd(UINT8*pDatabuf){T_zPb_AtScpbrTestRes*
scpbsInd=NULL;if(pDatabuf==NULL){return;}scpbsInd=(T_zPb_AtScpbrTestRes*)
pDatabuf;atPb_ScpbrTestRsp(scpbsInd);}VOID zPb_RecvScpbrReadInd(UINT8*pDatabuf){
T_zPb_ScpbrSetRes*scpbsReadInd=NULL;if(pDatabuf==NULL){return;}scpbsReadInd=(
T_zPb_ScpbrSetRes*)pDatabuf;atPb_ScpbrSetRsp(scpbsReadInd);}VOID zPb_RecvCpmsInd
(UINT8*pDatabuf){T_zPb_AtCpbsReadRes*cpbsInd=NULL;if(pDatabuf==NULL){return;}
cpbsInd=(T_zPb_AtCpbsReadRes*)pDatabuf;atPb_RecvCpbsReadRsp(cpbsInd);}VOID 
zPb_RecvZpbicInd(UINT8*pDatabuf){T_zAt_ZpbicRes*ptPara=ZUFI_NULL;if(pDatabuf==
NULL){return;}ptPara=(T_zAt_ZpbicRes*)(pDatabuf);printf(
"\x7a\x50\x62\x5f\x52\x65\x63\x76\x5a\x70\x62\x69\x63\x49\x6e\x64\x20\x70\x61\x72\x61\x2c\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2c\x20\x74\x79\x70\x65\x3a\x25\x64" "\n"
,ptPara->result,ptPara->opertype);if(((0x423+2001-0xbf3)==ptPara->result)&&(
(0x9+9453-0x24f5)==ptPara->opertype)){CHAR needPb[(0x388+3659-0x11a1)]={
(0x5d8+3523-0x139b)};sc_cfg_get(NV_NEED_SUPPORT_PB,needPb,sizeof(needPb));if(
(0x75f+8090-0x26f9)!=strcmp(needPb,"\x6e\x6f")){ipc_send_message(MODULE_ID_PB,
MODULE_ID_AT_CTL,MSG_CMD_PBINIT_REQ,(0x9ed+4578-0x1bcf),NULL,(0x76b+1539-0xd6e))
;}}}VOID zPb_RecvZuslotInd(UINT8*pDatabuf){T_zAt_ZuslotRes*ptPara=ZUFI_NULL;if(
pDatabuf==NULL){return;}ptPara=(T_zAt_ZuslotRes*)(pDatabuf);printf(
"\x7a\x50\x62\x5f\x52\x65\x63\x76\x5a\x70\x62\x69\x63\x49\x6e\x64\x20\x70\x61\x72\x61\x2c\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2c\x20\x74\x79\x70\x65\x3a\x25\x64" "\n"
,ptPara->slot,ptPara->slot_state);if(ptPara->slot_state==(0x1c7+3273-0xe90)){
CHAR needPb[(0xe4b+910-0x11a7)]={(0x103d+2704-0x1acd)};sc_cfg_get(
NV_NEED_SUPPORT_PB,needPb,sizeof(needPb));if((0x111b+4283-0x21d6)!=strcmp(needPb
,"\x6e\x6f")){atPb_CfgPbNvInit();atPb_DelAllRecsSimDb();}}}VOID 
zPb_RecvPbInitRst(UINT8*pDatabuf){int pbReadRet=-(0x17b7+776-0x1abe);memcpy(&
g_PbOptRsp,pDatabuf,sizeof(T_zPb_optRsp));if(g_PbOptRsp.result==-
(0x2026+1239-0x24fc)){atPb_IintPbErr(NULL);return;}pbReadRet=
atPb_SendScpbrSet_repeat(g_Pb_sem_id);printf(
"\x7a\x50\x62\x5f\x52\x65\x63\x76\x50\x62\x49\x6e\x69\x74\x52\x73\x74\x20\x72\x65\x73\x75\x6c\x74\x3a\x25\x64" "\n"
,pbReadRet);sc_cfg_set(ZPB_NV_INIT,ZPB_OPERATE_SUC);}void zPbLocalHandleWebMsg(
MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x50\x62\x20\x72\x65\x63\x76\x20\x77\x65\x62\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_WRITE_PB:
atWeb_AddOneContact(ptMsgBuf->aucDataBuf);break;case MSG_CMD_DEL_A_PB:
atWeb_DelOneContact(ptMsgBuf->aucDataBuf);break;case MSG_CMD_DEL_MUTI_PB:
atWeb_DelMultiContact(ptMsgBuf->aucDataBuf);break;case MSG_CMD_DEL_ALL_PB:
atWeb_DelAllContact(ptMsgBuf->aucDataBuf);break;default:break;}}void 
zPbHandleWebMsg(MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x50\x62\x20\x72\x65\x63\x76\x20\x77\x65\x62\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_WRITE_PB:case 
MSG_CMD_DEL_A_PB:case MSG_CMD_DEL_MUTI_PB:case MSG_CMD_DEL_ALL_PB:
ipc_send_message(MODULE_ID_PB,MODULE_ID_PB_LOCAL,ptMsgBuf->usMsgCmd,ptMsgBuf->
usDataLen,(unsigned char*)ptMsgBuf->aucDataBuf,(0x13f9+3362-0x211b));break;
default:break;}}UINT8 zPbMsgCreat(VOID){g_zPb_MsqId=msgget(MODULE_ID_PB,
IPC_CREAT|(0x159c+2525-0x1df9));if(g_zPb_MsqId==-(0x782+3205-0x1406)){return 
ZUFI_FAIL;}g_zPb_LocalMsqId=msgget(MODULE_ID_PB_LOCAL,IPC_CREAT|
(0x456+2080-0xaf6));if(g_zPb_LocalMsqId==-(0xeb8+870-0x121d)){return ZUFI_FAIL;}
sem_init(&g_Pb_sem_id,(0x2272+1003-0x265d),(0x1677+3954-0x25e9));return 
ZUFI_SUCC;}void detect_modem_state(void){CHAR state[(0x242d+685-0x26a8)]={
(0xd4b+5567-0x230a)};sc_cfg_get(
"\x6d\x6f\x64\x65\x6d\x5f\x6d\x61\x69\x6e\x5f\x73\x74\x61\x74\x65",state,sizeof(
state));if((0x1a8a+2087-0x22b1)==strcmp(state,
"\x6d\x6f\x64\x65\x6d\x5f\x73\x69\x6d\x5f\x75\x6e\x64\x65\x74\x65\x63\x74\x65\x64"
)||(0xdd0+4151-0x1e07)==strcmp(state,
"\x6d\x6f\x64\x65\x6d\x5f\x73\x69\x6d\x5f\x64\x65\x73\x74\x72\x6f\x79")){
sc_cfg_set("\x70\x62\x6d\x5f\x69\x6e\x69\x74\x5f\x66\x6c\x61\x67","\x30");}}void
 zPbLocalHandleAtctlMsg(MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x50\x62\x20\x6c\x6f\x63\x61\x6c\x20\x72\x65\x63\x76\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_PBINIT_RSP:
zPb_RecvPbInitRst(ptMsgBuf->aucDataBuf);break;default:break;}}VOID 
zPbHandleAtctlMsg(MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x50\x62\x20\x72\x65\x63\x76\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n",
ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_DELETE_PB_RSP:case 
MSG_CMD_ADD_MODIFY_PB_RSP:case MSG_CMD_READ_PB_RSP:{memcpy(&g_PbOptRsp,ptMsgBuf
->aucDataBuf,sizeof(T_zPb_optRsp));sem_post(&g_Pb_sem_id);}break;case 
MSG_CMD_CPBS_IND:zPb_RecvCpmsInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_SCPBR_IND:zPb_RecvScpbrInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_SCPBR_READ_IND:zPb_RecvScpbrReadInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_ZPBIC_IND:zPb_RecvZpbicInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_ZUSLOT_IND:zPb_RecvZuslotInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_PBINIT_RSP:ipc_send_message(MODULE_ID_PB,MODULE_ID_PB_LOCAL,ptMsgBuf->
usMsgCmd,ptMsgBuf->usDataLen,(unsigned char*)ptMsgBuf->aucDataBuf,
(0x2c7+3292-0xfa3));break;default:break;}}VOID zPbHandleResetToFactory(){CHAR 
clearPb[(0x8ea+5355-0x1da3)]={(0xb71+377-0xcea)};sc_cfg_get(
NV_CLEAR_PB_WHEN_RESTORE,clearPb,sizeof(clearPb));printf(
"\x61\x74\x57\x65\x62\x5f\x52\x65\x73\x74\x6f\x72\x65\x46\x61\x63\x74\x6f\x72\x79\x53\x65\x74\x74\x69\x6e\x67\x20\x65\x6e\x74\x65\x72\x65\x64\x21\x20" "\n"
);printf(
"\x63\x6c\x65\x61\x72\x5f\x70\x62\x5f\x77\x68\x65\x6e\x5f\x72\x65\x73\x74\x6f\x72\x65\x3d\x25\x73\x20" "\n"
,clearPb);if(strcmp(clearPb,"\x79\x65\x73")==(0x13b8+4243-0x244b)){atPb_DropDb()
;}ipc_send_message(MODULE_ID_PB,MODULE_ID_MAIN_CTRL,MSG_CMD_RESET_RSP,
(0xb88+5218-0x1fea),NULL,(0x9f9+2801-0x14ea));}void zPbHandleMainCtrlMsg(MSG_BUF
*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x50\x62\x20\x72\x65\x63\x76\x20\x6d\x61\x69\x6e\x20\x63\x74\x72\x6c\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_RESET_NOTIFY:
zPbHandleResetToFactory(ptMsgBuf->aucDataBuf);break;default:break;}}void 
pb_msg_thread_proc(void*arg){int iRet=(0x1531+732-0x180d);MSG_BUF stMsg={
(0xe73+4760-0x210b)};int msgSize=sizeof(MSG_BUF)-sizeof(SINT32);int queueId=*((
int*)arg);prctl(PR_SET_NAME,"\x70\x62\x5f\x6c\x6f\x63\x61\x6c",
(0x1676+490-0x1860),(0x790+7772-0x25ec),(0x189+9133-0x2536));while(
(0x1262+2529-0x1c42)){iRet=(0xd84+5618-0x2376);memset(&stMsg,(0x1094+31-0x10b3),
sizeof(MSG_BUF));iRet=msgrcv(queueId,&stMsg,msgSize,(0x100c+1268-0x1500),
(0x1269+535-0x1480));printf(
"\x70\x62\x5f\x6d\x73\x67\x5f\x74\x68\x72\x65\x61\x64\x5f\x70\x72\x6f\x63\x3a\x25\x78\x2c\x25\x78\x20\x4d\x4f\x44\x55\x4c\x45\x5f\x49\x44\x5f\x41\x54\x5f\x43\x54\x4c\x3d\x25\x78" "\n"
,stMsg.src_id,stMsg.usMsgCmd,MODULE_ID_AT_CTL);if(iRet>=(0x1705+697-0x19be)){
switch(stMsg.src_id){case MODULE_ID_WEB_CGI:{zPbHandleWebMsg(&stMsg);break;}case
 MODULE_ID_AT_CTL:{zPbHandleAtctlMsg(&stMsg);break;}case MODULE_ID_PB:{
zPbLocalHandleWebMsg(&stMsg);zPbLocalHandleAtctlMsg(&stMsg);break;}case 
MODULE_ID_MAIN_CTRL:{zPbHandleMainCtrlMsg(&stMsg);break;}default:{break;}}}else{
printf(
"\x5b\x70\x62\x5d\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x2c\x20\x65\x72\x72\x6d\x73\x67\x20\x3d\x20\x25\x73" "\n"
,errno,strerror(errno));}}}int phonebook_main(int argc,char*argv[]){pthread_t 
recv_thread_tid=(0x24d1+215-0x25a8);MSG_BUF msgBuf={(0x1a36+2321-0x2347)};CHAR 
needPb[(0x4aa+8433-0x2569)]={(0xb2a+5744-0x219a)};prctl(PR_SET_NAME,
"\x70\x62\x5f\x6d\x61\x69\x6e",(0x733+7470-0x2461),(0x6f7+833-0xa38),
(0x1ea+180-0x29e));loglevel_init();sc_cfg_get(NV_NEED_SUPPORT_PB,needPb,sizeof(
needPb));if((0xe27+5012-0x21bb)!=strcmp(needPb,"\x6e\x6f")){
#ifdef WEBS_SECURITY
if(access(ZPB_DB_PATH,F_OK)!=(0x1d6+4737-0x1457)){if(access(ZPB_TMP_PATH,F_OK)==
(0x154+3691-0xfbf)){if(remove(ZPB_SEC_PATH)!=(0x567+7700-0x237b)){slog(PB_PRINT,
SLOG_ERR,
"\x72\x65\x6d\x6f\x76\x65\x20\x5a\x50\x42\x5f\x53\x45\x43\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}if(rename(ZPB_TMP_PATH,ZPB_SEC_PATH)!=(0x894+729-0xb6d)){slog(PB_PRINT,
SLOG_ERR,
"\x72\x65\x6e\x61\x6d\x65\x20\x5a\x50\x42\x5f\x54\x4d\x50\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}}if(access(ZPB_SEC_PATH,F_OK)==(0xef9+3180-0x1b65)){char rnum_buf[
(0x779+5308-0x1c1d)]={(0x205b+39-0x2082)};char cmd[(0x246+1770-0x8b0)]={
(0x20e5+264-0x21ed)};sc_cfg_get("\x72\x6e\x75\x6d\x5f\x61\x74",rnum_buf,sizeof(
rnum_buf));snprintf(cmd,sizeof(cmd),
"\x2f\x62\x69\x6e\x2f\x6f\x70\x65\x6e\x73\x73\x6c\x20\x65\x6e\x63\x20\x2d\x64\x20\x2d\x61\x65\x73\x32\x35\x36\x20\x2d\x73\x61\x6c\x74\x20\x2d\x69\x6e\x20\x25\x73\x20\x2d\x6f\x75\x74\x20\x25\x73\x20\x2d\x70\x61\x73\x73\x20\x70\x61\x73\x73\x3a\x25\x73"
,ZPB_SEC_PATH,ZPB_DB_PATH,rnum_buf);zxic_system(cmd);}}
#endif		
zPbMsgCreat();atPb_Init();}else{return-(0x65a+2031-0xe48);}printf(
"\x50\x62\x20\x61\x70\x70\x20\x69\x6e\x69\x74\x20\x66\x69\x6e\x69\x73\x68\x65\x64\x2c\x20\x77\x69\x6c\x6c\x20\x74\x6f\x20\x72\x65\x63\x65\x69\x76\x65\x20\x6d\x73\x67\x2c\x20\x6d\x73\x67\x69\x64\x3a\x25\x64" "\n"
,g_zPb_MsqId);if(pthread_create(&recv_thread_tid,NULL,pb_msg_thread_proc,(void*)
(&g_zPb_LocalMsqId))==-(0xd6+5782-0x176b)){assert((0x345+8311-0x23bc));}
detect_modem_state();pb_msg_thread_proc(&g_zPb_MsqId);return(0xa6a+3806-0x1948);
}
