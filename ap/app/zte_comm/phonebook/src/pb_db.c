
#include "pb_com.h"
T_zPb_DbResult atPb_CreatDb(){T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[
ZPB_MAX_BYTES_DB]={(0x885+3165-0x14e2)};UINT32 count=(0xa7+4336-0x1197);result=
atPb_ExecDbSql(ZPB_CREATE_PBM_TABLE,NULL,NULL);if(ZPB_DB_OK!=result){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x72\x65\x61\x74\x65\x20\x70\x62\x6d\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(
ZPB_CREATE_PBM_SIM_CAPABILITY_TABLE,NULL,NULL);if(ZPB_DB_OK!=result){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x72\x65\x61\x74\x65\x20\x70\x62\x6d\x5f\x73\x69\x6d\x5f\x63\x61\x70\x61\x62\x69\x6c\x69\x74\x79\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(
ZPB_CREATE_PBM_DEVICE_CAPABILITY_TABLE,NULL,NULL);if(ZPB_DB_OK!=result){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x72\x65\x61\x74\x65\x20\x70\x62\x6d\x5f\x64\x65\x76\x69\x63\x65\x5f\x63\x61\x70\x61\x62\x69\x6c\x69\x74\x79\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}snprintf(sql,sizeof(sql),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_USIM);result=atPb_ExecDbSql(sql,NULL,NULL);if(
ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x72\x65\x61\x74\x65\x20\x70\x62\x6d\x5f\x64\x65\x76\x69\x63\x65\x5f\x63\x61\x70\x61\x62\x69\x6c\x69\x74\x79\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}memset(sql,(0x1bf+8454-0x22c5),sizeof(sql));snprintf(sql
,sizeof(sql)-(0xe16+6287-0x26a4),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73"
,ZPB_DB_SIM_CAPABILITY_TABLE);(VOID)atPb_ExecDbSql(sql,atPb_DbCountTableLineCb,&
count);if((0x43c+6637-0x1e29)<count){memset(sql,(0x710+3141-0x1355),sizeof(sql))
;snprintf(sql,sizeof(sql)-(0x19ba+2212-0x225d),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x53\x69\x6d\x5f\x74\x79\x70\x65\x3e\x3d\x30"
,ZPB_DB_SIM_CAPABILITY_TABLE);result=atPb_ExecDbSql(sql,NULL,NULL);if(ZPB_DB_OK
!=result){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x65\x74\x53\x69\x6d\x43\x61\x70\x61\x63\x69\x74\x79\x54\x61\x62\x6c\x65\x3a\x66\x61\x69\x6c\x21" "\n"
);return result;}}return ZPB_DB_OK;}T_zPb_DbResult atPb_DropDb(){T_zPb_DbResult 
result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0x9b6+2540-0x13a2)};result=
atPb_ExecDbSql(ZTE_DROP_PBM_SQL,NULL,NULL);if(ZPB_DB_OK!=result){slog(PB_PRINT,
SLOG_ERR,
"\x61\x74\x50\x62\x5f\x44\x72\x6f\x70\x44\x62\x3a\x64\x65\x6c\x20\x70\x62\x6d\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(ZTE_DROP_PBM_DEVICE_SQL,NULL,NULL)
;if(ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_ERR,
"\x61\x74\x50\x62\x5f\x44\x72\x6f\x70\x44\x62\x3a\x64\x65\x6c\x20\x70\x62\x6d\x5f\x64\x65\x76\x69\x63\x65\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(ZTE_DROP_PBM_SIM_SQL,NULL,NULL);if
(ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_ERR,
"\x61\x74\x50\x62\x5f\x44\x72\x6f\x70\x44\x62\x3a\x64\x65\x6c\x20\x70\x62\x6d\x5f\x73\x69\x6d\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(ZTE_DROP_PBM_SIM_CAPABILITY_SQL,
NULL,NULL);if(ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_ERR,
"\x61\x74\x50\x62\x5f\x44\x72\x6f\x70\x44\x62\x3a\x64\x65\x6c\x20\x70\x62\x6d\x5f\x73\x69\x6d\x5f\x63\x61\x70\x61\x62\x69\x6c\x69\x74\x79\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}result=atPb_ExecDbSql(ZTE_DROP_PBM_DEVICE_CAPABILITY_SQL
,NULL,NULL);if(ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_ERR,
"\x61\x74\x50\x62\x5f\x44\x72\x6f\x70\x44\x62\x3a\x64\x65\x6c\x20\x70\x62\x6d\x5f\x64\x65\x76\x69\x63\x65\x5f\x63\x61\x70\x61\x62\x69\x6c\x69\x74\x79\x20\x74\x61\x62\x6c\x65\x20\x72\x65\x73\x75\x6c\x74\x20\x69\x73\x20\x25\x64" "\n"
,result);return result;}return ZPB_DB_OK;}T_zPb_DbResult atPb_DbOpen(sqlite3**
pDb){sqlite3*pTmpDb=NULL;if(NULL==pDb){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x69\x6e\x76\x61\x6c\x69\x64\x65\x20\x69\x6e\x70\x75\x74\x73\x2e"
);return ZPB_DB_ERROR_INVALIDPTR;}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x73\x71\x6c\x69\x74\x65\x33\x5f\x6f\x70\x65\x6e\x20\x63\x61\x6c\x6c"
);
#if (0x274+7819-0x20ff)
if(!fopen(ZPB_DB_PATH,"\x72")){file=fopen(ZPB_DB_PATH,"\x77");if(!file){printf(
"\x75\x6e\x61\x62\x6c\x65\x20\x74\x6f\x20\x6f\x70\x65\x6e\x20\x20\x66\x69\x6c\x65\x20\x65\x74\x63\x5f\x72\x77\x2f\x70\x62\x6d\x2e\x64\x62" "\n"
);}else{printf(
"\x20\x6f\x70\x65\x6e\x20\x20\x66\x69\x6c\x65\x20\x65\x74\x63\x5f\x72\x77\x2f\x70\x62\x6d\x2e\x64\x62\x20\x66\x69\x6c\x65\x3d\x25\x64" "\n"
,file);fclose(file);}}
#endif
if(sqlite3_open(ZPB_DB_PATH,&pTmpDb)){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x61\x6e\x20\x6e\x6f\x74\x20\x6f\x70\x65\x6e\x20\x64\x62\x2c\x73\x71\x6c\x69\x74\x65\x33\x5f\x65\x72\x72\x6d\x73\x67\x3a\x25\x73\x2e"
,sqlite3_errmsg(pTmpDb));(VOID)sqlite3_close(pTmpDb);return 
ZPB_DB_ERROR_NOTOPENDB;}*pDb=pTmpDb;return ZPB_DB_OK;}T_zPb_DbResult 
atPb_DbClose(sqlite3*pDb){if(NULL==pDb){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x69\x6e\x76\x61\x6c\x69\x64\x65\x20\x69\x6e\x70\x75\x74\x73\x2e"
);return ZPB_DB_ERROR_INVALIDPTR;}if(sqlite3_close(pDb)){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x61\x6e\x20\x6e\x6f\x74\x20\x63\x6c\x6f\x73\x65\x20\x64\x62"
);return ZPB_DB_ERROR;}
#ifdef WEBS_SECURITY
if(access(ZPB_TMP_PATH,F_OK)==(0x1922+1274-0x1e1c)){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x74\x6d\x70\x20\x64\x62\x20\x73\x74\x61\x79");if(
remove(ZPB_TMP_PATH)!=(0x10d1+4386-0x21f3)){slog(PB_PRINT,SLOG_ERR,
"\x72\x65\x6d\x6f\x76\x65\x20\x5a\x50\x42\x5f\x54\x4d\x50\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}}if(rename(ZPB_SEC_PATH,ZPB_TMP_PATH)!=(0x5a5+4732-0x1821)){slog(PB_PRINT,
SLOG_ERR,
"\x72\x65\x6e\x61\x6d\x65\x20\x5a\x50\x42\x5f\x53\x45\x43\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}{char rnum_buf[(0xda8+5601-0x2371)]={(0x85d+1211-0xd18)};char cmd[
(0x1086+2820-0x1b0a)]={(0x17a3+1725-0x1e60)};sc_cfg_get(
"\x72\x6e\x75\x6d\x5f\x61\x74",rnum_buf,sizeof(rnum_buf));snprintf(cmd,sizeof(
cmd),
"\x2f\x62\x69\x6e\x2f\x6f\x70\x65\x6e\x73\x73\x6c\x20\x65\x6e\x63\x20\x2d\x65\x20\x2d\x61\x65\x73\x32\x35\x36\x20\x2d\x73\x61\x6c\x74\x20\x2d\x69\x6e\x20\x25\x73\x20\x2d\x6f\x75\x74\x20\x25\x73\x20\x2d\x70\x61\x73\x73\x20\x70\x61\x73\x73\x3a\x25\x73"
,ZPB_DB_PATH,ZPB_SEC_PATH,rnum_buf);zxic_system(cmd);if(access(ZPB_SEC_PATH,F_OK
)==(0x13d2+750-0x16c0)){if(remove(ZPB_TMP_PATH)!=(0x1a45+2852-0x2569)){slog(
PB_PRINT,SLOG_ERR,
"\x72\x65\x6d\x6f\x76\x65\x20\x5a\x50\x42\x5f\x54\x4d\x50\x5f\x50\x41\x54\x48\x31\x20\x66\x61\x69\x6c"
);}}}
#endif	
return ZPB_DB_OK;}static check_sql_cmd(const char*pSql){if(pSql!=NULL){if(strstr
(pSql,"\x3b")||strstr(pSql,"\x2d\x2d")){return(0x1807+3078-0x240d);}return
(0x92b+4339-0x1a1d);}return(0x2243+912-0x25d3);}T_zPb_DbResult atPb_ExecDbSql(
const char*pSql,sqlite3_callback callback,VOID*pFvarg){sqlite3*pDb=NULL;CHAR 
dbErrMsg[(0x125c+4900-0x2500)]={(0x12f2+1262-0x17e0)};if(NULL==pSql){return 
ZPB_DB_ERROR_INVALIDPTR;}
#ifdef WEBS_SECURITY
if(check_sql_cmd(pSql)==(0x850+5362-0x1d42)){slog(PB_PRINT,SLOG_ERR,
"\x21\x21\x61\x74\x50\x62\x5f\x45\x78\x65\x63\x44\x62\x53\x71\x6c\x3a\x78\x73\x73\x20\x25\x73" "\n"
,pSql);return ZPB_DB_ERROR_INVALIDPTR;}
#endif	
if(ZPB_DB_OK!=atPb_DbOpen(&pDb)){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x6f\x70\x65\x6e\x20\x70\x62\x6d\x2e\x64\x62\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return ZPB_DB_ERROR_NOTOPENDB;}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x45\x78\x65\x63\x44\x62\x53\x71\x6c\x3a\x25\x73" "\n"
,pSql);if(sqlite3_exec(pDb,pSql,callback,pFvarg,NULL)){strncpy(dbErrMsg,
sqlite3_errmsg(pDb),sizeof(dbErrMsg)-(0x197f+1376-0x1ede));slog(PB_PRINT,
SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x63\x61\x6e\x20\x6e\x6f\x74\x20\x65\x78\x65\x63\x20\x73\x71\x6c\x2c\x73\x71\x6c\x69\x74\x65\x33\x5f\x65\x72\x72\x6d\x73\x67\x3a\x25\x73\x2e"
,dbErrMsg);(VOID)sqlite3_close(pDb);return ZPB_DB_ERROR;}(VOID)atPb_DbClose(pDb)
;return ZPB_DB_OK;}SINT32 atPb_InitApIndexCb(VOID*fvarg,int line,char**zresult,
char**lname){SINT32 index=(0x659+3827-0x154c);if((0xa3f+2783-0x151d)>line){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x49\x6e\x69\x74\x41\x70\x49\x6e\x64\x65\x78\x43\x62\x3a\x72\x65\x63\x6f\x72\x64\x20\x6e\x6f\x20\x64\x61\x74\x61\x2e" "\n"
);return-(0x1101+781-0x140d);}index=atoi(zresult[(0xe91+5331-0x2364)]);if(index>
ZPB_AP_MAX_RECORD){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x49\x6e\x69\x74\x41\x70\x49\x6e\x64\x65\x78\x43\x62\x3a\x69\x6e\x64\x65\x78\x20\x6f\x76\x65\x72\x66\x6c\x6f\x77\x2e" "\n"
);return-(0x1c53+72-0x1c9a);}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x49\x6e\x69\x74\x41\x70\x49\x6e\x64\x65\x78\x43\x62\x3a\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3d\x25\x64" "\n"
,index);g_zPb_ApIndex[index]=PBM_SUCCESS;return(0x11cf+3658-0x2019);}
T_zPb_DbResult atPb_InitApIndex(){CHAR sql[ZPB_MAX_BYTES_DB]={
(0x1295+3104-0x1eb5)};SINT32 i=(0xe21+1109-0x1275);g_zPb_ApIndex[
(0x1ca0+645-0x1f25)]=ZPB_AP_MAX_RECORD;for(i=(0x9ed+1288-0xef4);i<=g_zPb_ApIndex
[(0xcb8+1464-0x1270)];i++){g_zPb_ApIndex[i]=PBM_ERROR_NOT_FOUND;}snprintf(sql,
sizeof(sql),
"\x73\x65\x6c\x65\x63\x74\x20\x50\x62\x6d\x5f\x69\x6e\x64\x65\x78\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_AP);return atPb_ExecDbSql(sql,atPb_InitApIndexCb,
ZUFI_NULL);}INT zte_pbm_check_and_creat_dir(char*path){if(-(0x18ac+347-0x1a06)==
access(path,(0x23b+7336-0x1ee3))){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x70\x62\x6d\x3a\x25\x73\x20\x64\x6f\x65\x73\x20\x6e\x6f\x74\x20\x65\x78\x69\x73\x74\x2c\x73\x6f\x63\x72\x65\x61\x74\x65\x20\x69\x74\x2e" "\n"
,ZPB_DB_DIR);if(-(0x37+6468-0x197a)==mkdir(path,(0xed3+4958-0x2032))){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x3a\x66\x61\x69\x6c\x65\x64\x20\x74\x6f\x20\x63\x72\x65\x61\x74\x65\x20\x64\x62\x20\x64\x69\x72\x2e" "\n"
);return-(0xbea+2046-0x13e7);}}return(0x850+4345-0x1949);}UINT8 
zte_pbm_check_web_pbm_dir(VOID){
#ifdef _MBB_OS_UCLINUX
(VOID)zte_pbm_check_and_creat_dir(
"\x2f\x6d\x6e\x74\x2f\x6a\x66\x66\x73\x32\x2f\x65\x74\x63\x5f\x72\x77");(VOID)
zte_pbm_check_and_creat_dir(
"\x2f\x6d\x6e\x74\x2f\x6a\x66\x66\x73\x32\x2f\x65\x74\x63\x5f\x72\x77\x2f\x63\x6f\x6e\x66\x69\x67"
);
#else
(VOID)zte_pbm_check_and_creat_dir("\x2f\x65\x74\x63\x5f\x72\x77");(VOID)
zte_pbm_check_and_creat_dir(
"\x2f\x65\x74\x63\x5f\x72\x77\x2f\x63\x6f\x6e\x66\x69\x67");
#endif
return ZUFI_SUCC;}T_zPb_DbResult atPb_DelSimRecFromPbTable(SINT32 index){
T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0x8bd+2039-0x10b4)}
;snprintf(sql,sizeof(sql),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64\x20\x61\x6e\x64\x20\x50\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_USIM,index);result=atPb_ExecDbSql(sql,NULL,NULL);
if(ZPB_DB_OK==result){g_zPb_SimIndex[index]=PBM_ERROR_NOT_FOUND;}return result;}
T_zPb_DbResult atPb_LoadARecToPbmTable(T_zPb_WebContact*pbPara){T_zPb_DbResult 
result=ZPB_DB_ERROR;CHAR sql[ZPB_MAX_BYTES_DB]={(0x1f4f+562-0x2181)};if(NULL==
pbPara){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x4c\x6f\x61\x64\x41\x52\x65\x63\x54\x6f\x50\x62\x6d\x54\x61\x62\x6c\x65\x3a\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74"
);return ZPB_DB_ERROR_INVALIDPTR;}snprintf(sql,sizeof(sql),"insert into %s (Pbm_index,Location,Number,Type,Name,Anr,Anr1,Email,Sne) \
			values(\'%d\',\'%d\',\'%s\',\'%d\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')",ZPB_DB_PBM_TABLE,pbPara->pbIndex,pbPara->pbLocation,pbPara->mobilNumber,pbPara
->pbType,pbPara->name,pbPara->homeNumber,pbPara->officeNumber,pbPara->email,
pbPara->sne);result=atPb_ExecDbSql(sql,NULL,NULL);if(ZPB_DB_OK==result){CHAR 
pbMax[(0x19a4+3067-0x256d)]={(0x1408+2439-0x1d8f)};sc_cfg_get(
ZPB_NV_USIMINDEXMAX,pbMax,sizeof(pbMax));if((pbPara->pbIndex>=(0x12c+3734-0xfc1)
)&&(pbPara->pbIndex<=atoi(pbMax))){g_zPb_SimIndex[pbPara->pbIndex]=PBM_SUCCESS;}
(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);}else{(VOID)sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x70\x62\x6d\x3a\x65\x78\x65\x63\x20\x74\x61\x62\x6c\x65\x20\x25\x73\x20\x72\x65\x73\x75\x6c\x74\x20\x25\x64" "\n"
,ZPB_DB_PBM_TABLE,result);return result;}VOID atPb_SqlModifyOneRec(
T_zPb_WebContact*pbmPara,char*sql,int len){printf(
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x71\x6c\x4d\x6f\x64\x69\x66\x79\x4f\x6e\x65\x52\x65\x63\x20\x65\x6e\x74\x65\x72"
);if(ZPB_LOCATION_USIM==pbmPara->pbLocation){snprintf(sql,len,"update %s set Pbm_index=\'%d\',Location=\'%d\',Number=\'%s\',Type=\'%d\',Name=\'%s\',Anr=\'%s\', \
        		Anr1=\'%s\',Email=\'%s\',Sne=\'%s\' where id=%d",ZPB_DB_PBM_TABLE,pbmPara->pbIndex,pbmPara->pbLocation,pbmPara->mobilNumber,
pbmPara->pbType,pbmPara->name,pbmPara->homeNumber,pbmPara->officeNumber,pbmPara
->email,pbmPara->sne,pbmPara->pbId);}else if(ZPB_LOCATION_AP==pbmPara->
pbLocation){snprintf(sql,len,"update %s set Pbm_index=\'%d\',Location=\'%d\',Number=\'%s\',Type=\'%d\',Name=\'%s\',Anr=\'%s\', \
					Anr1=\'%s\',Email=\'%s\',Sne=\'%s\',Pbm_group=\'%s\' where id=%d",ZPB_DB_PBM_TABLE,pbmPara->pbIndex,pbmPara->pbLocation,pbmPara->mobilNumber,
pbmPara->pbType,pbmPara->name,pbmPara->homeNumber,pbmPara->officeNumber,pbmPara
->email,pbmPara->sne,pbmPara->group,pbmPara->pbId);}}T_zPb_DbResult 
atPb_DbGetParamCb(VOID*fvarg,int line,char**zresult,char**lname){T_zPb_Header 
para={(0x1641+652-0x18cd)};if((0x109b+188-0x1156)>line){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x62\x47\x65\x74\x50\x61\x72\x61\x6d\x43\x62\x3a\x72\x65\x63\x6f\x72\x64\x20\x6e\x6f\x20\x64\x61\x74\x61\x2e"
);return ZPB_DB_ERROR;}para.pbIndex=atoi(zresult[(0x95+8134-0x205b)]);para.
pbLocation=atoi(zresult[(0x229a+585-0x24e2)]);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x62\x47\x65\x74\x50\x61\x72\x61\x6d\x43\x62\x3a\x20\x69\x6e\x64\x65\x78\x3d\x25\x64\x2c\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,para.pbIndex,para.pbLocation);memcpy(fvarg,&para,sizeof(para));return ZPB_DB_OK
;}T_zPb_DbResult atPb_GetIndexLocationById(T_zPb_Header*pbPara){CHAR sql[
ZPB_MAX_BYTES_DB]={(0xf47+784-0x1257)};snprintf(sql,sizeof(sql)-
(0x6b3+7718-0x24d8),
"\x73\x65\x6c\x65\x63\x74\x20\x50\x62\x6d\x5f\x69\x6e\x64\x65\x78\x2c\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x69\x64\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,pbPara->pbId);memset(pbPara,(0x145d+4-0x1461),sizeof(
T_zPb_Header));return atPb_ExecDbSql(sql,atPb_DbGetParamCb,pbPara);}VOID 
atPb_SqlNewOneRec(T_zPb_WebContact*pbmPara,CHAR*sql,int len){if(
ZPB_LOCATION_USIM==pbmPara->pbLocation){snprintf(sql,len,"insert into %s (Pbm_index,Location,Number,Type,Name,Anr,Anr1,Email,Sne)\
    			values(\'%d\',\'%d\',\'%s\',\'%d\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')",ZPB_DB_PBM_TABLE,pbmPara->pbIndex,pbmPara->pbLocation,pbmPara->mobilNumber,
pbmPara->pbType,pbmPara->name,pbmPara->homeNumber,pbmPara->officeNumber,pbmPara
->email,pbmPara->sne);}else if(ZPB_LOCATION_AP==pbmPara->pbLocation){snprintf(
sql,len,"insert into %s (Pbm_index,Location,Number,Type,Name,Anr,Anr1,Email,Sne,Pbm_group)\
				values(\'%d\',\'%d\',\'%s\',\'%d\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')",ZPB_DB_PBM_TABLE,pbmPara->pbIndex,pbmPara->pbLocation,pbmPara->mobilNumber,
pbmPara->pbType,pbmPara->name,pbmPara->homeNumber,pbmPara->officeNumber,pbmPara
->email,pbmPara->sne,pbmPara->group);}}T_zPb_DbResult 
atPb_WriteContactToPbmTable(T_zPb_WebContact*pPbRecord,BOOL pbNewFlag){
T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0x1d7f+458-0x1f49)}
;if(NULL==pPbRecord){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x57\x72\x69\x74\x65\x43\x6f\x6e\x74\x61\x63\x74\x54\x6f\x50\x62\x6d\x54\x61\x62\x6c\x65\x3a\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74"
);return ZPB_DB_ERROR_INVALIDPTR;}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x57\x72\x69\x74\x65\x43\x6f\x6e\x74\x61\x63\x74\x54\x6f\x50\x62\x6d\x54\x61\x62\x6c\x65\x3a\x69\x6e\x70\x75\x74\x20\x69\x6e\x64\x65\x78\x20\x69\x73\x20\x25\x64\x2c\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64" "\n"
,pPbRecord->pbIndex,pPbRecord->pbLocation);if(TRUE==pbNewFlag){atPb_SqlNewOneRec
(pPbRecord,sql,sizeof(sql));}else{atPb_SqlModifyOneRec(pPbRecord,sql,sizeof(sql)
);}result=atPb_ExecDbSql(sql,NULL,NULL);if(ZPB_DB_OK==result){if(
ZPB_LOCATION_USIM==pPbRecord->pbLocation){g_zPb_SimIndex[(pPbRecord->pbIndex)]=
PBM_SUCCESS;}else if(ZPB_LOCATION_AP==pPbRecord->pbLocation){g_zPb_ApIndex[(
pPbRecord->pbIndex)]=PBM_SUCCESS;}}return result;}SINT32 atPb_DbCountTableLineCb
(VOID*fvarg,int line,char**zresult,char**lname){if((0x65a+4615-0x1860)>line){
slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x72\x65\x63\x6f\x72\x64\x20\x6e\x6f\x20\x64\x61\x74\x61\x2e"
);return-(0x78+4551-0x123e);}*(int*)fvarg=atoi(zresult[(0x6a6+8101-0x264b)]);
return(0xceb+1904-0x145b);}T_zPb_DbResult atPb_SetSimCapacityTable(
T_zPb_UsimCapacity pbPara){T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[
ZPB_MAX_BYTES_DB]={(0x1566+1770-0x1c50)};UINT32 count=(0x2033+1152-0x24b3);
snprintf(sql,sizeof(sql)-(0x84b+4763-0x1ae5),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73"
,ZPB_DB_SIM_CAPABILITY_TABLE);(VOID)atPb_ExecDbSql(sql,atPb_DbCountTableLineCb,&
count);if((0x6e1+1777-0xdd2)<count){memset(sql,(0x757+6795-0x21e2),sizeof(sql));
snprintf(sql,sizeof(sql)-(0x162a+3581-0x2426),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x53\x69\x6d\x5f\x74\x79\x70\x65\x3e\x3d\x30"
,ZPB_DB_SIM_CAPABILITY_TABLE);result=atPb_ExecDbSql(sql,NULL,NULL);if(ZPB_DB_OK
!=result){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x65\x74\x53\x69\x6d\x43\x61\x70\x61\x63\x69\x74\x79\x54\x61\x62\x6c\x65\x3a\x66\x61\x69\x6c\x21" "\n"
);return result;}}memset(sql,(0x4bd+3044-0x10a1),sizeof(sql));snprintf(sql,
sizeof(sql)-(0x31c+6174-0x1b39),"insert into %s (Sim_type,Max_record_number,Used_record_number,Max_number_len,Max_name_len,Max_anr_len,Max_anr1_len, \
	          Max_email_len,Max_sne_len) values(\'%d\',\'%d\',\'%d\',\'%d\',\'%d\',\'%d\',\'%d\',\'%d\',\'%d\')",ZPB_DB_SIM_CAPABILITY_TABLE,pbPara.simType,pbPara.maxRecordNum,pbPara.
usedRecordNum,pbPara.maxNumberLen,pbPara.maxNameLen,pbPara.maxAnrLen,pbPara.
maxAnr1Len,pbPara.maxEmailLen,pbPara.maxSneLen);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x65\x74\x53\x69\x6d\x43\x61\x70\x61\x63\x69\x74\x79\x54\x61\x62\x6c\x65\x3a\x6f\x6b\x21" "\n"
);return atPb_ExecDbSql(sql,NULL,NULL);}T_zPb_DbResult atPb_SetApCapacityTable()
{T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0xd5+6574-0x1a83)}
;SINT32 count=(0x152b+2958-0x20b9);T_zPb_ApCapacity pbPara={(0x1e7c+1429-0x2411)
};snprintf(sql,sizeof(sql)-(0x26d+5768-0x18f4),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73"
,ZPB_DB_DEVICE_CAPABILITY_TABLE);(VOID)atPb_ExecDbSql(sql,
atPb_DbCountTableLineCb,&count);if((0x68b+1282-0xb8d)<count){memset(sql,
(0x1507+4050-0x24d9),sizeof(sql));snprintf(sql,sizeof(sql)-(0x11c3+1047-0x15d9),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73",
ZPB_DB_DEVICE_CAPABILITY_TABLE);result=atPb_ExecDbSql(sql,NULL,NULL);if(
ZPB_DB_OK!=result){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x65\x74\x41\x70\x43\x61\x70\x61\x63\x69\x74\x79\x54\x61\x62\x6c\x65\x3a\x66\x61\x69\x6c\x21" "\n"
);return result;}}memset(sql,(0x505+7480-0x223d),sizeof(sql));snprintf(sql,
sizeof(sql)-(0x1d1+7314-0x1e62),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_AP);result=atPb_ExecDbSql(sql,
atPb_DbCountTableLineCb,&count);if(ZPB_DB_OK==result){pbPara.usedRecordNum=count
;pbPara.maxRecordNum=ZPB_AP_MAX_RECORD;}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x6d\x61\x78\x5f\x72\x65\x63\x5f\x6e\x75\x6d\x3d\x25\x64\x2c\x75\x73\x65\x64\x3d\x25\x64"
,pbPara.maxRecordNum,pbPara.usedRecordNum);memset(sql,(0x23d6+60-0x2412),sizeof(
sql));snprintf(sql,sizeof(sql)-(0x971+5292-0x1e1c),
"\x69\x6e\x73\x65\x72\x74\x20\x69\x6e\x74\x6f\x20\x25\x73\x20\x28\x4d\x61\x78\x5f\x72\x65\x63\x6f\x72\x64\x5f\x6e\x75\x6d\x62\x65\x72\x2c\x55\x73\x65\x64\x5f\x72\x65\x63\x6f\x72\x64\x5f\x6e\x75\x6d\x62\x65\x72\x29\x20\x76\x61\x6c\x75\x65\x73\x28" "\'" "\x25\x64" "\'" "\x2c" "\'" "\x25\x64" "\'" "\x29"
,ZPB_DB_DEVICE_CAPABILITY_TABLE,pbPara.maxRecordNum,pbPara.usedRecordNum);return
 atPb_ExecDbSql(sql,NULL,NULL);}T_zPb_DbResult atPb_DbGetIndexByGroupCb(VOID*
fvarg,int line,char**zresult,char**lname){T_zPb_ApIndex*pbIndex=NULL;int i=
(0xbfd+3401-0x1946);if((0xabc+664-0xd53)>line){return ZPB_DB_ERROR;}pbIndex=(
T_zPb_ApIndex*)fvarg;i=pbIndex->count;slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x62\x47\x65\x74\x49\x6e\x64\x65\x78\x42\x79\x47\x72\x6f\x75\x70\x43\x62\x20\x65\x6e\x74\x65\x72\x2c\x69\x3d\x25\x64" "\n"
,i);pbIndex->apIndex[i+(0x45+8057-0x1fbd)]=atoi(zresult[(0x9e8+1547-0xff3)]);
slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x67\x65\x74\x5f\x69\x6e\x64\x65\x78\x5f\x62\x79\x5f\x67\x72\x6f\x75\x70\x5f\x63\x62\x3a\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3d\x25\x64"
,pbIndex->apIndex[i+(0xbd+3394-0xdfe)]);pbIndex->count=i+(0xea1+6244-0x2704);
slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x67\x65\x74\x5f\x69\x6e\x64\x65\x78\x5f\x62\x79\x5f\x67\x72\x6f\x75\x70\x5f\x63\x62\x3a\x70\x62\x6d\x20\x63\x6f\x75\x6e\x74\x20\x69\x73\x20\x25\x64"
,pbIndex->count);return ZPB_DB_OK;}T_zPb_DbResult atPb_DelRecFromPbmTableByGroup
(T_zPb_ApIndex*index){T_zPb_DbResult result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]
={(0xf99+1994-0x1763)};SINT32 countByGroup=(0xd98+3894-0x1cce);CHAR pbGroup[
ZPB_PARAM_SIZE20]={(0x6c1+8243-0x26f4)};sc_cfg_get(ZPB_NV_GROUP,pbGroup,sizeof(
pbGroup));snprintf(sql,sizeof(sql)-(0x1f6d+592-0x21bc),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64\x20\x61\x6e\x64\x20\x28\x50\x62\x6d\x5f\x67\x72\x6f\x75\x70\x3d" "\"" "\x25\x73" "\"" "\x29"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_AP,pbGroup);(VOID)atPb_ExecDbSql(sql,
atPb_DbCountTableLineCb,&countByGroup);memset(sql,(0xa86+2180-0x130a),sizeof(sql
));snprintf(sql,sizeof(sql)-(0x471+7115-0x203b),
"\x73\x65\x6c\x65\x63\x74\x20\x50\x62\x6d\x5f\x69\x6e\x64\x65\x78\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64\x20\x61\x6e\x64\x20\x28\x50\x62\x6d\x5f\x67\x72\x6f\x75\x70\x3d" "\"" "\x25\x73" "\"" "\x29"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_AP,pbGroup);result=atPb_ExecDbSql(sql,
atPb_DbGetIndexByGroupCb,index);if(countByGroup==index->count){memset(sql,
(0xe42+375-0xfb9),sizeof(sql));snprintf(sql,sizeof(sql)-(0x239+7976-0x2160),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64\x20\x61\x6e\x64\x20\x28\x50\x62\x6d\x5f\x67\x72\x6f\x75\x70\x3d" "\"" "\x25\x73" "\"" "\x29"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_AP,pbGroup);if(ZPB_DB_OK==atPb_ExecDbSql(sql,NULL
,NULL)){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);}slog(PB_PRINT,
SLOG_DEBUG,
"\x70\x62\x3a\x70\x62\x6d\x3a\x65\x78\x65\x63\x20\x74\x61\x62\x6c\x65\x20\x25\x73\x20\x72\x65\x73\x75\x6c\x74\x20\x25\x64" "\n"
,ZPB_DB_PBM_TABLE,result);}else{return ZPB_DB_ERROR;}(VOID)sc_cfg_set(
ZPB_NV_GROUP,"");return result;}VOID atPb_GetLocationIndexForDel(T_zPb_DelInfo*
recData,SINT32 delTime){T_zPb_Header pbHeader={(0x3ca+9023-0x2709)};if(NULL==
recData){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x47\x65\x74\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x49\x6e\x64\x65\x78\x46\x6f\x72\x44\x65\x6c\x2d\x2d\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74" "\n"
);return;}pbHeader.pbId=recData->delId[delTime];if(ZPB_DB_OK!=
atPb_GetIndexLocationById(&pbHeader)){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x66\x69\x6e\x64\x20\x69\x6e\x64\x65\x78\x20\x61\x6e\x64\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x66\x61\x69\x6c\x65\x64"
);return;}recData->delIndex[delTime]=pbHeader.pbIndex;recData->delLocation=
pbHeader.pbLocation;slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x70\x62\x6d\x3a\x64\x65\x6c\x20\x69\x6e\x64\x65\x78\x3d\x25\x64\x2c\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64\x2c\x64\x65\x6c\x5f\x70\x62\x6d\x5f\x74\x69\x6d\x65\x3d\x25\x64"
,recData->delIndex[delTime],recData->delLocation,delTime);}T_zPb_DbResult 
atPb_DelARecFromPbmTable(T_zPb_DelInfo*pbPara,SINT32 delTime){T_zPb_DbResult 
result=ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0xc9a+2419-0x160d)};if(NULL==
pbPara){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x70\x62\x6d\x3a\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74"
);return ZPB_DB_ERROR_INVALIDPTR;}atPb_GetLocationIndexForDel(pbPara,delTime);
slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x65\x6c\x41\x52\x65\x63\x46\x72\x6f\x6d\x50\x62\x6d\x54\x61\x62\x6c\x65\x20\x65\x6e\x74\x65\x72\x2c\x64\x65\x6c\x54\x69\x6d\x65\x3d\x25\x64\x2c\x69\x64\x3d\x25\x64" "\n"
,delTime,pbPara->delId[delTime]);snprintf(sql,sizeof(sql)-(0xacd+2776-0x15a4),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x69\x64\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,pbPara->delId[delTime]);result=atPb_ExecDbSql(sql,NULL,NULL);
if(ZPB_DB_OK==result){if(ZPB_LOCATION_AP==pbPara->delLocation){g_zPb_ApIndex[(
pbPara->delIndex[delTime])]=PBM_ERROR_NOT_FOUND;}else if(ZPB_LOCATION_USIM==
pbPara->delLocation){g_zPb_SimIndex[(pbPara->delIndex[delTime])]=
PBM_ERROR_NOT_FOUND;}}return result;}VOID atPb_ClearSimPbmIndexArray(VOID){
SINT32 i=(0xf2c+4566-0x2101);for(i=(0xb91+6006-0x2306);(i<=g_zPb_SimIndex[
(0x386+4277-0x143b)])&&(i<(ZPB_SIM_MAX_RECORD+(0x1260+3145-0x1ea8)));i++){
g_zPb_SimIndex[i]=PBM_ERROR_NOT_FOUND;}}VOID atPb_ClearApPbmIndexArray(VOID){
SINT32 i=(0x134f+4839-0x2635);for(i=(0x379+4987-0x16f3);(i<=g_zPb_ApIndex[
(0x1273+5185-0x26b4)])&&(i<(ZPB_AP_MAX_RECORD+(0x391+6156-0x1b9c)));i++){
g_zPb_ApIndex[i]=PBM_ERROR_NOT_FOUND;}}T_zPb_DbResult 
atPb_DelAllRecsFromPbmTable(T_zPb_DelInfo*pbPara){T_zPb_DbResult result=
ZPB_DB_OK;CHAR sql[ZPB_MAX_BYTES_DB]={(0xe8a+4596-0x207e)};if(NULL==pbPara){slog
(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x52\x65\x63\x73\x46\x72\x6f\x6d\x50\x62\x6d\x54\x61\x62\x6c\x65\x3a\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74" "\n"
);return ZPB_DB_ERROR_INVALIDPTR;}snprintf(sql,sizeof(sql)-(0x1f70+1885-0x26cc),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,pbPara->delLocation);result=atPb_ExecDbSql(sql,NULL,NULL);slog
(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x52\x65\x63\x73\x46\x72\x6f\x6d\x50\x62\x6d\x54\x61\x62\x6c\x65\x3a\x65\x78\x65\x63\x20\x74\x61\x62\x6c\x65\x20\x25\x73\x20\x72\x65\x73\x75\x6c\x74\x20\x25\x64"
,ZPB_DB_PBM_TABLE,result);if(ZPB_LOCATION_USIM==pbPara->delLocation){
atPb_ClearSimPbmIndexArray();}else if(ZPB_LOCATION_AP==pbPara->delLocation){
atPb_ClearApPbmIndexArray();}else if(ZPB_LOCATION_ALL==pbPara->delLocation){
atPb_ClearSimPbmIndexArray();atPb_ClearApPbmIndexArray();}return result;}
