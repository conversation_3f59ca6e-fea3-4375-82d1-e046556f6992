
#include <ctype.h>
#include <semaphore.h>
#include <limits.h>
#include "pb_com.h"
#define ZPB_UCS2                    (0xfe4+3278-0x1cb1)
#define ZPB_UCS2_PREFIX             (0x1e2b+1205-0x2260)
#define ZPB_UCS2_PREFIX_LEN         (0x97d+6940-0x2497)
#define ZPB_NON_GSM                     (0x241+7555-0x1fa4)
#define ZPB_GSM_CHARACTER_SET_SIZE      (0x571+6520-0x1e69)
#define ZPB_INIT_LOAD_RECORD_NUM    (0xa8+4892-0x1392)
VOID atPb_CvtChCode(UINT16 basePointer,UINT8 srcData,UINT8*chMsb,UINT8*chLsb);
BOOL atPb_CvtU82ToU80(const UINT8*srcData,UINT32 srcLen,UINT8*destData,UINT32 
destLen);BOOL atPb_CvtU81ToU80(const UINT8*srcData,UINT32 srcLen,UINT8*destData,
UINT32 destLen);extern VOID atPb_ClearSimPbmIndexArray(VOID);extern sem_t 
g_Pb_sem_id;extern T_zPb_optRsp g_PbOptRsp;UINT32 g_zPb_DelIndex=
(0x9da+4710-0x1c40);SINT32 g_zPb_SimIndex[ZPB_SIM_MAX_RECORD+(0xb3+2784-0xb92)]=
{(0x9b4+2605-0x13e1)};SINT32 g_zPb_ApIndex[ZPB_AP_MAX_RECORD+(0x377+1381-0x8db)]
={(0x68+6268-0x18e4)};T_zPb_DelStatusMultiOrAll g_zPb_DelStatusUsim={
(0x1934+2293-0x2229)};const unsigned char G_ZPB_NEWUCS2TOGSM[
ZPB_GSM_CHARACTER_SET_SIZE*(0xbe9+4838-0x1ecd)]={ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,(0x1bb+6970-0x1ceb),ZPB_NON_GSM,ZPB_NON_GSM,
(0x120+3354-0xe2d),ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,(0x1bf1+540-0x1ded),(0xa7f+3089-0x166f),(0x14c5+3086-0x20b1),
(0x1490+3825-0x235e),(0x15f6+2125-0x1e41),(0x1f0+8545-0x232c),
(0x1780+1994-0x1f24),(0x15d0+812-0x18d5),(0xe7+8416-0x219f),(0xf83+2460-0x18f6),
(0xdf3+2669-0x1836),(0x1377+4070-0x2332),(0x18af+1432-0x1e1b),
(0xe37+1546-0x1414),(0xc12+1812-0x12f8),(0x6b4+2313-0xf8e),(0x804+1237-0xca9),
(0x5b9+801-0x8a9),(0x1121+4248-0x2187),(0xe8d+5339-0x2335),(0xb2+3352-0xd96),
(0x8cf+2454-0x1230),(0x381+1255-0x832),(0xa0c+2821-0x14da),(0x9d2+6099-0x216d),
(0x1679+685-0x18ed),(0x1210+3437-0x1f43),(0x40b+5334-0x18a6),(0x218+8224-0x21fc)
,(0x1962+3453-0x26a2),(0x17ab+2872-0x22a5),(0x117+4804-0x139c),
(0x163+6931-0x1c76),(0x1c50+1527-0x2206),(0x168+4448-0x1286),(0xd7b+3136-0x1978)
,(0x18ad+2568-0x2271),(0x1ca0+2353-0x258c),(0x5d3+5221-0x19f2),
(0x107+5361-0x15b1),(0x6e8+8224-0x26c0),(0xe05+1323-0x12e7),(0xc54+6576-0x25ba),
(0xc6b+5960-0x2368),(0x7d+7942-0x1f37),(0xc7+4764-0x1316),(0x166f+3036-0x21fd),
(0x1d4+4070-0x116b),(0xc90+5906-0x2352),(0x62f+2588-0xffa),(0x5a5+5972-0x1ca7),
(0x37f+432-0x4dc),(0x7c2+4623-0x197d),(0x1096+4346-0x213b),(0x1a38+1025-0x1de3),
(0xb64+1418-0x1097),(0x371+3156-0xf6d),(0x13cd+1803-0x1a7f),(0x60b+6342-0x1e77),
(0x7e2+5266-0x1c19),ZPB_NON_GSM,(0xe8c+2835-0x1942),ZPB_NON_GSM,
(0x481+2237-0xd2d),ZPB_NON_GSM,(0x18b5+3236-0x24f8),(0xe7b+5074-0x21eb),
(0x972+1216-0xdcf),(0x15db+3959-0x24ee),(0xcd3+2506-0x1638),(0x195+5341-0x160c),
(0x110f+2173-0x1925),(0x1f86+1804-0x262a),(0x1410+749-0x1694),
(0xdfc+4030-0x1d50),(0xb0f+1609-0x10ed),(0x3c0+8772-0x2598),(0x1ed+6168-0x1998),
(0x11e8+5384-0x2682),(0x5cf+3921-0x14b1),(0xb4f+6249-0x2348),(0x115+28-0xc0),
(0x10c3+3832-0x1f49),(0x24b8+248-0x253d),(0x8a2+6457-0x2167),(0x9d7+7429-0x2667)
,(0x8fc+5926-0x1fac),(0x208+6678-0x1ba7),(0x908+7623-0x2657),(0xdc2+877-0x10b6),
(0x44a+342-0x526),ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,(0x1009+5156-0x23ed),ZPB_NON_GSM,
(0xafa+1588-0x112d),(0x1448+2329-0x1d3d),(0x51a+2117-0xd5c),ZPB_NON_GSM,
(0x1e87+804-0x214c),ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,ZPB_NON_GSM,
(0x58c+3166-0x118a),(0x1bb+744-0x462),(0xa27+1328-0xf16),(0x1355+3068-0x1f10),
(0xf74+3689-0x1d9c),(0x1d76+14-0x1d29),(0x144a+420-0x15e0),(0x6c1+6926-0x21b3),
(0xd06+941-0x10aa),(0x1045+1773-0x16ed),(0xbbb+6381-0x2489),(0x1d7+8790-0x23e8),
(0xce3+1343-0x11dd),(0x457+1856-0xb4e),(0x1023+224-0x10ba),(0x1bd+9625-0x270d),
(0x4da+7586-0x2233),ZPB_NON_GSM,(0x9c1+4738-0x1be6),(0x1417+275-0x14db),
(0x69c+3292-0x1329),(0xec+2231-0x954),(0xef8+6049-0x264a),(0x775+3953-0x168a),
ZPB_NON_GSM,(0xbb7+6554-0x2546),(0x1ed+6857-0x1c61),(0xe61+1787-0x1507),
(0x1138+298-0x120d),(0x19db+2008-0x2155),(0x451+1431-0x98f),ZPB_NON_GSM,
(0x1ae2+2248-0x238c),(0x1291+1543-0x1819),(0x20b0+105-0x20b8),
(0x1de9+301-0x1eb5),(0xb04+934-0xe49),(0xb49+5899-0x21d9),(0x1466+4697-0x26b0),
(0x1e4d+403-0x1fc3),(0x4d7+1405-0xa4b),(0xe89+366-0xff3),(0x188d+2633-0x22d1),
(0x15b+7259-0x1d51),(0x867+2140-0x105e),(0x938+204-0x9fd),(0x1471+3605-0x221d),
(0x1bac+970-0x1f0d),(0x2dd+7783-0x20db),ZPB_NON_GSM,(0x519+8637-0x2659),
(0x188+3112-0xda8),(0x2192+849-0x2474),(0x1007+3379-0x1ccb),(0xffb+1372-0x14e8),
(0x1d18+1729-0x235d),ZPB_NON_GSM,(0x16aa+870-0x1a04),(0x1264+4194-0x22c0),
(0x443+656-0x65e),(0x21fa+212-0x2259),(0x1602+774-0x188a),(0x1042+4690-0x221b),
ZPB_NON_GSM,(0x1c81+2277-0x24ed)};VOID atPb_Init(VOID){UINT32 pbCount=
(0x6bf+7442-0x23d1);g_zPb_SimIndex[(0x981+1159-0xe08)]=(0x502+4641-0x1723);
sc_cfg_set(ZPB_NV_INIT,ZPB_LOADING);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_OPERATE_SUC);for(pbCount=(0xd99+5250-0x221a);pbCount<=ZPB_SIM_MAX_RECORD;
pbCount++){g_zPb_SimIndex[pbCount]=PBM_ERROR_NOT_FOUND;}if(ZPB_DB_OK!=
atPb_CreatDb()){slog(PB_PRINT,SLOG_ERR,"pb:atPb_Init:create pbm table failed\n")
;return;}if(ZPB_DB_OK!=atPb_InitApIndex()){slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x49\x6e\x69\x74\x3a\x69\x6e\x69\x74\x20\x61\x70\x49\x6e\x64\x65\x78\x20\x66\x61\x69\x6c\x65\x64" "\n"
);return;}(VOID)atPb_SetApCapacityTable();}VOID atPb_CfgPbNvInit(VOID){(VOID)
sc_cfg_set(ZPB_NV_USIMINDEXMIN,"\x30");(VOID)sc_cfg_set(ZPB_NV_USIMINDEXMAX,
"\x30");(VOID)sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x6e\x6f");}int 
atpb_CvtUcs2ToAlphaField(char*src,int srcLen,char*dest){int i=
(0x167d+1375-0x1bdc);int min=32767;int max=(0x1968+420-0x1b0c);int temp=
(0xbb9+4421-0x1cfe);int outOff=(0x82b+7552-0x25ab);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x73\x72\x63\x4c\x65\x6e\x3d\x25\x64" "\n"
,srcLen);if(srcLen<=(0x1ae7+1752-0x21bf)||src==NULL||dest==NULL){return-
(0x1149+4070-0x212e);}if(srcLen<=(0x12e4+4149-0x2317)){dest[(0x1192+1409-0x1713)
]=(0x16c6+600-0x189e);memcpy(dest+(0x6ea+7542-0x245f),src,srcLen);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x75\x73\x65\x20\x38\x30\x20\x63\x6f\x64\x65\x2c\x20\x6c\x65\x6e\x20\x3d\x25\x64" "\n"
,srcLen+(0x983+4600-0x1b7a));return srcLen+(0xcd8+2720-0x1777);}for(i=
(0xe8d+228-0xf71);i<srcLen;i+=(0x14a9+404-0x163b)){if(src[i]!=
(0x190d+1697-0x1fae)){temp=(int)(((src[i]<<(0x231+6906-0x1d23))&65280)|(src[i+
(0x1e7c+1585-0x24ac)]&(0x28c+8113-0x213e)));
#if (0x1184+274-0x1296) 
if(temp<(0x222+2890-0xd6c)){max=min+(0x18bf+870-0x1ba3);break;}
#endif            
if(min>temp){min=temp;}if(max<temp){max=temp;}}}printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x6d\x69\x6e\x3d\x25\x64\x2c\x20\x6d\x61\x78\x3d\x25\x64" "\n"
,min,max);if((max-min)<(0x533+1194-0x95c)){if((unsigned char)(min&
(0x7ba+3193-0x13b3))==(unsigned char)(max&(0x35d+3262-0xf9b))){dest[
(0x1686+1302-0x1b9b)]=(unsigned char)(srcLen/(0xf7+1540-0x6f9));dest[
(0xac5+1354-0x100f)]=(unsigned char)(0x198a+1860-0x204d);min=(int)(min&32640);
dest[(0x716+3947-0x167f)]=(unsigned char)((min>>(0x40a+8939-0x26ee))&
(0xf4a+1360-0x139b));outOff=(0x10b3+3761-0x1f61);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x75\x73\x65\x20\x38\x31\x20\x63\x6f\x64\x65" "\n"
);}else{dest[(0x2+6255-0x1870)]=(unsigned char)(srcLen/(0x2045+352-0x21a3));dest
[(0x384+1836-0xab0)]=(unsigned char)(0x110b+444-0x1245);dest[
(0x12b8+3161-0x1f0f)]=(unsigned char)((min>>(0x1865+737-0x1b3e))&
(0x19ed+154-0x1988));dest[(0x14b5+1321-0x19db)]=(unsigned char)(min&
(0x493+7419-0x208f));outOff=(0x8a4+5960-0x1fe8);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x75\x73\x65\x20\x38\x32\x20\x63\x6f\x64\x65" "\n"
);}for(i=(0x1457+1546-0x1a61);i<srcLen;i+=(0xa7d+4427-0x1bc6)){if(src[i]==
(0x17a7+690-0x1a59)){dest[outOff]=(unsigned char)(src[i+(0x161d+1467-0x1bd7)]&
(0x110+137-0x11a));}else{temp=(int)((((src[i]<<(0x1b53+539-0x1d66))&65280)|(src[
i+(0x28c+5144-0x16a3)]&(0x170c+3253-0x22c2)))-min);dest[outOff]=(unsigned char)(
temp|(0x1278+1692-0x1894));}outOff++;}printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x20\x6f\x75\x74\x4f\x66\x66\x3d\x25\x64\x2c\x64\x65\x73\x74\x5b\x25\x64\x5d\x3d\x25\x78" "\n"
,outOff,outOff,dest[outOff]);return outOff;}dest[(0x42+4943-0x1391)]=
(0x547+8017-0x2418);memcpy(dest+(0xa64+5828-0x2127),src,srcLen);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x70\x62\x5f\x43\x76\x74\x55\x63\x73\x32\x54\x6f\x41\x6c\x70\x68\x61\x46\x69\x65\x6c\x64\x2c\x20\x75\x73\x65\x20\x38\x30\x20\x63\x6f\x64\x65\x2c\x20\x6c\x65\x6e\x20\x3d\x25\x64" "\n"
,srcLen+(0x153d+4394-0x2666));return srcLen+(0x333+3051-0xf1d);}int 
atPb_GetU80Code(const UINT8*srcCode,UINT32 srcCodeLen,UINT8*destCode,UINT32 
destCodeLen){assert(destCode!=NULL&&srcCode!=NULL);if(srcCodeLen==
(0x351+4913-0x1682)||destCodeLen==(0x1b1a+2109-0x2357)||destCodeLen<srcCodeLen){
return-(0x14+4650-0x123d);}memset(destCode,(0x4f2+3210-0x117c),destCodeLen);
destCode[(0xe83+3926-0x1dd9)]=(0x537+7751-0x22fe);printf(
"\x63\x68\x65\x6e\x6a\x69\x65\x20\x2d\x2d\x2d\x2d\x2d\x61\x74\x50\x62\x5f\x47\x65\x74\x55\x38\x30\x43\x6f\x64\x65\x2d\x2d\x2d\x2d\x2d\x73\x72\x63\x43\x6f\x64\x65\x5b\x30\x5d\x20\x3d\x20\x25\x64" "\n"
,srcCode[(0x13a8+1118-0x1806)]);switch(srcCode[(0x11c5+1474-0x1787)]){case
(0x346+7600-0x2076):{memcpy(destCode,srcCode,srcCodeLen);return srcCodeLen;}case
(0x332+7141-0x1e96):{atPb_CvtU81ToU80(srcCode+(0xc4d+1921-0x13cd),srcCodeLen-
(0xf86+4551-0x214c),destCode+(0x5c8+7662-0x23b5),destCodeLen-
(0x1dec+2182-0x2671));return srcCode[(0x343+7378-0x2014)]*(0x1059+3365-0x1d7c)+
(0x1cb5+1985-0x2475);}case(0x4c1+5239-0x18b6):{atPb_CvtU82ToU80(srcCode+
(0x321+2730-0xdca),srcCodeLen-(0xa69+2825-0x1571),destCode+(0x86c+5563-0x1e26),
destCodeLen-(0xe77+4946-0x21c8));return srcCode[(0x1a2+6806-0x1c37)]*
(0xb51+3871-0x1a6e)+(0x9e9+7155-0x25db);}default:{return-(0x19b2+1943-0x2148);}}
}BOOL atPb_CvtU82ToU80(const UINT8*srcData,UINT32 srcLen,UINT8*destData,UINT32 
destLen){UINT8 chNum=(0x31+5359-0x1520);UINT16 basePointer=(0x2df+4613-0x14e4);
UINT8 iCurChar=(0x42f+5640-0x1a37);UINT32 iCurSrc=(0x5cb+7189-0x21e0);chNum=(
UINT32)srcData[(0x597+3869-0x14b4)];basePointer=(UINT16)srcData[
(0x14b5+2932-0x2028)];basePointer=basePointer<<(0xfaa+5301-0x2457);basePointer=
basePointer+srcData[(0x30+6124-0x181a)];if(chNum*(0x72c+3533-0x14f7)>destLen){
return FALSE;}for(iCurSrc=(0x1b7f+2896-0x26cc);iCurSrc<srcLen&&iCurChar<chNum;
iCurSrc++,iCurChar++){UINT8*curDest=destData+(0x1af+7267-0x1e10)*iCurChar;
atPb_CvtChCode(basePointer,srcData[iCurSrc],curDest,curDest+(0x926+3036-0x1501))
;}return TRUE;}BOOL atPb_CvtU81ToU80(const UINT8*srcData,UINT32 srcLen,UINT8*
destData,UINT32 destLen){UINT8 chNum=(0x2438+37-0x245d);UINT16 basePointer=
(0x1d85+2409-0x26ee);UINT8 iCurChar=(0x1817+2821-0x231c);UINT32 iCurSrc=
(0xe72+3788-0x1d3e);chNum=srcData[(0x148+5435-0x1683)];basePointer=((UINT16)
srcData[(0xaba+702-0xd77)])<<(0x5d9+2382-0xf20);if(chNum*(0x8a9+7483-0x25e2)>
destLen){return FALSE;}for(iCurSrc=(0x5a+6709-0x1a8d);iCurSrc<srcLen&&iCurChar<
chNum;iCurSrc++,iCurChar++){UINT8*curDest=destData+(0x6c9+1915-0xe42)*iCurChar;
atPb_CvtChCode(basePointer,srcData[iCurSrc],curDest,curDest+(0xfc6+4930-0x2307))
;}return TRUE;}VOID atPb_CvtChCode(UINT16 basePointer,UINT8 srcData,UINT8*chMsb,
UINT8*chLsb){UINT16 curChar=(0x574+6046-0x1d12);assert(chMsb!=NULL&&chLsb!=NULL)
;if((srcData&(0x85d+1905-0xf4e))==(0x494+509-0x691)){curChar=srcData;}else{
curChar=basePointer+(srcData&(0xd5a+5510-0x2261));}*chMsb=(UINT8)(curChar>>
(0x20eb+561-0x2314));*chLsb=(UINT8)((curChar<<(0xd8+1051-0x4eb))>>
(0xab6+5454-0x1ffc));return;}int atPb_String2Bytes(const char*pSrc,unsigned char
*pDst,int nSrcLength){int i=(0x562+3557-0x1347);if(pSrc==NULL||pDst==NULL||
nSrcLength<(0x56c+5514-0x1af6)){return-(0x6d+7100-0x1c28);}for(i=
(0x588+914-0x91a);i<nSrcLength;i+=(0x197a+3344-0x2688)){if(*pSrc>=
((char)(0x12fc+464-0x149c))&&*pSrc<=((char)(0x8d3+7451-0x25b5))){*pDst=(*pSrc-
((char)(0x1975+988-0x1d21)))<<(0x22c+6107-0x1a03);}else{*pDst=((toupper(*pSrc)-
((char)(0x90a+1449-0xe72)))+(0x56a+101-0x5c5))<<(0x2b4+9046-0x2606);}pSrc++;if(*
pSrc>=((char)(0x1666+1258-0x1b20))&&*pSrc<=((char)(0xa1a+3366-0x1707))){*pDst|=*
pSrc-((char)(0x1474+3370-0x216e));}else{*pDst|=(toupper(*pSrc)-
((char)(0x273+854-0x588)))+(0x49d+5451-0x19de);}pSrc++;pDst++;}return nSrcLength
/(0x124b+3037-0x1e26);}int atPb_Bytes2String(const unsigned char*pSrc,char*pDst,
int nSrcLength){const char tab[]=
"\x30\x31\x32\x33\x34\x35\x36\x37\x38\x39\x41\x42\x43\x44\x45\x46";int i=
(0xa72+4732-0x1cee);if(pSrc==NULL||pDst==NULL||nSrcLength<(0x1680+3175-0x22e7)){
return-(0xd77+1405-0x12f3);}for(i=(0x162d+2398-0x1f8b);i<nSrcLength;i++){*pDst++
=tab[*pSrc>>(0x1831+2486-0x21e3)];*pDst++=tab[*pSrc&(0x227c+809-0x2596)];pSrc++;
}*pDst='\0';return nSrcLength*(0x5e8+7998-0x2524);}static VOID 
atPb_WebRecodeShow(T_zPb_WebContact const*pFromweb){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x49\x6e\x64\x65\x78\x3d\x25\x64" "\n"
,pFromweb->pbIndex);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x4e\x61\x6d\x65\x3d\x25\x73" "\n",
pFromweb->name);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x50\x62\x4e\x75\x6d\x31\x3d\x25\x73" "\n"
,pFromweb->mobilNumber);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x50\x62\x4e\x75\x6d\x32\x3d\x25\x73" "\n"
,pFromweb->homeNumber);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x50\x62\x4e\x75\x6d\x33\x3d\x25\x73" "\n"
,pFromweb->officeNumber);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x72\x6f\x6d\x77\x65\x62\x2e\x65\x6d\x61\x69\x6c\x3d\x25\x73" "\n"
,pFromweb->email);}int atPb_IintPbErr(UINT8*pErrCode){sc_cfg_set(ZPB_NV_INIT,
ZPB_LOAD_ERROR);return TRUE;}int atPb_ScpbrSetErr(UINT8*pErrCode){printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x50\x62\x5f\x53\x63\x70\x62\x72\x53\x65\x74\x45\x72\x72\x20\x66\x61\x69\x6c\x20\x61\x6e\x64\x20\x72\x65\x74\x75\x72\x6e" "\n"
);sc_cfg_set(ZPB_NV_INIT,ZPB_OPERATE_SUC);return TRUE;}VOID atBase_IintPbOk(VOID
){sc_cfg_set(ZPB_NV_INIT,ZPB_OPERATE_SUC);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x50\x62\x5f\x53\x65\x6e\x64\x53\x63\x70\x62\x72\x53\x65\x74\x20\x61\x74\x42\x61\x73\x65\x5f\x49\x69\x6e\x74\x50\x62\x4f\x6b\x20" "\n"
);}VOID atPb_RecvCpbsReadRsp(T_zPb_AtCpbsReadRes*cpbsInd){CHAR resInfo[
(0x23bb+364-0x2513)]={(0x2f3+3295-0xfd2)};if(strncmp(cpbsInd->locType,"\x53\x4d"
,(0x1ab7+2517-0x248a))==(0xc78+2385-0x15c9)){(VOID)snprintf(resInfo,
(0x1639+2248-0x1eed),"\x25\x64",cpbsInd->usedEntries);(VOID)sc_cfg_set(
NV_PB_USEDENTRIES,resInfo);memset(resInfo,(0x6d9+5854-0x1db7),(0x2df+2439-0xc52)
);(VOID)snprintf(resInfo,(0x1420+4485-0x2591),"\x25\x64",cpbsInd->totalEntries);
(VOID)sc_cfg_set(NV_PB_TOTALENTRIES,resInfo);g_zPb_SimIndex[(0xb1f+6667-0x252a)]
=(UINT32)(cpbsInd->totalEntries);}else{printf(
"\x61\x74\x50\x62\x5f\x52\x65\x63\x76\x43\x70\x62\x73\x52\x65\x61\x64\x52\x73\x70\x20\x6e\x6f\x74\x20\x53\x4d\x3a\x25\x73" "\n"
,cpbsInd->locType);}}static VOID atPb_SetScpbrResToNv(CHAR const*pbNvKeyWord,
UINT32 len){char converted[(0x1f9+1513-0x7d8)]={(0x1e86+1223-0x234d)};assert(
pbNvKeyWord!=ZUFI_NULL);(VOID)snprintf(converted,(0x928+3369-0x1647),"\x25\x64",
len);(VOID)sc_cfg_set(pbNvKeyWord,converted);}VOID atPb_ScpbrTestRsp(
T_zPb_AtScpbrTestRes*scpbsInd){T_zPb_UsimCapacity pbPara={(0x13f+8040-0x20a7)};
CHAR pbUsed[(0x1ed4+1384-0x240a)]={(0xb79+4394-0x1ca3)};sc_cfg_get(
NV_PB_USEDENTRIES,pbUsed,sizeof(pbUsed));g_zPb_DelIndex=(UINT32)scpbsInd->
minIndex;pbPara.simType=ZPB_USIM;pbPara.maxRecordNum=scpbsInd->maxIndex;pbPara.
usedRecordNum=atoi(pbUsed);pbPara.maxNumberLen=scpbsInd->maxNumberLen;pbPara.
maxNameLen=scpbsInd->maxTextLen;pbPara.maxAnrLen=scpbsInd->maxNumberLen;pbPara.
maxAnr1Len=scpbsInd->maxNumberLen;pbPara.maxEmailLen=scpbsInd->maxEmailLen;
atPb_SetScpbrResToNv(ZPB_NV_USIMINDEXMIN,(UINT32)scpbsInd->minIndex);
atPb_SetScpbrResToNv(ZPB_NV_USIMINDEXMAX,(UINT32)scpbsInd->maxIndex);(VOID)
atPb_SetSimCapacityTable(pbPara);}int atPb_SendScpbrSet_repeat(sem_t tSemId){int
 min=(0xce8+5989-0x244d);int max=(0xbe8+6492-0x2544);int res=(0x8e5+1739-0xfb0);
int index=(0x1a+609-0x27b);CHAR pbMin[(0xdfa+2486-0x177e)]={(0x15d1+2340-0x1ef5)
};CHAR pbMax[(0x1406+2946-0x1f56)]={(0x22b9+771-0x25bc)};int indexmin=
(0xee4+4638-0x2102);int indexmax=(0x2b7+1490-0x889);T_zPb_ScpbrReadRes 
scpbrReadInfo={(0x475+8752-0x26a5)};sc_cfg_get(ZPB_NV_USIMINDEXMIN,pbMin,sizeof(
pbMin));sc_cfg_get(ZPB_NV_USIMINDEXMAX,pbMax,sizeof(pbMax));(VOID)sc_cfg_set(
ZPB_NV_INIT,ZPB_LOADING);if(strcmp(pbMin,"\x30")==(0x1514+3509-0x22c9)||strcmp(
pbMax,"\x30")==(0x644+8090-0x25de)){sc_cfg_set(ZPB_NV_INIT,ZPB_OPERATE_SUC);
printf(
"\x61\x74\x50\x62\x5f\x53\x65\x6e\x64\x53\x63\x70\x62\x72\x53\x65\x74\x20\x63\x61\x72\x64\x20\x75\x6e\x73\x75\x70\x70\x6f\x72\x74\x20\x70\x62" "\n"
);return-(0x3e4+1126-0x849);}sscanf(pbMin,"\x25\x64",&min);sscanf(pbMax,
"\x25\x64",&max);if((min<(0x876+3945-0x17df)||min>INT_MAX-(0xde8+787-0x10fa))||(
max<(0x902+5827-0x1fc5)||max>INT_MAX-(0x1383+1656-0x19fa))){printf(
"\x61\x74\x50\x62\x5f\x53\x65\x6e\x64\x53\x63\x70\x62\x72\x53\x65\x74\x20\x70\x62\x20\x6e\x75\x6d\x20\x65\x72\x72\x20\x6d\x69\x6e\x3a\x25\x64\x2c\x20\x6d\x61\x78\x3a\x25\x64" "\n"
,min,max);return-(0xbe4+3084-0x17ef);}while((0xb9c+5224-0x2003)){if(indexmin<min
){indexmin=min;indexmax=min+ZPB_INIT_LOAD_RECORD_NUM-(0x7d+1704-0x724);if(
indexmax>max){indexmax=max;break;}printf(
"\x5b\x50\x42\x5d\x20\x31\x31\x31\x20\x69\x6e\x64\x65\x78\x6d\x69\x6e\x3d\x25\x64\x2c\x20\x69\x6e\x64\x65\x78\x6d\x61\x78\x3d\x25\x64\x2c\x28\x25\x64\x2d\x25\x64\x29" "\n"
,indexmin,indexmax,min,max);}else{indexmin=indexmin+ZPB_INIT_LOAD_RECORD_NUM;if(
indexmin>max){printf(
"\x5b\x50\x42\x5d\x20\x33\x33\x33\x20\x69\x6e\x64\x65\x78\x6d\x69\x6e\x3d\x25\x64\x2c\x20\x69\x6e\x64\x65\x78\x6d\x61\x78\x3d\x25\x64\x2c\x28\x25\x64\x2d\x25\x64\x29" "\n"
,indexmin,indexmax,min,max);break;}indexmax=indexmax+ZPB_INIT_LOAD_RECORD_NUM;if
(indexmax>max){indexmax=max;}printf(
"\x5b\x50\x42\x5d\x20\x32\x32\x32\x20\x69\x6e\x64\x65\x78\x6d\x69\x6e\x3d\x25\x64\x2c\x20\x69\x6e\x64\x65\x78\x6d\x61\x78\x3d\x25\x64\x2c\x28\x25\x64\x2d\x25\x64\x29" "\n"
,indexmin,indexmax,min,max);}scpbrReadInfo.minIndex=indexmin;scpbrReadInfo.
maxIndex=indexmax;res=ipc_send_message(MODULE_ID_PB,MODULE_ID_AT_CTL,
MSG_CMD_READ_PB_REQ,sizeof(T_zPb_ScpbrReadRes),&scpbrReadInfo,
(0x21a0+204-0x226c));sem_wait(&g_Pb_sem_id);if(g_PbOptRsp.result==-
(0x372+905-0x6fa)){break;}}return g_PbOptRsp.result;}
#if (0x19a2+2392-0x22fa)
int atPb_SendScpbrSet(PSTR pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){UINT32 
indexmin=(0xa11+2086-0x1237);UINT32 indexmax=(0x41b+1067-0x846);CHAR atcmdMsg[
(0x715+326-0x815)]={(0xa6a+4984-0x1de2)};int res=(0x4e4+6895-0x1fd3);CHAR pbMin[
(0x1094+671-0x1301)]={(0x3b2+1192-0x85a)};CHAR pbMax[(0x1828+2705-0x2287)]={
(0x1d76+1164-0x2202)};sc_cfg_get(ZPB_NV_USIMINDEXMIN,pbMin,sizeof(pbMin));
sc_cfg_get(ZPB_NV_USIMINDEXMAX,pbMax,sizeof(pbMax));(VOID)sc_cfg_set(ZPB_NV_INIT
,ZPB_LOADING);if(strcmp(pbMin,"\x30")==(0xe40+5714-0x2492)||strcmp(pbMax,"\x30")
==(0x5d4+5903-0x1ce3)){printf(
"\x61\x74\x50\x62\x5f\x53\x65\x6e\x64\x53\x63\x70\x62\x72\x53\x65\x74\x20\x63\x61\x72\x64\x20\x75\x6e\x73\x75\x70\x70\x6f\x72\x74\x20\x70\x62" "\n"
);return ZAT_RESULT_OK;}sscanf(pbMin,"\x25\x64",&indexmin);sscanf(pbMax,
"\x25\x64",&indexmax);(VOID)snprintf(atcmdMsg,sizeof(atcmdMsg),
"\x41\x54\x5e\x53\x43\x50\x42\x52\x3d\x25\x64\x2c\x25\x64" "\r\n",indexmin,
indexmax);pthread_mutex_lock(&smsdb_mutex);res=zSvr_SendAtSyn(ZAT_SCPBR_SET_CMD,
atcmdMsg,cid,pAtRst,atRstSize);pthread_mutex_unlock(&smsdb_mutex);return res;}
#endif
UINT8 atPb_EncodeText(CHAR*pbDst,CHAR const*pbTextSrc,UINT32 dstLen){UINT8 i=
(0x138d+3608-0x21a5);CHAR strTarget[ZPB_TEXT_SIZE_BYTES]={(0x918+1734-0xfde)};if
(pbTextSrc!=ZUFI_NULL){if(strlen(pbTextSrc)*(0xfc7+1010-0x13b5)+
ZPB_UCS2_PREFIX_LEN<dstLen){snprintf((CHAR*)&strTarget[(0xc0b+4787-0x1ebe)],
sizeof(strTarget),"\x25\x30\x32\x58",ZPB_UCS2_PREFIX);for(i=(0x1442+2904-0x1f9a)
;(i<strlen(pbTextSrc))&&(i*(0x1fbd+419-0x215c)+ZPB_UCS2_PREFIX_LEN<dstLen);i++){
snprintf(strTarget+i*(0xc85+2221-0x152e)+ZPB_UCS2_PREFIX_LEN,sizeof(strTarget)-i
*(0x13c+5024-0x14d8)-ZPB_UCS2_PREFIX_LEN,"\x30\x30\x25\x30\x32\x58",pbTextSrc[i]
);}strncpy(pbDst,strTarget,dstLen-(0x816+2955-0x13a0));return ZUFI_SUCC;}}return
 ZUFI_FAIL;}VOID atPb_ScpbrSetRsp(T_zPb_ScpbrSetRes*atRes){T_zPb_WebContact 
pbRecord={(0x2473+272-0x2583)};CHAR pbDst[ZPB_TEXT_SIZE_BYTES]={
(0x97b+3368-0x16a3)};CHAR text[ZPB_TEXT_SIZE_BYTES]={(0x6a0+5158-0x1ac6)};int 
text_len=(0x2490+315-0x25cb);int tmp_len=(0x1581+32-0x15a1);CHAR tmp[
ZPB_TEXT_SIZE_BYTES]={(0xce8+990-0x10c6)};if(atRes->coding!=ZPB_UCS2){if(
atPb_EncodeText(pbDst,atRes->text,ZPB_TEXT_SIZE_BYTES)==ZUFI_SUCC){strncpy(atRes
->text,pbDst+(0x414+1546-0xa1c),sizeof(atRes->text)-(0x1038+3797-0x1f0c));}else{
slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x53\x63\x70\x62\x72\x53\x65\x74\x52\x73\x70\x65\x6e\x63\x6f\x64\x65\x20\x65\x72\x72\x21\x2e" "\n"
);return;}}else{text_len=atPb_String2Bytes(&atRes->text,&text,strlen(atRes->text
));tmp_len=atPb_GetU80Code(&text,text_len,&tmp,ZPB_TEXT_SIZE_BYTES);if(tmp_len<
(0x103d+5755-0x26b8))return;memset(&text,(0x1657+4131-0x267a),
ZPB_TEXT_SIZE_BYTES);text_len=atPb_Bytes2String(&tmp,&text,tmp_len);if(text_len>
(0x1f1+102-0x255)){memset(&atRes->text,(0x260+7318-0x1ef6),ZPB_TEXT_SIZE_BYTES);
memcpy(&atRes->text,text+(0xf2+3029-0xcc5),strlen(text)-(0x1a4c+3069-0x2647));}}
pbRecord.pbId=-(0x11fd+927-0x159b);pbRecord.pbIndex=(SINT32)atRes->index;strncpy
(pbRecord.name,atRes->text,sizeof(pbRecord.name)-(0x961+6422-0x2276));strncpy(
pbRecord.mobilNumber,atRes->number1,sizeof(pbRecord.mobilNumber)-
(0x1a64+1569-0x2084));strncpy(pbRecord.officeNumber,atRes->number2,sizeof(
pbRecord.officeNumber)-(0x1ff8+1640-0x265f));strncpy(pbRecord.homeNumber,atRes->
number3,sizeof(pbRecord.homeNumber)-(0xed4+472-0x10ab));strncpy(pbRecord.email,
atRes->email,sizeof(pbRecord.email)-(0x935+2610-0x1366));pbRecord.pbLocation=
ZPB_LOCATION_USIM;(VOID)atPb_LoadARecToPbmTable(&pbRecord);}VOID 
atPb_SetDelStatusMultOrAll(){if(g_zPb_DelStatusUsim.dealFailNum>
(0xd9a+1161-0x1223)){if(g_zPb_DelStatusUsim.dealSuccNum>(0xcc7+4849-0x1fb8)){(
VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_MUL_DEL_PART_SUC);}else{(VOID)sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_OPERATE_SUC);}}
#if (0xd29+290-0xe4b)
VOID atPb_RecvScpbwAddErr(UINT8*pErrCode){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_NEW_ERROR);if((strncmp(pErrCode,ZAT_ERRCODE_MEMORY_FULL,(0x87a+2656-0x12d8))
==(0x128b+3700-0x20ff))){(VOID)sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x79\x65\x73");
printf(
"\x70\x62\x3a\x52\x65\x63\x76\x20\x43\x4d\x45\x20\x45\x72\x72\x43\x6f\x64\x65\x3a\x20\x32\x30\x2e" "\n"
);}else{printf(
"\x70\x62\x3a\x52\x65\x63\x76\x20\x41\x74\x20\x53\x63\x70\x62\x77\x20\x61\x64\x64\x20\x45\x72\x72\x2e" "\n"
);}}
#endif
VOID atPb_RecvScpbwModifyErr(UINT8*pErrCode){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_NEW_ERROR);printf(
"\x70\x62\x3a\x52\x65\x63\x76\x20\x41\x74\x20\x53\x63\x70\x62\x77\x20\x6d\x6f\x64\x69\x66\x79\x20\x65\x72\x72\x2e" "\n"
);}VOID pb_AsctoHex(UINT16*pcHex,SINT32 iDstLen,UINT8 const*pcASC,SINT32 iSrcLen
){UINT32 iOddOrEven=(0x181+4342-0x1277);UINT8 iTemp=(0x1344+3903-0x2283);SINT32 
i=(0x1437+2501-0x1dfc);if((NULL==pcHex)||(NULL==pcASC)||((0x1f73+104-0x1fdb)==
iDstLen)||(iDstLen<(iSrcLen/(0xc3a+3565-0x1a25)+(0xd85+5577-0x234d)))){return;}
for(i=(0x39c+6992-0x1eec);i<iSrcLen;i++){if(iOddOrEven%(0xad2+368-0xc40)==
(0x7e9+425-0x992)){iTemp=*(pcASC+i);if((iTemp>=((char)(0xe93+6059-0x260e)))&&(
iTemp<=((char)(0x8b9+1772-0xf6c)))){*(pcHex+i/(0x14e8+4579-0x26c9))|=(UINT8)(
iTemp-(0x13ad+3890-0x22af))<<(0xc1+5489-0x162e);}else{*(pcHex+i/
(0x159a+436-0x174c))|=(UINT8)(iTemp-(0x936+1467-0xeba))<<(0x669+1397-0xbda);}}
else{iTemp=*(pcASC+i);if((iTemp>=((char)(0x82a+2838-0x1310)))&&(iTemp<=
((char)(0x692+8074-0x25e3)))){*(pcHex+i/(0x2ac+7193-0x1ec3))|=iTemp-
(0x981+4250-0x19eb);}else{*(pcHex+i/(0x65f+7168-0x225d))|=iTemp-
(0x8aa+5574-0x1e39);}}iOddOrEven++;}}SINT32 atPb_IfUcs2IsSMS7(UINT16*psUcs2,
SINT32 iLength){int iRetVal=(0x105a+1854-0x1797);char cTemp;int i=
(0x9d8+1560-0xff0);if(NULL==psUcs2){return-(0xa70+4350-0x1b6d);}for(i=
(0x199c+1031-0x1da3);i<iLength;i++){if((0x967+7797-0x26dc)>psUcs2[i]){switch(
psUcs2[i]){case(0x258+9174-0x2622):case(0x19d+9324-0x25ae):case
(0x24a6+140-0x24d6):case(0xcf4+5289-0x2140):case(0x2ff+6240-0x1b01):case
(0x2c3+1010-0x63a):case(0x1c2b+295-0x1cd6):case(0x59a+944-0x8cd):case
(0x160b+1784-0x1c85):case(0x180+1259-0x5c7):case(0x1888+83-0x18bb):{break;}
default:{cTemp=(char)G_ZPB_NEWUCS2TOGSM[psUcs2[i]];if(ZPB_NON_GSM==cTemp){
iRetVal=(0x3dc+5384-0x18e4);}break;}}}else{switch(psUcs2[i]){case
(0x913+521-0x788):case(0xf05+2076-0x137b):case(0x5ba+751-0x516):case
(0x4c2+9159-0x24ee):case(0x16b6+1917-0x1a8a):case(0x21a8+1495-0x23df):case
(0x4a4+8814-0x234a):case(0x7bb+4299-0x14e3):case(0x480+2745-0xb81):case
(0x151f+1005-0x156e):{break;}default:{iRetVal=(0x1eeb+372-0x205f);break;}}}if(
(0x1fd4+539-0x21ef)==iRetVal){break;}}return iRetVal;}static UINT8 
atPb_EncodeNameToUcs2(char*pbDst,UINT32 iDstLen,char const*pbSrc){UINT16 acHex[
(0x2b8+4729-0x1431)]={(0x364+2584-0xd7c)};SINT32 srclen=(0x2348+679-0x25ef);
SINT32 rest=(0xe5d+4474-0x1fd7);assert(pbDst!=NULL&&pbSrc!=NULL);srclen=(SINT32)
strlen(pbSrc);slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x50\x62\x5f\x45\x6e\x63\x6f\x64\x65\x4e\x61\x6d\x65\x54\x6f\x55\x63\x73\x32\x20\x69\x6e\x70\x75\x74\x20\x70\x73\x53\x72\x63\x3d\x25\x73\x2c\x69\x53\x72\x63\x4c\x65\x6e\x3d\x25\x64" "\n"
,pbSrc,srclen);pb_AsctoHex(acHex,(0x26c+8372-0x2220),(UINT8*)pbSrc,srclen);rest=
atPb_IfUcs2IsSMS7(acHex,srclen/(0xdb0+4108-0x1db8));if(rest==(0x448+7225-0x2080)
){return atPb_EncodeText(pbDst,pbSrc,iDstLen);}else if(rest==
(0x1409+4035-0x23cc)){if(strlen(pbSrc)+(0x110c+5071-0x24d9)<iDstLen){memcpy(
pbDst,"\x38\x30",(0x576+4081-0x1565));memcpy(pbDst+(0x182b+3709-0x26a6),pbSrc,
srclen);return ZUFI_SUCC;}}return ZUFI_FAIL;}SINT32 atPb_FindIdleIndex(
T_zPb_WebContact const*pbRecv,BOOL pbNewFlag){SINT32 count=(0xc5c+2907-0x17b6);
SINT32 total=(0x2d9+8578-0x245b);SINT32*IndexType=NULL;if((NULL==pbRecv)){slog(
PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x66\x69\x6e\x64\x5f\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3a\x74\x68\x65\x20\x70\x61\x72\x61\x20\x6f\x66\x20\x66\x69\x6e\x64\x5f\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x20\x61\x72\x65\x20\x4e\x55\x4c\x4c" "\n"
);return-(0x116c+5361-0x265c);}if(TRUE==pbNewFlag){if(ZPB_LOCATION_USIM==pbRecv
->pbLocation){total=g_zPb_SimIndex[(0x3e7+3868-0x1303)];IndexType=g_zPb_SimIndex
;}else if(ZPB_LOCATION_AP==pbRecv->pbLocation){total=g_zPb_ApIndex[
(0x268+3341-0xf75)];IndexType=g_zPb_ApIndex;}for(;count<=total;count++){if((NULL
!=IndexType)&&(IndexType[count]==PBM_ERROR_NOT_FOUND)){break;}}if(count>total){
slog(PB_PRINT,SLOG_ERR,
"\x70\x62\x3a\x66\x69\x6e\x64\x5f\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3a\x63\x61\x6e\x20\x6e\x6f\x74\x20\x66\x69\x6e\x64\x20\x69\x6e\x64\x65\x78\x5f\x69\x64\x2c\x69\x6e\x64\x65\x78\x5f\x69\x64\x3d\x25\x64" "\n" "\x2e"
,count);return-(0x3bf+365-0x52b);}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x66\x69\x6e\x64\x5f\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x3a\x20\x69\x6e\x64\x65\x78\x5f\x69\x64\x3d\x25\x64\x2c\x20\x74\x6f\x74\x61\x6c\x20\x69\x73\x20\x25\x64" "\n"
,count,total);return count;}else{return pbRecv->pbIndex;}}BOOL atPb_GetASCII(
CHAR*pSrc,CHAR*pDst,SINT32 len){SINT32 j=(0xcaf+2910-0x180d);SINT32 i=
(0x13a6+1194-0x1850);CHAR buf[ZPB_TEXT_SIZE_BYTES]={(0x12df+3909-0x2224)};CHAR 
str[(0x1549+115-0x15b9)]={(0x78f+6595-0x2152)};SINT32 length=(0xe2c+5718-0x2482)
;length=atPb_String2Bytes(pSrc,buf,len);for(i=(0x7d5+5398-0x1ceb);i<length;i+=
(0xe2f+3641-0x1c66)){if(buf[i]!=(0x25c+6067-0x1a0f)||buf[i+(0xc1b+5756-0x2296)]>
(0x920+6972-0x23dd)){return FALSE;}pDst[j++]=buf[i+(0x8a9+2700-0x1334)];}return 
TRUE;}VOID atWeb_AddOrModOnePbUsim(T_zPb_WebContact*pWebPbContact,BOOL pbNewFlag
,sem_t semId){int atRes=(0xb8b+1876-0x12df);CHAR pbName[ZPB_TEXT_SIZE_BYTES+
(0x1709+1050-0x1b20)]={(0x6d1+5271-0x1b68)};CHAR buf_src[ZPB_TEXT_SIZE_BYTES+
(0xdb1+577-0xfef)]={(0x1c1+1851-0x8fc)};CHAR buf_dest[ZPB_TEXT_SIZE_BYTES+
(0x1332+1176-0x17c7)]={(0x128b+1097-0x16d4)};T_zPb_ScpbwParam scpbwParam={
(0x9cd+2079-0x11ec)};int len=(0x4eb+7529-0x2254);atPb_WebRecodeShow(
pWebPbContact);if(atPb_GetASCII(pWebPbContact->name,pbName,strlen(pWebPbContact
->name))){scpbwParam.coding=(0x170c+2896-0x225c);}else{len=atPb_String2Bytes(
pWebPbContact->name,buf_src,strlen(pWebPbContact->name));len=
atpb_CvtUcs2ToAlphaField(buf_src,len,buf_dest);atPb_Bytes2String(buf_dest,pbName
,len);scpbwParam.coding=(0xef0+2930-0x1a61);}scpbwParam.pbIndex=pWebPbContact->
pbIndex;strncpy(scpbwParam.name,pbName,sizeof(scpbwParam.name)-
(0x1d5+8061-0x2151));strncpy(scpbwParam.mobilNumber,pWebPbContact->mobilNumber,
sizeof(scpbwParam.mobilNumber)-(0x1429+1355-0x1973));strncpy(scpbwParam.
officeNumber,pWebPbContact->officeNumber,sizeof(scpbwParam.officeNumber)-
(0x16f7+727-0x19cd));strncpy(scpbwParam.homeNumber,pWebPbContact->homeNumber,
sizeof(scpbwParam.homeNumber)-(0x88b+2736-0x133a));strncpy(scpbwParam.email,
pWebPbContact->email,sizeof(scpbwParam.email)-(0x14ed+3216-0x217c));
ipc_send_message(MODULE_ID_PB,MODULE_ID_AT_CTL,MSG_CMD_ADD_MODIFY_PB_REQ,sizeof(
T_zPb_ScpbwParam),(unsigned char*)&scpbwParam,(0x2b1+7983-0x21e0));sem_wait(&
g_Pb_sem_id);if(g_PbOptRsp.result!=(0x753+579-0x995)){(VOID)sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);if(TRUE==pbNewFlag){printf(
"\x70\x62\x3a\x61\x64\x64\x20\x53\x63\x70\x62\x77\x20\x65\x72\x72\x6f\x72\x2e" "\n"
);}else{printf(
"\x70\x62\x3a\x6d\x6f\x64\x69\x66\x79\x20\x53\x63\x70\x62\x77\x20\x65\x72\x72\x6f\x72\x2e" "\n"
);}return;}printf(
"\x70\x62\x3a\x53\x65\x6e\x64\x20\x53\x63\x70\x62\x77\x20\x6f\x6b\x2e" "\n");if(
ZPB_DB_OK==atPb_WriteContactToPbmTable(pWebPbContact,pbNewFlag)){(VOID)
sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);printf(
"\x70\x62\x3a\x57\x72\x69\x74\x65\x43\x6f\x6e\x74\x61\x63\x74\x54\x6f\x50\x62\x6d\x54\x61\x62\x6c\x65\x20\x6f\x6b\x2e" "\n"
);}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);printf(
"\x70\x62\x3a\x57\x72\x69\x74\x65\x43\x6f\x6e\x74\x61\x63\x74\x54\x6f\x50\x62\x6d\x54\x61\x62\x6c\x65\x20\x65\x72\x72\x6f\x72\x2e" "\n"
);}}VOID atWeb_AddOnePb(T_zPb_WebContact*webPbContact,sem_t semId){SINT32 
idleIndex=(0xbe7+5377-0x20e7);T_zPb_Header pbHeader={(0x1850+2469-0x21f5)};BOOL 
pbNewFlag=FALSE;T_zPb_DelInfo delRecord={(0x1866+3486-0x2604)};CHAR ptFlag[
(0x3da+7874-0x2288)]={(0x1ff1+1503-0x25d0)};printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x2c\x20\x70\x62\x49\x64\x3d\x25\x64\x21" "\n"
,webPbContact->pbId);if(-(0x49b+11-0x4a5)==webPbContact->pbId){slog(PB_PRINT,
SLOG_DEBUG,
"\x70\x62\x3a\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x20\x6e\x65\x77\x2e" "\n"
);pbNewFlag=TRUE;idleIndex=atPb_FindIdleIndex(webPbContact,pbNewFlag);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x20\x69\x64\x6c\x65\x49\x6e\x64\x65\x78\x3d\x25\x64\x2e" "\n"
,idleIndex);if(idleIndex!=-(0x1a88+1826-0x21a9)){webPbContact->pbIndex=idleIndex
;}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);printf(
"\x5b\x50\x42\x5d\x20\x6d\x65\x6d\x6f\x72\x79\x20\x69\x73\x20\x66\x75\x6c\x6c\x2c\x20\x63\x61\x6e\x20\x6e\x6f\x74\x20\x61\x64\x64\x20\x72\x65\x63\x6f\x64\x65\x20\x61\x6e\x79\x20\x6d\x6f\x72\x65\x2e" "\n"
);return;}}else{printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x20\x6d\x6f\x64\x69\x66\x79\x2e" "\n"
);pbHeader.pbId=webPbContact->pbId;if(ZPB_DB_OK!=atPb_GetIndexLocationById(&
pbHeader)){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);return;}
webPbContact->pbIndex=pbHeader.pbIndex;webPbContact->pbLocation=pbHeader.
pbLocation;}printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x3a\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64" "\n"
,webPbContact->pbLocation);if(ZPB_LOCATION_AP==webPbContact->pbLocation){if(
ZPB_DB_OK!=atPb_WriteContactToPbmTable(webPbContact,pbNewFlag)){(VOID)sc_cfg_set
(ZPB_NV_WRITE_FLAG,ZPB_NEW_ERROR);}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_OPERATE_SUC);}}else if(ZPB_LOCATION_USIM==webPbContact->pbLocation){
atWeb_AddOrModOnePbUsim(webPbContact,pbNewFlag,semId);}else{printf(
"\x5b\x50\x42\x5d\x20\x77\x72\x69\x74\x65\x5f\x70\x62\x6d\x5f\x72\x65\x63\x6f\x72\x64\x3a\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x69\x73\x20\x4e\x55\x4c\x4c" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_LOCATION_IS_NULL);}printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x41\x64\x64\x4f\x6e\x65\x50\x62\x20\x64\x65\x6c\x5f\x69\x64\x3d\x25\x64\x2e" "\n"
,webPbContact->del_id);sc_cfg_get(ZPB_NV_WRITE_FLAG,ptFlag,sizeof(ptFlag));if(
(0x8ca+3065-0x14c3)==strcmp("\x30",ptFlag)&&-(0x10c8+3617-0x1ee8)!=webPbContact
->del_id){delRecord.delId[(0x8bb+7106-0x247d)]=webPbContact->del_id;
atWeb_DelOnepb(&delRecord,semId);}}VOID atWeb_DelOnepb(T_zPb_DelInfo*delRecord,
sem_t semId){CHAR errCode[ZSVR_AT_RES_CODE_LEN]={(0xbfd+346-0xd57)};int atRes=
(0x7b2+441-0x96b);SINT32 delTime=(0x2c5+9087-0x2644);atPb_GetLocationIndexForDel
(delRecord,delTime);printf(
"\x5b\x50\x42\x5d\x20\x64\x65\x6c\x5f\x61\x5f\x70\x62\x6d\x5f\x72\x65\x63\x6f\x72\x64\x2d\x2d\x64\x65\x6c\x20\x69\x6e\x64\x65\x78\x3d\x25\x64\x2c\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64" "\n"
,delRecord->delIndex[(0x1a3+1623-0x7fa)],delRecord->delLocation);if(
ZPB_LOCATION_AP==delRecord->delLocation){if(ZPB_DB_OK==atPb_DelARecFromPbmTable(
delRecord,delTime)){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4f\x6e\x65\x70\x62\x2d\x2d\x64\x65\x6c\x20\x41\x50\x20\x73\x75\x63\x63\x65\x73\x73" "\n"
);return;}slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x64\x65\x6c\x5f\x61\x5f\x70\x62\x6d\x5f\x72\x65\x63\x6f\x72\x64\x3a\x72\x65\x6d\x6f\x76\x65\x20\x72\x65\x63\x20\x66\x72\x6f\x6d\x20\x70\x62\x6d\x20\x74\x61\x62\x6c\x65\x20\x66\x61\x69\x6c\x65\x64" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}else if(ZPB_LOCATION_USIM==
delRecord->delLocation){CHAR pbMax[(0x14b1+492-0x166b)]={(0x7c0+3836-0x16bc)};
sc_cfg_get(ZPB_NV_USIMINDEXMAX,pbMax,sizeof(pbMax));if((delRecord->delIndex[
(0x2fc+6119-0x1ae3)]>=(0xb6f+4234-0x1bf8))&&(delRecord->delIndex[
(0x573+4262-0x1619)]<=atoi(pbMax))){ipc_send_message(MODULE_ID_PB,
MODULE_ID_AT_CTL,MSG_CMD_DELETE_PB_REQ,sizeof(int),(unsigned char*)&delRecord->
delIndex[(0x5db+665-0x874)],(0x1a1f+1776-0x210f));sem_wait(&g_Pb_sem_id);if(
g_PbOptRsp.result!=(0x504+2949-0x1088)){sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_DEL_ERROR);}sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x6e\x6f");}if(ZPB_DB_OK==
atPb_DelARecFromPbmTable(delRecord,delTime)){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_OPERATE_SUC);printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4f\x6e\x65\x70\x62\x2d\x2d\x64\x65\x6c\x20\x53\x49\x4d\x20\x73\x75\x63\x63\x65\x73\x73" "\n"
);return;}(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}else{slog(PB_PRINT,
SLOG_DEBUG,
"\x70\x62\x3a\x64\x65\x6c\x5f\x61\x5f\x70\x62\x6d\x5f\x72\x65\x63\x6f\x72\x64\x3a\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x69\x73\x20\x4e\x55\x4c\x4c" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_LOCATION_IS_NULL);}}VOID 
atWeb_DelMultPb(T_zPb_DelInfo*delRecord,sem_t semId){CHAR errCode[
ZSVR_AT_RES_CODE_LEN]={(0xbc8+1445-0x116d)};int atRes=(0x19b+8222-0x21b9);SINT32
 delCount=(0xd14+3463-0x1a9b);(VOID)sc_cfg_set(NV_PHO_DEL,"");memset(&
g_zPb_DelStatusUsim,(0x989+4256-0x1a29),sizeof(T_zPb_DelStatusMultiOrAll));
g_zPb_DelStatusUsim.dealFlag=ZPB_DEL_MULTI_RECORD_USIM;for(;delCount<delRecord->
delTotal;delCount++){slog(PB_PRINT,SLOG_DEBUG,
"\x70\x62\x33\x3a\x64\x65\x6c\x49\x64\x5b\x25\x64\x5d\x3d\x25\x64\x2c\x64\x65\x6c\x5f\x70\x62\x6d\x5f\x69\x6e\x64\x65\x78\x5b\x25\x64\x5d\x3d\x25\x64" "\n"
,delCount,delRecord->delId[delCount],delCount,delRecord->delIndex[delCount]);
atPb_GetLocationIndexForDel(delRecord,delCount);if(ZPB_LOCATION_AP==delRecord->
delLocation){if(ZPB_DB_OK!=atPb_DelARecFromPbmTable(delRecord,delCount)){slog(
PB_PRINT,SLOG_DEBUG,
"\x70\x62\x3a\x70\x62\x6d\x3a\x72\x65\x6d\x6f\x76\x65\x20\x74\x68\x65\x20\x69\x28\x25\x64\x29\x20\x72\x65\x63\x20\x66\x72\x6f\x6d\x20\x70\x62\x6d\x20\x74\x61\x62\x6c\x65\x20\x66\x61\x69\x6c\x65\x64" "\n"
,delCount);g_zPb_DelStatusUsim.dealFailNum++;continue;}g_zPb_DelStatusUsim.
dealSuccNum++;}else if(ZPB_LOCATION_USIM==delRecord->delLocation){CHAR pbMax[
(0x410+5519-0x196d)]={(0xdf+8698-0x22d9)};sc_cfg_get(ZPB_NV_USIMINDEXMAX,pbMax,
sizeof(pbMax));if((delRecord->delIndex[delCount]>=(0x236c+424-0x2513))&&(
delRecord->delIndex[delCount]<=atoi(pbMax))){printf(
"\x70\x62\x39\x3a\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x4d\x75\x6c\x74\x50\x62\x3a\x72\x65\x6d\x6f\x76\x65\x20\x74\x68\x65\x20\x69\x6e\x64\x65\x78\x28\x25\x64\x29\x20\x72\x65\x63\x20\x66\x72\x6f\x6d\x20\x70\x62\x6d\x20\x74\x61\x62\x6c\x65" "\n"
,delRecord->delIndex[delCount]);ipc_send_message(MODULE_ID_PB,MODULE_ID_AT_CTL,
MSG_CMD_DELETE_PB_REQ,sizeof(int),(unsigned char*)&delRecord->delIndex[delCount]
,(0x38+6082-0x17fa));sem_wait(&g_Pb_sem_id);if(g_PbOptRsp.result!=
(0x987+2745-0x143f)){g_zPb_DelStatusUsim.dealFailNum++;sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}else{g_zPb_DelStatusUsim.dealSuccNum++;
sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x6e\x6f");}}else{continue;}(VOID)
atPb_DelSimRecFromPbTable(delRecord->delIndex[delCount]);}else{slog(PB_PRINT,
SLOG_DEBUG,
"\x70\x62\x3a\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x69\x73\x20\x4e\x55\x4c\x4c" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_LOCATION_IS_NULL);}}
atPb_SetDelStatusMultOrAll();}T_zPb_DbResult atPb_DelRecByGroup(VOID){
T_zPb_DbResult result=ZPB_DB_OK;T_zPb_ApIndex index={(0x1e19+1929-0x25a2)};
SINT32 i=(0x1907+523-0x1b11);result=atPb_DelRecFromPbmTableByGroup(&index);if(
ZPB_DB_OK!=result){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);return 
result;}(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);for(i=
(0x87+9622-0x261c);i<=ZPB_AP_MAX_RECORD;i++){if((0x30a+353-0x46b)!=index.apIndex
[i]){g_zPb_ApIndex[(index.apIndex[i])]=PBM_ERROR_NOT_FOUND;}}return result;}VOID
 atPb_DelAllRecsSimDb(VOID){CHAR sql[ZPB_MAX_BYTES_DB]={(0x1ce0+2175-0x255f)};
snprintf(sql,sizeof(sql)-(0x14e9+2362-0x1e22),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_USIM);if(ZPB_DB_OK==atPb_ExecDbSql(sql,NULL,NULL)
){atPb_ClearSimPbmIndexArray();if(ZPB_DB_OK!=atPb_ExecDbSql(
ZTE_DROP_PBM_SIM_CAPABILITY_SQL,NULL,NULL)){printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x50\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x52\x65\x63\x73\x53\x69\x6d\x44\x62\x20\x65\x78\x65\x63\x44\x62\x20\x32\x32\x32\x32\x20\x66\x61\x69\x6c" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);return;}(VOID)sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_DEL_ERROR);}}VOID atPb_DelAllRecsSim(T_zPb_DelInfo*pdelRecord,sem_t semId){
CHAR errCode[ZSVR_AT_RES_CODE_LEN]={(0xb6d+7000-0x26c5)};int atRes=
(0x1b+541-0x238);UINT32 i=(0xe9c+1711-0x154b);CHAR sql[ZPB_MAX_BYTES_DB]={
(0x688+7346-0x233a)};UINT32 count=(0xb61+3315-0x1854);if(pdelRecord!=NULL){
memset(&g_zPb_DelStatusUsim,(0x1184+2905-0x1cdd),sizeof(
T_zPb_DelStatusMultiOrAll));g_zPb_DelStatusUsim.dealFlag=ZPB_DEL_ALL_RECORD_USIM
;snprintf(sql,sizeof(sql)-(0x427+5244-0x18a2),
"\x73\x65\x6c\x65\x63\x74\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_USIM);(VOID)atPb_ExecDbSql(sql,
atPb_DbCountTableLineCb,&count);if((0xa6+2478-0xa54)<count){CHAR pbMin[
(0x360+144-0x3be)]={(0x55c+5545-0x1b05)};CHAR pbMax[(0x66c+467-0x80d)]={
(0xab+9418-0x2575)};UINT32 i_pbMin=(0x162a+2386-0x1f7c);UINT32 i_pbMax=
(0xd6c+5762-0x23ee);sc_cfg_get(ZPB_NV_USIMINDEXMIN,pbMin,sizeof(pbMin));
sc_cfg_get(ZPB_NV_USIMINDEXMAX,pbMax,sizeof(pbMax));i_pbMin=atoi(pbMin);i_pbMax=
atoi(pbMax);if(i_pbMin>ZPB_SIM_MAX_RECORD||i_pbMax>ZPB_SIM_MAX_RECORD){printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x50\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x52\x65\x63\x73\x53\x69\x6d\x20\x73\x69\x6d\x20\x69\x6e\x64\x65\x78\x20\x74\x6f\x6f\x20\x6c\x61\x72\x67\x65" "\n"
);return;}for(i=i_pbMin;i<=i_pbMax;i++){ipc_send_message(MODULE_ID_PB,
MODULE_ID_AT_CTL,MSG_CMD_DELETE_PB_REQ,sizeof(int),(unsigned char*)&i,
(0xb70+7061-0x2705));sem_wait(&g_Pb_sem_id);if(g_PbOptRsp.result!=
(0x4b3+3853-0x13bf)){g_zPb_DelStatusUsim.dealFailNum++;sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}else{g_zPb_DelStatusUsim.dealSuccNum++;
sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x6e\x6f");}}snprintf(sql,sizeof(sql)-
(0x128+7721-0x1f50),
"\x64\x65\x6c\x65\x74\x65\x20\x66\x72\x6f\x6d\x20\x25\x73\x20\x77\x68\x65\x72\x65\x20\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64"
,ZPB_DB_PBM_TABLE,ZPB_LOCATION_USIM);if(ZPB_DB_OK==atPb_ExecDbSql(sql,NULL,NULL)
){(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);atPb_ClearSimPbmIndexArray
();}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_DEL_ERROR);}
atPb_SetDelStatusMultOrAll();(VOID)sc_cfg_set(ZPB_NV_USIMMEMORYFULL,"\x6e\x6f");
}}}VOID atWeb_DelAllpb(T_zPb_DelInfo*delRecord,sem_t semId){printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x70\x62\x20\x64\x65\x6c\x4c\x6f\x63\x61\x74\x69\x6f\x6e\x3d\x25\x64" "\n"
,delRecord->delLocation);if(ZPB_LOCATION_AP==delRecord->delLocation){if(
ZPB_DB_OK==atPb_DelAllRecsFromPbmTable(delRecord)){(VOID)sc_cfg_set(
ZPB_NV_WRITE_FLAG,ZPB_OPERATE_SUC);}else{(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,
ZPB_DEL_ERROR);}}else if(ZPB_LOCATION_USIM==delRecord->delLocation){
atPb_DelAllRecsSim(delRecord,semId);}else if(ZPB_LOCATION_ALL==delRecord->
delLocation){delRecord->delLocation=ZPB_LOCATION_AP;(VOID)
atPb_DelAllRecsFromPbmTable(delRecord);delRecord->delLocation=ZPB_LOCATION_USIM;
atPb_DelAllRecsSim(delRecord,semId);}else if(ZPB_LOCATION_GROUP==delRecord->
delLocation){(VOID)atPb_DelRecByGroup();}else{printf(
"\x5b\x50\x42\x5d\x20\x61\x74\x57\x65\x62\x5f\x44\x65\x6c\x41\x6c\x6c\x70\x62\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x69\x73\x20\x4e\x55\x4c\x4c" "\n"
);(VOID)sc_cfg_set(ZPB_NV_WRITE_FLAG,ZPB_LOCATION_IS_NULL);return;}}
