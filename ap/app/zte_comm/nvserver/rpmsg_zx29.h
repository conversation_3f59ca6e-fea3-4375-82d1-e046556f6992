/*******************************************************************************
* Copyright (C) 2013, ZTE Corporation.
*
* File Name: rpmsg_zx29.h
* File Mark:
* Description:  
* Others:
* Version:       V0.1
* Author:        ShiDeYou
* Date:          2013-09-23
* History 1:
*     Date:
*     Version:
*     Author:
*     Modification:
*******************************************************************************/

/*******************************************************************************
*                                  Include files                               *
*******************************************************************************/

/*******************************************************************************
*                                  Types                                       *
*******************************************************************************/
#ifndef RPMSG_ZX29_H
#define RPMSG_ZX29_H

#include <linux/ioctl.h>

#define RPMSG_IOC_MAGIC     'R'

/*ioctl cmd usd by device*/
#define RPMSG_CREATE_CHANNEL          _IOW(RPMSG_IOC_MAGIC, 1, char *)
#define RPMSG_GET_DATASIZE       _IOWR(RPMSG_IOC_MAGIC, 2, char *)
#define RPMSG_SET_INT            _IOW(RPMSG_IOC_MAGIC, 3, char *)
#define RPMSG_SET_INT_FLAG        _IOW(RPMSG_IOC_MAGIC, 4, char *)
#define RPMSG_CLEAR_INT_FLAG      _IOW(RPMSG_IOC_MAGIC, 5, char *)
#define RPMSG_SET_POLL_FLAG       _IOW(RPMSG_IOC_MAGIC, 6, char *)
#define RPMSG_CLEAR_POLL_FLAG     _IOW(RPMSG_IOC_MAGIC, 7, char *)

#define AT_DEV   armps_rpmsgch9
#define FOTA_DEV    "/dev/armps_rpmsgch12"

#endif
