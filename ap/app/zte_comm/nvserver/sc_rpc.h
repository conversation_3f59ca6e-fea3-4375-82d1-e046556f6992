/*******************************************************************************
* 版权所有 (C)2023, 中兴通讯股份有限公司。
* 
* 文件名称:     sc_rpc.h
* 文件标识:     sc_rpc.h
* 内容摘要:     rpc头文件
* 使用方法:     #include <sc_rpc.h>
* 
* 修改日期        版本号      修改标记        修改人          修改内容
* ------------------------------------------------------------------------------
* 2023/03/23       V1.0        Create          周国坡          创建
* 
*******************************************************************************/
#ifndef _SC_RPC_H
#define _SC_RPC_H

enum 
{
    RPC_MSG_TYPE_REQUEST = 0xAA,
    RPC_MSG_TYPE_REPLY = 0xBB,
    RPC_MSG_TYPE_READY = 0x55
};

enum
{
    RPC_FUNC_ID_NV = 10
};

typedef struct 
{
    unsigned int msg_type;
    unsigned int func_id;
    unsigned int data_len;
    char       data[4096];
} T_sc_rpc_header;

int sc_rpc_open(const char *rpmsg_dev);

int sc_rpc_clear(int fd);

int sc_rpc_send(int fd, T_sc_rpc_header *data, unsigned int flag);

int sc_rpc_recv(int fd, T_sc_rpc_header *data, unsigned int timeout);

int sc_rpc_close(int fd);

#endif
