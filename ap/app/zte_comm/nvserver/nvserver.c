
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>
#include <string.h>
#include <sys/file.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include "nvserver.h"
#include "nv_typedef.h"
#include <message.h>
#ifdef FOTA_AB
#include "zxic_fota_ab_upgrade.h"
#endif
#ifdef __cplusplus
extern"C"{
#endif
static void nvConfig();static void nvInit();static int nvDirInit();static void 
analyMsg(T_NV_MSG_INFO*msgrecv,T_NV_MSG_RESULT*msgsnd);static bool checkNvFs(
char*file);static bool isCfgConfiged(char*configFile);static bool isNvConfiged(
char*configFile);static uint getSum(const char*s,int len);static int 
loadFactroyParam(T_NV_NODE*list);static int restoreNvFs(char*dstFile,char*
srcFile);static int loadNvFs(char*file);static int addConfigFile(char*nvFile,
char*configFile);static int saveNvFs(char*nvName,char*nvFile);static int nvset(
char*file,const char*key,const char*value,int saveFlag);static int nvget(char*
file,char*key,char*value);static int nvunset(char*file,char*key);static int 
nvclear(char*file);static int nvreset(char*file);static int nvcommit(char*file);
T_NV_NODE*nv_list;int nvserver_main(int argc,char*argv[]){int msgId=
(0x1c0b+2299-0x2506);T_NV_MSG_INFO rcvBuf;T_NV_MSG_RESULT sndBuf;struct msqid_ds
 msgInfo;prctl(PR_SET_NAME,"\x6e\x76\x73\x65\x72\x76\x65\x72",
(0x1be8+679-0x1e8f),(0x159f+1921-0x1d20),(0x105b+1782-0x1751));memset(&rcvBuf,
(0x12b8+1997-0x1a85),sizeof(rcvBuf));memset(&sndBuf,(0xbdf+4172-0x1c2b),sizeof(
sndBuf));memset(&msgInfo,(0x1057+5497-0x25d0),sizeof(msgInfo));msgId=msgget(
MODULE_ID_NV,IPC_CREAT|(0x159f+1450-0x19c9));if(-(0xb17+6059-0x22c1)==msgId){
printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x6d\x73\x67\x67\x65\x74\x20\x6d\x73\x67\x49\x64\x20\x66\x61\x69\x6c\x2c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,errno);return-(0x76b+1504-0xd4a);}if(-(0x158b+2552-0x1f82)!=msgctl(msgId,
IPC_STAT,&msgInfo)){msgInfo.msg_qbytes=262144;if(-(0x5f3+4665-0x182b)==msgctl(
msgId,IPC_SET,&msgInfo))printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x6d\x73\x67\x63\x74\x6c\x20\x6d\x73\x67\x49\x64\x20\x66\x61\x69\x6c\x2c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,errno);}if(nvDirInit()!=(0x441+5414-0x1967)){printf(
"\x6e\x76\x44\x69\x72\x49\x6e\x69\x74\x20\x66\x61\x69\x6c\x65\x21" "\n");return-
(0x373+5093-0x1757);}nvConfig();nvInit();while((0xb92+432-0xd41)){if(-
(0x850+2432-0x11cf)==msgrcv(msgId,&rcvBuf,sizeof(T_NV_MSG_INFO)-sizeof(long),
MSG_TYPE_NV,(0x1f1+5147-0x160c))){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6d\x73\x67\x72\x63\x76\x20\x66\x61\x69\x6c\x2c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x21" "\n"
,errno);continue;}analyMsg(&rcvBuf,&sndBuf);if(-(0x91d+2052-0x1120)==msgsnd(
msgId,&sndBuf,sizeof(T_NV_MSG_RESULT)-sizeof(long),(0x36a+6723-0x1dad))){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6d\x73\x67\x73\x6e\x64\x20\x66\x61\x69\x6c\x2c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x21" "\n"
,errno);continue;}}return((0x4da+3084-0x10e6));}static void configdir(char*dir){
DIR*dp;int ret;struct dirent*entry;struct stat statbuf;if((dp=opendir(dir))==
NULL){fprintf(stderr,
"\x63\x61\x6e\x6e\x6f\x74\x20\x6f\x70\x65\x6e\x20\x64\x69\x72\x65\x63\x74\x6f\x72\x79\x3a\x20\x25\x73" "\n"
,dir);return;}chdir(dir);while((entry=readdir(dp))!=NULL){ret=lstat(entry->
d_name,&statbuf);if(ret<(0x158b+2323-0x1e9e)){fprintf(stderr,
"\x6c\x73\x74\x61\x74\x20\x65\x72\x72\x6f\x72\x3a\x20\x25\x73" "\n",strerror(
errno));chdir("\x2e\x2e");closedir(dp);return;}if(!S_ISDIR(statbuf.st_mode)){if(
strcmp("\x2e",entry->d_name)==(0x1094+3367-0x1dbb)||strcmp("\x2e\x2e",entry->
d_name)==(0xfeb+5004-0x2377))continue;if(!isNvConfiged(entry->d_name)){if(
addConfigFile(entry->d_name,NULL)!=RESULT_SUCCESS)printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x63\x6f\x6e\x66\x69\x67\x20\x25\x73\x20\x65\x72\x72\x6f\x72\x21" "\n"
,entry->d_name);}}}chdir("\x2e\x2e");closedir(dp);}static void nvConfig(){char*
val=NULL;FILE*fp=NULL;char buf[NV_MAX_CONFIG_LEN]={(0xbec+101-0xc51)};fp=fopen(
NV_CONFIG_FILE,"\x72\x6f");if(!fp){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x6f\x70\x65\x6e\x20\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x21" "\n"
,NV_CONFIG_FILE,errno);return;}while(fgets(buf,NV_MAX_CONFIG_LEN,fp)){if(buf[
(0x1b7+4787-0x146a)]=='\n'||buf[(0x19e7+236-0x1ad3)]==
((char)(0x1fa+4588-0x13c3)))continue;val=strchr(buf,((char)(0x1529+2517-0x1ec1))
);if(!val){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x6f\x72\x6d\x61\x74\x20\x65\x72\x72\x6f\x72\x3a\x20\x73\x74\x72\x20\x3d\x20\x25\x73\x21" "\n"
,NV_CONFIG_FILE,buf);continue;}buf[strlen(buf)-(0xaa9+4722-0x1d1a)]='\0';*val++=
'\0';if(!isCfgConfiged(buf)){if(addConfigFile(val,buf)!=RESULT_SUCCESS)printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x63\x6f\x6e\x66\x69\x67\x20\x25\x73\x20\x65\x72\x72\x6f\x72\x21" "\n"
,buf);}}fclose(fp);configdir(NV_FS_MAIN_PATH);}static int nvDirInit(){if(access(
NV_FS_PATH,F_OK)!=(0x1362+1517-0x194f)){if(mkdir(NV_FS_PATH,(0x1dbb+415-0x1d6d))
!=(0x109d+4086-0x2093)){printf(
"\x6e\x65\x72\x76\x65\x72\x20\x6d\x6b\x64\x69\x72\x20\x25\x73\x20\x66\x61\x6c\x69\x2c\x65\x72\x72\x6e\x6f\x3d\x25\x64" "\n"
,NV_FS_PATH,errno);return-(0xff+7707-0x1f19);}if(mkdir(NV_FS_MAIN_PATH,
(0x274+2206-0x925))!=(0x1b16+2133-0x236b)){printf(
"\x6e\x65\x72\x76\x65\x72\x20\x6d\x6b\x64\x69\x72\x20\x25\x73\x20\x66\x61\x6c\x69\x2c\x65\x72\x72\x6e\x6f\x3d\x25\x64" "\n"
,NV_FS_MAIN_PATH,errno);return-(0x1d+3250-0xcce);}if(mkdir(NV_FS_BACKUP_PATH,
(0x43f+992-0x632))!=(0x117+277-0x22c)){printf(
"\x6e\x65\x72\x76\x65\x72\x20\x6d\x6b\x64\x69\x72\x20\x25\x73\x20\x66\x61\x6c\x69\x2c\x65\x72\x72\x6e\x6f\x3d\x25\x64" "\n"
,NV_FS_BACKUP_PATH,errno);return-(0x548+3528-0x130f);}}else{if(access(
NV_FS_MAIN_PATH,F_OK)!=(0xcfd+2904-0x1855)){if(mkdir(NV_FS_MAIN_PATH,
(0x24d2+970-0x26af))!=(0x1913+1492-0x1ee7)){printf(
"\x6e\x65\x72\x76\x65\x72\x20\x6d\x6b\x64\x69\x72\x20\x25\x73\x20\x66\x61\x6c\x69\x2c\x65\x72\x72\x6e\x6f\x3d\x25\x64" "\n"
,NV_FS_MAIN_PATH,errno);return-(0xd51+5094-0x2136);}}if(access(NV_FS_BACKUP_PATH
,F_OK)!=(0x51f+4527-0x16ce)){if(mkdir(NV_FS_BACKUP_PATH,(0x8d8+7060-0x227f))!=
(0xbcb+1234-0x109d)){printf(
"\x6e\x65\x72\x76\x65\x72\x20\x6d\x6b\x64\x69\x72\x20\x25\x73\x20\x66\x61\x6c\x69\x2c\x65\x72\x72\x6e\x6f\x3d\x25\x64" "\n"
,NV_FS_BACKUP_PATH,errno);return-(0x39b+2672-0xe0a);}}}return
(0x10d9+1199-0x1588);}static void nvInit(){T_NV_NODE*list=NULL;char nvMainFile[
NV_PATH_LEN]={(0xa23+6304-0x22c3)};char nvBackupFile[NV_PATH_LEN]={
(0x12f7+780-0x1603)};for(list=nv_list;list;list=list->next){snprintf(nvMainFile,
NV_PATH_LEN,"\x25\x73\x2f\x25\x73",NV_FS_MAIN_PATH,list->nvFile);snprintf(
nvBackupFile,NV_PATH_LEN,"\x25\x73\x2f\x25\x73",NV_FS_BACKUP_PATH,list->nvFile);
if(checkNvFs(nvMainFile)){if(!checkNvFs(nvBackupFile))restoreNvFs(nvBackupFile,
nvMainFile);}else if(checkNvFs(nvBackupFile)){restoreNvFs(nvMainFile,
nvBackupFile);}else{loadFactroyParam(list);nvcommit(list->nvFile);continue;}
loadNvFs(list->nvFile);if(!strcmp(list->nvFile,NV_CFG)&&get_update_status()==
(0xed8+3311-0x1bc5)){reloadFactroyParam(list);delete_not_needed(list);nvcommit(
list->nvFile);
#ifdef FOTA_AB
dual_AB_set_fota_status_for_nv((0xd87+4061-0x1d64));
#endif
}}}uint hash(const char*s){uint hash=(0x1281+888-0x15f9);while(*s){hash=
NV_HASH_MUL*hash+*s++;}return hash;}static int loadFactroyParam(T_NV_NODE*list){
char*val=NULL;FILE*fp=NULL;T_NV_CONFIG*config=NULL;char buf[NV_MAX_ITEM_LEN]={
(0x1f8f+1693-0x262c)};for(config=list->fileList;config;config=config->next){fp=
fopen(config->configFile,"\x72\x6f");if(!fp){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x6f\x70\x65\x6e\x20\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x21" "\n"
,config->configFile,errno);return RESULT_FILE_OPEN_FAIL;}while(fgets(buf,
NV_MAX_ITEM_LEN,fp)){if(buf[(0x984+294-0xaaa)]=='\n'||buf[(0x1407+1599-0x1a46)]
==((char)(0x30b+2779-0xdc3)))continue;val=strchr(buf,((char)(0xe8+4284-0x1167)))
;if(!val){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x6f\x72\x6d\x61\x74\x20\x65\x72\x72\x6f\x72\x3a\x73\x74\x72\x69\x6e\x67\x20\x3d\x20\x25\x73" "\n"
,config->configFile,buf);continue;}if(buf[strlen(buf)-(0x7ec+728-0xac3)]=='\n')
buf[strlen(buf)-(0x1c54+1630-0x22b1)]='\0';*val++='\0';nvset(list->nvFile,buf,
val,(0x13fb+4187-0x2455));}printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6c\x6f\x61\x64\x46\x61\x63\x74\x72\x6f\x79\x50\x61\x72\x61\x6d\x20\x25\x73\x21" "\n"
,config->configFile);fclose(fp);}return RESULT_SUCCESS;}static bool checkNvFs(
char*file){int len=(0x1076+234-0x1160);int cnt=(0x120a+345-0x1363);FILE*fp=NULL;
char*buf=NULL;struct stat statbuff={(0x143+637-0x3c0)};if(stat(file,&statbuff)<
(0x158b+3384-0x22c3))return false;len=statbuff.st_size;if(len<NV_CHECK_SIZE)
return false;fp=fopen(file,"\x72\x6f");if(!fp)return false;buf=(char*)malloc(len
);if(!buf){fclose(fp);return false;}cnt=(0x195+7835-0x2030);while(cnt<len){cnt=
cnt+fread(buf+cnt,(0x18a3+2175-0x2121),len-cnt,fp);if(ferror(fp)){clearerr(fp);
free(buf);fclose(fp);return false;}}if(len!=cnt){free(buf);fclose(fp);return 
false;}if(getSum(buf,len-NV_CHECK_SIZE)+NV_FILE_FLAG!=*(uint*)(buf+len-
NV_CHECK_SIZE)){free(buf);fclose(fp);return false;}free(buf);fclose(fp);return 
true;}static int copyfile(const char*from,const char*to){int fd_to;int fd_from;
char buf[(0x2164+5410-0x2686)];ssize_t nread;int ret=-(0x4c6+4523-0x1670);
fd_from=open(from,O_RDONLY);if(fd_from<(0x822+257-0x923))return-
(0x170a+1111-0x1b5f);fd_to=open(to,O_RDWR|O_CREAT|O_TRUNC|O_SYNC,
(0xf5b+943-0x116a));if(fd_to<(0x24eb+522-0x26f5)){ret=-(0x4fd+3295-0x11d9);goto 
out_error;}while((0xe3a+3322-0x1b33)){char*out_ptr;ssize_t nwritten;nread=read(
fd_from,buf,sizeof(buf));if(nread==(0xfc5+5732-0x2629)){break;}else{if(nread<
(0x1fe3+548-0x2207)){if(errno==EINTR||errno==EAGAIN){continue;}else{ret=-
(0x1d9f+2273-0x267c);goto out_error;}}}out_ptr=buf;do{nwritten=write(fd_to,
out_ptr,nread);if(nwritten>(0x862+7186-0x2474)){nread-=nwritten;out_ptr+=
nwritten;}else{if(nwritten<(0xb83+6596-0x2547)){if(errno==EINTR||errno==EAGAIN){
continue;}else{ret=-(0x1ff4+557-0x221c);goto out_error;}}}}while(nread>
(0xd37+213-0xe0c));}ret=fsync(fd_to);if(ret<(0x35d+7181-0x1f6a)){printf(
"\x53\x79\x6e\x63\x20\x46\x61\x69\x6c\x65\x64\x3a\x25\x73\x2c\x20\x66\x69\x6c\x65\x20\x70\x61\x74\x68\x3a\x25\x73"
,strerror(errno),to);goto out_error;}if(close(fd_to)<(0x392+4542-0x1550)){fd_to=
-(0x1404+1692-0x1a9f);ret=-(0x1e35+1581-0x245c);goto out_error;}close(fd_from);
return(0x17b3+900-0x1b37);out_error:printf(
"\x63\x6f\x70\x79\x66\x69\x6c\x65\x20\x25\x73\x20\x74\x6f\x20\x25\x73\x20\x65\x72\x72\x6f\x72\x3a\x25\x64" "\n"
,from,to,ret);close(fd_from);if(fd_to>=(0x1015+4283-0x20d0))close(fd_to);return 
ret;}static int restoreNvFs(char*dstFile,char*srcFile){if(copyfile(srcFile,
dstFile)!=(0x1681+3950-0x25ef))return RESULT_FAIL;return RESULT_SUCCESS;}static 
int loadNvFs(char*file){int len=(0x507+7355-0x21c2);int cnt=(0x412+493-0x5ff);
FILE*fp=NULL;char*buf=NULL;char*name=NULL;char*value=NULL;char*eq=NULL;struct 
stat statbuff={(0xe11+2627-0x1854)};char nvFile[NV_PATH_LEN]={
(0x1d0+5450-0x171a)};sprintf(nvFile,"\x25\x73\x2f\x25\x73",NV_FS_MAIN_PATH,file)
;if(stat(nvFile,&statbuff)<(0x954+5879-0x204b))return RESULT_FAIL;len=statbuff.
st_size;if(NV_CHECK_SIZE>len)return RESULT_FAIL;fp=fopen(nvFile,"\x72\x6f");if(!
fp)return RESULT_FILE_OPEN_FAIL;len=len-NV_CHECK_SIZE;buf=(char*)malloc(len+
(0x6e6+1898-0xe4f));if(!buf){fclose(fp);return RESULT_MALLOC_FAIL;}memset(buf,
(0x1357+868-0x16bb),len+(0xa32+4377-0x1b4a));cnt=(0x4b1+5084-0x188d);while(cnt<
len){cnt=cnt+fread(buf+cnt,(0x121c+3462-0x1fa1),len-cnt,fp);if(ferror(fp)){
clearerr(fp);fclose(fp);free(buf);return RESULT_FILE_READ_FAIL;}}if(cnt!=len){
fclose(fp);free(buf);return RESULT_FILE_READ_FAIL;}buf[len]='\0';name=buf;while(
*name){if(!(eq=strchr(name,((char)(0x52b+4804-0x17b2))))){break;}*eq='\0';value=
eq+(0xdf0+5928-0x2517);nvset(file,name,value,(0x450+5367-0x1946));*eq=
((char)(0x469+5913-0x1b45));name=value+strlen(value)+(0xf89+3645-0x1dc5);}free(
buf);fclose(fp);return RESULT_SUCCESS;}static void analyMsg(T_NV_MSG_INFO*
msgrecv,T_NV_MSG_RESULT*msgsnd){switch(msgrecv->nvType){case MSG_GET:msgsnd->
result=nvget(msgrecv->file,msgrecv->key,msgsnd->value);break;case MSG_SET:msgsnd
->result=nvset(msgrecv->file,msgrecv->key,msgrecv->value,msgrecv->saveflag);
break;case MSG_UNSET:msgsnd->result=nvunset(msgrecv->file,msgrecv->key);break;
case MSG_CLEAR:msgsnd->result=nvclear(msgrecv->file);break;case MSG_RESET:msgsnd
->result=nvreset(msgrecv->file);break;case MSG_SHOW:msgsnd->result=saveNvFs(
msgrecv->file,NV_FS_SHOW);;break;case MSG_COMMIT:msgsnd->result=nvcommit(msgrecv
->file);break;default:break;}msgsnd->msgType=msgrecv->pid;msgsnd->msgIndex=
msgrecv->msgIndex;}static int addConfigFile(char*nvFile,char*configFile){
T_NV_NODE*list=NULL;T_NV_NODE*newList=NULL;T_NV_CONFIG*newConfig=NULL;if(!nvFile
){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x70\x61\x72\x61\x6d\x20\x69\x6c\x6c\x65\x67\x61\x6c\x21" "\n"
);return RESULT_INVAL;}if(configFile){newConfig=(T_NV_CONFIG*)malloc(sizeof(
T_NV_CONFIG));if(!newConfig)return RESULT_MALLOC_FAIL;strncpy(newConfig->
configFile,configFile,NV_PATH_LEN-(0x40d+6714-0x1e46));newConfig->configFile[
NV_PATH_LEN-(0x1352+4156-0x238d)]='\0';newConfig->next=NULL;}for(list=nv_list;
list;list=list->next){if(strcmp(list->nvFile,nvFile)==(0x842+3309-0x152f))break;
}if(!list){newList=(T_NV_NODE*)malloc(sizeof(T_NV_NODE));if(!newList){if(
newConfig)free(newConfig);return RESULT_MALLOC_FAIL;}newList->next=NULL;strncpy(
newList->nvFile,nvFile,NV_PATH_LEN-(0x1420+3819-0x230a));newList->nvFile[
NV_PATH_LEN-(0x13d1+934-0x1776)]='\0';memset(newList->nvTable,(0x442+157-0x4df),
NV_HASH_LEN*(0x1fc0+1745-0x268d));newList->fileList=newConfig;if(!nv_list)
nv_list=newList;else{newList->next=nv_list->next;nv_list->next=newList;}}else if
(!list->fileList)list->fileList=newConfig;else{if(newConfig==NULL)return 
RESULT_FAIL;newConfig->next=list->fileList->next;list->fileList->next=newConfig;
}return RESULT_SUCCESS;}static bool isCfgConfiged(char*configFile){T_NV_NODE*
list=NULL;T_NV_CONFIG*config=NULL;for(list=nv_list;list;list=list->next){for(
config=list->fileList;config;config=config->next){if(!strcmp(config->configFile,
configFile))return true;}}return false;}static bool isNvConfiged(char*nvFile){
T_NV_NODE*list=NULL;for(list=nv_list;list;list=list->next){if(!strcmp(list->
nvFile,nvFile))return true;}return false;}static uint getSum(const char*s,int 
len){uint sum=(0x824+3540-0x15f8);char*data=(char*)s;while(len-- >
(0x1ce8+2083-0x250b)){sum+=(*data++);}return sum;}static int saveNvFs(char*
nvName,char*nvFile){int i=(0x1943+3467-0x26ce);int sum=(0x41+7370-0x1d0b);int 
bufSize=(0x6f0+7332-0x2394);int itemSize=(0xa4c+6696-0x2474);int ret=
(0x63c+7310-0x22ca);int fp=(0xa53+2838-0x1569);char*buf=NULL;T_NV_NODE*list=NULL
;T_NV_ITEM*item=NULL;for(list=nv_list;list;list=list->next){if(strcmp(list->
nvFile,nvName))continue;fp=open(nvFile,O_SYNC|O_RDWR|O_CREAT|O_TRUNC,
(0x12d1+4748-0x23bd));if(fp==-(0xaaa+6896-0x2599)){printf(
"\x6f\x70\x65\x6e\x20\x25\x73\x20\x66\x61\x69\x6c\x2c\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,nvFile,errno);return RESULT_FILE_OPEN_FAIL;}buf=(char*)malloc(NV_BLOCK_SIZE);if
(!buf){close(fp);return RESULT_MALLOC_FAIL;}for(i=(0x2b8+7500-0x2004);i<
NV_HASH_LEN;i++){for(item=list->nvTable[i];item;item=item->next){if(strcmp(
nvFile,NV_FS_SHOW)&&!item->saveFlag)continue;itemSize=strlen(item->key)+strlen(
item->value)+(0xc08+5667-0x2229);if(bufSize+itemSize>NV_BLOCK_SIZE){if(write(fp,
buf,bufSize)<(0x1ebd+1779-0x25b0)){printf(
"\x65\x72\x72\x6f\x72\x20\x25\x73\x20\x25\x64\x3a\x20\x77\x72\x69\x74\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,__FILE__,__LINE__,errno);close(fp);free(buf);return RESULT_FILE_WRITE_FAIL;}sum
+=getSum(buf,bufSize);bufSize=(0xccc+6498-0x262e);}sprintf(buf+bufSize,
"\x25\x73\x3d\x25\x73",item->key,item->value);bufSize+=itemSize;}}if(bufSize!=
(0x1b40+541-0x1d5d)){if(write(fp,buf,bufSize)<(0x8f0+4525-0x1a9d)){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x77\x72\x69\x74\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,errno);close(fp);free(buf);return RESULT_FILE_WRITE_FAIL;}sum+=getSum(buf,
bufSize);}sum+=NV_FILE_FLAG;if(write(fp,&sum,NV_CHECK_SIZE)<(0xc8c+383-0xe0b)){
printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x20\x77\x72\x69\x74\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64" "\n"
,errno);close(fp);free(buf);return RESULT_FILE_WRITE_FAIL;}ret=fsync(fp);free(
buf);close(fp);if(ret<(0x1bbc+1247-0x209b)){printf(
"\x53\x79\x6e\x63\x20\x46\x61\x69\x6c\x65\x64\x3a\x25\x73\x2c\x20\x66\x69\x6c\x65\x20\x70\x61\x74\x68\x3a\x25\x73"
,strerror(errno),nvFile);return ret;}return RESULT_SUCCESS;}return 
RESULT_NO_FILE;}static int nvget(char*file,char*key,char*value){int index=
(0x2321+143-0x23b0);T_NV_NODE*list=NULL;T_NV_ITEM*item=NULL;for(list=nv_list;
list;list=list->next){if(strcmp(list->nvFile,file))continue;index=hash(key)%
NV_HASH_LEN;for(item=list->nvTable[index];item;item=item->next){if(strcmp(item->
key,key))continue;strncpy(value,item->value,NV_MAX_VAL_LEN-(0x1b81+1334-0x20b6))
;value[NV_MAX_VAL_LEN-(0x656+3211-0x12e0)]='\0';return RESULT_SUCCESS;}}return 
RESULT_NO_ITEM;}static int nvset(char*file,const char*key,const char*value,int 
saveFlag){int index=(0x1049+2799-0x1b38);int ret=(0x1567+1310-0x1a85);int 
key_buf_len=(0xe61+5171-0x2294);int value_buf_len=(0x1be1+210-0x1cb3);T_NV_NODE*
list=NULL;T_NV_ITEM*item=NULL;T_NV_ITEM*newItem=NULL;if(NULL==key||NULL==value)
return RESULT_FAIL;key_buf_len=strlen(key)+(0x43+2942-0xbc0);value_buf_len=
strlen(value)+(0x20ef+696-0x23a6);for(list=nv_list;list;list=list->next){if(
strcmp(list->nvFile,file))continue;index=hash(key)%NV_HASH_LEN;for(item=list->
nvTable[index];item;item=item->next){if(strcmp(item->key,key))continue;if(
saveFlag)item->saveFlag=saveFlag;if(!strcmp(item->value,value))return 
RESULT_SUCCESS;free(item->value);item->value=(char*)malloc(value_buf_len);if(!
item->value)return RESULT_MALLOC_FAIL;strncpy(item->value,value,value_buf_len-
(0x106d+2774-0x1b42));item->value[value_buf_len-(0x1d9+8462-0x22e6)]='\0';return
 RESULT_SUCCESS;}newItem=(T_NV_ITEM*)malloc(sizeof(T_NV_ITEM));if(!newItem)
return RESULT_MALLOC_FAIL;newItem->key=(char*)malloc(key_buf_len);if(!newItem->
key){free(newItem);return RESULT_MALLOC_FAIL;}newItem->value=(char*)malloc(
value_buf_len);if(!newItem->value){free(newItem->key);free(newItem);return 
RESULT_MALLOC_FAIL;}strncpy(newItem->key,key,key_buf_len-(0x16ca+3361-0x23ea));
newItem->key[key_buf_len-(0x361+4820-0x1634)]='\0';strncpy(newItem->value,value,
value_buf_len-(0x26c+6079-0x1a2a));newItem->value[value_buf_len-
(0x114+4552-0x12db)]='\0';newItem->next=NULL;newItem->saveFlag=saveFlag;newItem
->update_flag=(0x154+6582-0x1b0a);if(!list->nvTable[index])list->nvTable[index]=
newItem;else{newItem->next=list->nvTable[index]->next;list->nvTable[index]->next
=newItem;}return RESULT_SUCCESS;}ret=addConfigFile(file,NULL);if(ret==
RESULT_SUCCESS)return nvset(file,key,value,saveFlag);else return ret;}static int
 nvunset(char*file,char*key){int index=(0x36b+5286-0x1811);T_NV_NODE*list=NULL;
T_NV_ITEM*item=NULL;T_NV_ITEM*prev=NULL;for(list=nv_list;list;list=list->next){
if(strcmp(list->nvFile,file))continue;index=hash(key)%NV_HASH_LEN;for(item=list
->nvTable[index];item;prev=item,item=item->next){if(strcmp(item->key,key))
continue;if(!prev)list->nvTable[index]=item->next;else prev->next=item->next;
free(item->key);free(item->value);free(item);return RESULT_SUCCESS;}}return 
RESULT_NO_ITEM;}static int nvreset(char*file){int ret=(0x4e4+4702-0x1742);
T_NV_NODE*list=NULL;for(list=nv_list;list;list=list->next){if(strcmp(list->
nvFile,file))continue;ret=nvclear(file);if(ret!=RESULT_SUCCESS)return ret;if(
loadFactroyParam(list)!=RESULT_SUCCESS)return RESULT_FAIL;return nvcommit(file);
}return RESULT_NO_FILE;}static int nvclear(char*file){int i=(0x62a+3452-0x13a6);
T_NV_NODE*list=NULL;T_NV_ITEM*cur=NULL;T_NV_ITEM*item=NULL;for(list=nv_list;list
;list=list->next){if(strcmp(list->nvFile,file))continue;for(i=
(0xc83+3730-0x1b15);i<NV_HASH_LEN;i++){for(item=list->nvTable[i];item;){cur=item
;item=item->next;free(cur->key);free(cur->value);free(cur);}list->nvTable[i]=
NULL;}return RESULT_SUCCESS;}return RESULT_NO_FILE;}static int nvcommit(char*
file){int ret=(0x1f6a+1269-0x245f);char nvMainFile[NV_PATH_LEN]={
(0x1b4d+2319-0x245c)};char nvBackupFile[NV_PATH_LEN]={(0x247+2275-0xb2a)};
sprintf(nvMainFile,"\x25\x73\x2f\x25\x73",NV_FS_MAIN_PATH,file);sprintf(
nvBackupFile,"\x25\x73\x2f\x25\x73",NV_FS_BACKUP_PATH,file);ret=saveNvFs(file,
nvMainFile);if(ret!=RESULT_SUCCESS)return ret;return restoreNvFs(nvBackupFile,
nvMainFile);}
#ifdef __cplusplus
}
#endif

