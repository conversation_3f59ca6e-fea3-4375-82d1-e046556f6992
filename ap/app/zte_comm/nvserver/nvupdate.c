
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>
#include <string.h>
#include <sys/file.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include "nvserver.h"
#include "nv_typedef.h"
#include <message.h>
#include <fota_common.h>
#ifdef FOTA_AB
#include "zxic_fota_ab_upgrade.h"
#endif
#ifdef __cplusplus
extern"C"{
#endif
extern T_NV_NODE*nv_list;
#ifdef FOTA_AB
int get_update_status(void){int ret=dual_AB_get_fota_status_for_nv();if(ret==
(0xe3b+5051-0x21f5))return(0x56a+7205-0x218d);else return(0x27b+1564-0x897);}
#else
int get_update_status(void){int update_status;FILE*fd=(0x9d7+5638-0x1fdd);int 
ret;char*filename=NULL;if(access(FOTA_UPDATE_STATUS_FILE_OLD,R_OK)==
(0x7f8+5974-0x1f4e)){filename=FOTA_UPDATE_STATUS_FILE_OLD;}else{filename=
FOTA_UPDATE_STATUS_FILE;}printf(
"get_update_status, read_update_status from %s\n",filename);fd=fopen(filename,
"\x72\x62\x2b");if(fd==NULL){printf(
"\x5b\x6e\x76\x73\x65\x72\x76\x65\x72\x5d\x75\x70\x64\x61\x74\x65\x5f\x73\x74\x61\x74\x75\x73\x20\x6f\x70\x65\x6e\x20\x20\x65\x72\x72\x6f\x72\x3a\x25\x73" "\n"
,strerror(errno));goto error0;}ret=fscanf(fd,"\x25\x64",(int*)&update_status);if
(ret<(0x936+2334-0x1254)){printf(
"\x67\x65\x74\x20\x69\x6e\x66\x6f\x20\x66\x72\x6f\x6d\x20\x66\x69\x6c\x65\x20\x65\x72\x72\x6f\x72\x3a\x25\x73" "\n"
,strerror(errno));fclose(fd);goto error0;}printf(
"\x75\x70\x64\x61\x74\x65\x5f\x73\x74\x61\x74\x75\x73\x3d\x25\x64" "\n",
update_status);fclose(fd);return update_status;error0:return-(0x939+4988-0x1cb4)
;}
#endif
int nvupdate(char*nv_file,char*config_file,const char*key,const char*value,int 
saveFlag){int index=(0x19f5+1983-0x21b4);int key_buf_len=(0xc35+5264-0x20c5);int
 value_buf_len=(0x13c+8188-0x2138);T_NV_NODE*list=NULL;T_NV_ITEM*item=NULL;
T_NV_ITEM*newItem=NULL;if(NULL==key||NULL==value)return RESULT_FAIL;printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6e\x76\x75\x70\x64\x61\x74\x65\x20\x6e\x76\x5f\x66\x69\x6c\x65\x3a\x25\x73\x20\x6b\x65\x79\x3a\x25\x73\x20\x76\x61\x6c\x75\x65\x3a\x25\x73" "\n"
,nv_file,key,value);key_buf_len=strlen(key)+(0x287+6015-0x1a05);value_buf_len=
strlen(value)+(0xcd9+1434-0x1272);for(list=nv_list;list;list=list->next){if(
strcmp(list->nvFile,nv_file))continue;index=hash(key)%NV_HASH_LEN;for(item=list
->nvTable[index];item;item=item->next){if(strcmp(item->key,key))continue;if(
saveFlag)item->saveFlag=saveFlag;if(!strcmp(item->value,value)){item->
update_flag=(0x646+2308-0xf49);printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6e\x76\x75\x70\x64\x61\x74\x65\x20\x73\x61\x6d\x65\x73\x6b\x69\x70\x3a\x69\x74\x65\x6d\x2d\x3e\x6b\x65\x79\x3a\x25\x73\x20\x69\x74\x65\x6d\x2d\x3e\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x63\x6f\x6e\x66\x69\x67\x5f\x66\x69\x6c\x65\x3a\x25\x73" "\n"
,item->key,item->value,value,config_file);return RESULT_SUCCESS;}if(strstr(
config_file,"\x75\x73\x65\x72")){if((0x1a89+397-0x1c15)==item->update_flag){
printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6e\x76\x75\x70\x64\x61\x74\x65\x20\x73\x65\x63\x6f\x6e\x64\x20\x63\x68\x61\x6e\x67\x65\x3a\x69\x74\x65\x6d\x2d\x3e\x6b\x65\x79\x3a\x25\x73\x20\x69\x74\x65\x6d\x2d\x3e\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x63\x6f\x6e\x66\x69\x67\x5f\x66\x69\x6c\x65\x3a\x25\x73" "\n"
,item->key,item->value,value,config_file);}else{item->update_flag=
(0x8f7+1971-0x10a9);printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6e\x76\x75\x70\x64\x61\x74\x65\x20\x75\x73\x65\x72\x73\x6b\x69\x70\x3a\x69\x74\x65\x6d\x2d\x3e\x6b\x65\x79\x3a\x25\x73\x20\x69\x74\x65\x6d\x2d\x3e\x76\x61\x6c\x75\x65\x31\x3a\x25\x73\x20\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x63\x6f\x6e\x66\x69\x67\x5f\x66\x69\x6c\x65\x3a\x25\x73" "\n"
,item->key,item->value,value,config_file);return RESULT_SUCCESS;}}printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x6b\x65\x79\x3d\x25\x73\x20\x63\x68\x61\x6e\x67\x65\x20\x76\x61\x6c\x75\x65\x3a\x25\x73\x20\x74\x6f\x20\x76\x61\x6c\x75\x65\x3d\x25\x73\x20" "\n"
,item->key,item->value,value);free(item->value);item->value=(char*)malloc(
value_buf_len);if(!item->value)return RESULT_MALLOC_FAIL;strncpy(item->value,
value,value_buf_len-(0x41f+1988-0xbe2));item->value[value_buf_len-
(0x13ad+337-0x14fd)]='\0';item->update_flag=(0x1453+878-0x17c0);return 
RESULT_SUCCESS;}newItem=(T_NV_ITEM*)malloc(sizeof(T_NV_ITEM));if(!newItem){
printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x52\x45\x53\x55\x4c\x54\x5f\x4d\x41\x4c\x4c\x4f\x43\x5f\x46\x41\x49\x4c\x31\x20" "\n"
);return RESULT_MALLOC_FAIL;}newItem->key=(char*)malloc(strlen(key)+
(0xdb6+2401-0x1716));if(!newItem->key){free(newItem);printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x52\x45\x53\x55\x4c\x54\x5f\x4d\x41\x4c\x4c\x4f\x43\x5f\x46\x41\x49\x4c\x32" "\n"
);return RESULT_MALLOC_FAIL;}newItem->value=(char*)malloc(value_buf_len);if(!
newItem->value){free(newItem->key);free(newItem);printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x52\x45\x53\x55\x4c\x54\x5f\x4d\x41\x4c\x4c\x4f\x43\x5f\x46\x41\x49\x4c\x33\x20" "\n"
);return RESULT_MALLOC_FAIL;}strncpy(newItem->key,key,key_buf_len-
(0x1a29+1212-0x1ee4));newItem->key[key_buf_len-(0x1893+3293-0x256f)]='\0';
strncpy(newItem->value,value,value_buf_len-(0x16dd+2601-0x2105));newItem->value[
value_buf_len-(0x1714+617-0x197c)]='\0';newItem->next=NULL;newItem->saveFlag=
saveFlag;newItem->update_flag=(0x11b1+2869-0x1ce5);printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x61\x64\x64\x20\x6b\x65\x79\x3d\x25\x73\x2c\x20\x76\x61\x6c\x75\x65\x3d\x25\x73\x20" "\n"
,newItem->key,newItem->value);if(!list->nvTable[index])list->nvTable[index]=
newItem;else{newItem->next=list->nvTable[index]->next;list->nvTable[index]->next
=newItem;}return RESULT_SUCCESS;}return RESULT_FAIL;}int reloadFactroyParam(
T_NV_NODE*list){char*val=NULL;FILE*fp=NULL;T_NV_CONFIG*config=NULL;char buf[
NV_MAX_ITEM_LEN]={(0x12e1+3589-0x20e6)};printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x72\x65\x6c\x6f\x61\x64\x46\x61\x63\x74\x72\x6f\x79\x50\x61\x72\x61\x6d\x20\x6e\x76\x46\x69\x6c\x65\x3a\x25\x73" "\n"
,list->nvFile);for(config=list->fileList;config;config=config->next){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x72\x65\x6c\x6f\x61\x64\x46\x61\x63\x74\x72\x6f\x79\x50\x61\x72\x61\x6d\x20\x63\x6f\x6e\x66\x69\x67\x46\x69\x6c\x65\x20\x73\x74\x61\x72\x74\x3a\x25\x73\x21" "\n"
,config->configFile);fp=fopen(config->configFile,"\x72\x6f");if(!fp){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x6f\x70\x65\x6e\x20\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x61\x69\x6c\x20\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x21" "\n"
,config->configFile,errno);return RESULT_FILE_OPEN_FAIL;}while(fgets(buf,
NV_MAX_ITEM_LEN,fp)){if(buf[(0x1cf5+1679-0x2384)]=='\n'||buf[(0x23bf+440-0x2577)
]==((char)(0xfd5+480-0x1192)))continue;val=strchr(buf,((char)(0x30a+2290-0xbbf))
);if(!val){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x65\x72\x72\x6f\x72\x3a\x25\x73\x20\x66\x69\x6c\x65\x20\x66\x6f\x72\x6d\x61\x74\x20\x65\x72\x72\x6f\x72\x3a\x73\x74\x72\x69\x6e\x67\x20\x3d\x20\x25\x73" "\n"
,config->configFile,buf);continue;}buf[strlen(buf)-(0x2b6+7331-0x1f58)]='\0';*
val++='\0';nvupdate(list->nvFile,config->configFile,buf,val,(0xd9d+351-0xefb));}
printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x72\x65\x6c\x6f\x61\x64\x46\x61\x63\x74\x72\x6f\x79\x50\x61\x72\x61\x6d\x20\x63\x6f\x6e\x66\x69\x67\x46\x69\x6c\x65\x20\x65\x6e\x64\x3a\x25\x73\x21" "\n"
,config->configFile);fclose(fp);}return RESULT_SUCCESS;}void dump_list(T_NV_ITEM
*list){if(list==NULL){printf(
"\x6c\x69\x73\x74\x20\x69\x73\x20\x6e\x75\x6c\x6c" "\n");return;}T_NV_ITEM*p=
list->next;while(p!=NULL){printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x64\x75\x6d\x70\x20\x6b\x65\x79\x3d\x25\x73\x2c\x20\x76\x61\x6c\x75\x65\x3d\x25\x73\x2c\x20\x70\x3d\x30\x78\x25\x78" "\n"
,p->key,p->value,((unsigned int)p));p=p->next;}}int delete_not_needed(T_NV_NODE*
list){int index=(0x16d3+2378-0x201d);T_NV_ITEM*item=NULL;T_NV_ITEM head={
(0x190+238-0x27e)};T_NV_ITEM*prev=&head;printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x64\x65\x6c\x65\x74\x65\x5f\x6e\x6f\x74\x5f\x6e\x65\x65\x64\x65\x64\x20\x65\x6e\x74\x65\x72\x20\x2a\x2a\x2a" "\n"
);for(index=(0x5fd+2470-0xfa3);index<NV_HASH_LEN;index++){head.next=list->
nvTable[index];prev=&head;for(item=prev->next;item;){if((0x1a8+1316-0x6cb)==item
->update_flag){prev=item;item=item->next;}else{printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x64\x65\x6c\x65\x74\x65\x20\x6b\x65\x79\x3d\x25\x73\x2c\x20\x76\x61\x6c\x75\x65\x3d\x25\x73\x20" "\n"
,item->key,item->value);prev->next=item->next;free(item->key);free(item->value);
free(item);item=prev->next;}}list->nvTable[index]=head.next;}printf(
"\x6e\x76\x73\x65\x72\x76\x65\x72\x20\x64\x65\x6c\x65\x74\x65\x5f\x6e\x6f\x74\x5f\x6e\x65\x65\x64\x65\x64\x20\x65\x6e\x64\x20\x2a\x2a\x2a" "\n"
);return RESULT_SUCCESS;}
#ifdef __cplusplus
}
#endif

