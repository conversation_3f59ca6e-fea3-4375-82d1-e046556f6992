
#ifndef __ZXICBASIC_API_H
#define __ZXICBASIC_API_H

extern ssize_t  safe_read(int fd, void *buf, size_t count);
extern ssize_t  full_read(int fd, void *buf, size_t len);
extern ssize_t  read_close(int fd, void *buf, size_t size);
extern ssize_t  open_read_close(const char *filename, void *buf, size_t size);
extern ssize_t  safe_write(int fd, const void *buf, size_t count);
extern ssize_t  full_write(int fd, const void *buf, size_t len);

#endif