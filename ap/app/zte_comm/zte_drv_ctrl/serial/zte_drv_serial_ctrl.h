#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <unistd.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <pthread.h>
#include <errno.h>
#include <signal.h>
#include <syslog.h>
#include <sys/klog.h>
#include <assert.h>
#include "soft_timer.h"
#include "softap_api.h"

#define Uart_Path              "/sys/devices/platform/zx29_uart."
#define CtsRts_EN              "/ctsrts_input"
#define Wakeup_EN              "/wakeup_enable"
#define APP_CTRL_LOWPOWER      "/app_ctrl"
#define STR_USB_MODE_TYPE                "usb_modetype"

#define CTSRTS_ENABLE 1
#define CTSRTS_DISABLE 0

void com_read_proc();
void Uart_config_and_notify(void);

