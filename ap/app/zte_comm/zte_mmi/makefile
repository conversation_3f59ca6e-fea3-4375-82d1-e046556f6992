# /*****************************************************************************
#* 版权所有 (C)2015, 中兴通讯股份有限公司。
#* 
#* 文件名称:     Makefile
#* 文件标识:     Makefile
#* 内容摘要:     Makefile of ZTE applications
#* 使用方法:     void
#* 
#* 修改日期        版本号      修改标记        修改人          修改内容
#* -----------------------------------------------------------------------------
#* 2015/02/10      V1.0        Create          张楠          创建
#* 
# ******************************************************************************/

#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************
#include $(zte_app_mak)
#include $(zte_app_path)/net_team.mk
include $(COMMON_MK)
WORKPATH = $(zte_lib_path)
SOFT_TIMER_PATH = $(WORKPATH)/libsoft_timer

#*******************************************************************************
# execute
#*******************************************************************************
EXEC    = zte_mmi

#*******************************************************************************
# objects
#*******************************************************************************
OBJS    = mmi_adapter.o mmi.o mmi_battery.o mmi_net.o mmi_wifi.o mmi_tip.o mmi_sms.o mmi_lcd_page.o mmi_lcd.o mmi_lcd_init.o mmi_ctrl.o \
		  mmi_keystrokes.o mmi_lcd_timer.o mmi_led.o mmi_led_init.o mmi_led_adapter.o mmi_poweroff_charger.o mmi_traffic.o mmi_fota.o mmi_msg.o \
		  mmi_cfg.o mmi_poweroff.o mmi_battery_adapter.o mmi_mo.o mmi_mo_en.o mmi_mo_zh.o mmi_voip.o mmi_plat_adapter.o

#*******************************************************************************
# include path
#*******************************************************************************                
INCLUE_PATH = -I. -I./../../include
CFLAGS += -Wextra -Wall $(INCLUE_PATH) $(CUSTOM_MACRO)
CFLAGS  += -I$(SOFT_TIMER_PATH)

ifneq ($(CONFIG_MMI_LCD),no)
CFLAGS  += -I$(WORKPATH)/libzcore/min/zcore_zcore/src/zCore/src/gui/adapter/Linux/inc \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/src/zCore/inc/os \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/inc/zCore/gui \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/inc/zCore/os \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/inc/zCore/kernel \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/inc/zCore/gui/ctrl \
           -I$(WORKPATH)/libzcore/min/zcore_zcore/inc/zCore/mmi \
           -I$(WORKPATH)/libzcore/min/ 
endif

CFLAGS  += -I$(SOFT_TIMER_PATH) \
		   -L$(SOFT_TIMER_PATH)
CFLAGS += -g
CFLAGS += -I$(WORKPATH)/libnvram
CFLAGS += -I$(zte_app_path)/zte_comm/zte_hotplug

ifeq ($(LINUX_TYPE),uClinux)
CFLAGS += -g -O0
endif

#*******************************************************************************
# macro definition
#*******************************************************************************


#*******************************************************************************
# library
#*******************************************************************************
LDLIBS += -lpthread -lm -lrt 
CFLAGS += -I$(SOFT_TIMER_PATH)

LDLIBS += -lnvram -lpthread -lsoft_timer -lsoftap
#LDFLAGS += -L../soft_timer -lsofttimer 

ifneq ($(CONFIG_MMI_LCD),no)
LDLIBS += -lzcore
endif

#*******************************************************************************
# library path
#*******************************************************************************
LDLIBS  += -L$(WORKPATH)/libnvram
LDLIBS  += -L$(WORKPATH)/libsoft_timer

ifneq ($(CONFIG_MMI_LCD),no)
LDLIBS  += -L$(WORKPATH)/libzcore/min
endif

LDLIBS  += -L$(zte_lib_path)/libsoftap

#*******************************************************************************
# targets
#*******************************************************************************
lib: $(OBJS)
	@echo Compiling zte_mmi libraries.
	
clean:
	-rm -f $(OBJS)
	

	
