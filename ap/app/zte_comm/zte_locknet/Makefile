#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************	
include $(COMMON_MK)

##############USER COMIZE BEGIN################
EXEC1 = zte_locknet 

CPU_PUB_ROOT=$(TOPDIR_AP)/../pub

OBJS1 = $(patsubst %.c,%.o,$(wildcard ./src/*.c ))
                                   
CFLAGS += -I.
CFLAGS += -I./inc
CFLAGS += -I$(zte_app_path)/include
CFLAGS += -I$(zte_app_path)/zte_comm/at_ctl/inc
CFLAGS += -I$(zte_lib_path)/libsqlite
CFLAGS += -I$(LINUX_DIR)

CFLAGS += -I$(CPU_PUB_ROOT)/project/zx297520v3/include/nv
CFLAGS += -O -Dlinux=1 -DHIGH_SPEED=1
CFLAGS += -g
CFLAGS += -g -Werror=implicit-function-declaration
 
LDLIBS  += -lnvram_sc -L$(zte_lib_path)/libnvram
LDLIBS  += -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS  += -latutils -L$(zte_lib_path)/libatutils
LDLIBS  += -lsqlite -L$(zte_lib_path)/libsqlite
LDLIBS  += -lsoft_timer_sc -L$(zte_lib_path)/libsoft_timer

LDLIBS  += -lm
LDLIBS  += -lpthread -L$(zte_lib_path)/libpthread
LDLIBS  += -lcpnv -L$(zte_lib_path)/libcpnv
##############USER COMIZE END##################

#*******************************************************************************
# targets
#*******************************************************************************
lib: $(OBJS1)
	@echo Compiling zte_locknet libraries.
	
clean:
	-rm -f $(OBJS1)
