
#include <stdio.h>
#include <time.h>
#include <limits.h>
#include "sms_db.h"
#include "sms_code.h"
#include "cfg_nv_def.h"
typedef sqlite3_callback zte_wms_db_callback;
#define ZTE_WMS_CREATE_TABLE_SMS_SQL        \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73"\
 \
                                            \
"\x28\x69\x64\x20\x49\x4e\x54\x45\x47\x45\x52\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59\x20\x41\x55\x54\x4f\x49\x4e\x43\x52\x45\x4d\x45\x4e\x54"\
 \
                                            \
"\x20\x4e\x4f\x54\x20\x4e\x55\x4c\x4c\x2c\x20\x69\x6e\x64\x20\x54\x45\x58\x54\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x20\x54\x45\x58\x54\x2c\x54\x61\x67"\
 \
                                            \
"\x20\x54\x45\x58\x54\x2c\x4e\x75\x6d\x62\x65\x72\x20\x54\x45\x58\x54\x2c\x43\x63\x5f\x53\x6d\x73\x20\x54\x45\x58\x54\x2c\x43\x63\x5f\x52\x65\x66\x20"\
 \
                                            \
"\x20\x54\x45\x58\x54\x2c\x43\x63\x5f\x54\x6f\x74\x61\x6c\x20\x54\x45\x58\x54\x2c\x20\x43\x63\x5f\x53\x65\x71\x20\x54\x45\x58\x54\x2c\x43\x63\x5f\x4e\x75\x6d"\
 \
                                            \
"\x20\x54\x45\x58\x54\x2c\x43\x63\x5f\x43\x6f\x6e\x74\x65\x6e\x74\x20\x54\x45\x58\x54\x2c\x4c\x61\x6e\x67\x75\x61\x67\x65\x20\x54\x45\x58\x54\x2c\x54\x70\x5f\x44\x63\x73"\
 \
                                            \
"\x20\x54\x45\x58\x54\x2c\x4d\x73\x67\x5f\x52\x65\x66\x20\x54\x45\x58\x54\x2c\x43\x6f\x6e\x74\x65\x6e\x74\x20\x54\x45\x58\x54\x2c\x20"\
 \
                                            \
"\x20\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x5f\x52\x65\x63\x69\x76\x65\x64\x20\x54\x45\x58\x54\x2c\x44\x72\x61\x66\x74\x5f\x47\x72\x6f\x75\x70\x5f\x49\x64\x20\x54\x45\x58\x54\x2c\x59\x65\x61\x72\x20\x54\x45\x58\x54\x2c\x4d\x6f\x6e\x74\x68\x20\x54\x45\x58\x54\x2c"\
 \
                                            \
"\x20\x44\x61\x79\x20\x54\x45\x58\x54\x2c\x48\x6f\x75\x72\x20\x54\x45\x58\x54\x2c\x4d\x69\x6e\x75\x74\x65\x20\x54\x45\x58\x54\x2c\x53\x65\x63\x6f\x6e\x64\x20\x54\x45\x58\x54\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x20\x54\x45\x58\x54\x2c\x4d\x6b\x74\x69\x6d\x65\x20\x49\x4e\x54\x45\x47\x45\x52\x2c\x44\x69\x73\x70\x6c\x61\x79\x4d\x6f\x64\x65\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_CMD_STATUS_SQL       \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x63\x6d\x64\x5f\x73\x74\x61\x74\x75\x73\x28\x43\x6d\x64\x20\x54\x45\x58\x54\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59\x2c\x43\x6d\x64\x5f\x53\x74\x61\x74\x75\x73\x20\x54\x45\x58\x54\x2c"\
 \
                                            \
"\x20\x45\x72\x72\x5f\x43\x6f\x64\x65\x20\x54\x45\x58\x54\x2c\x53\x65\x6e\x64\x5f\x46\x61\x69\x6c\x5f\x43\x6f\x75\x6e\x74\x20\x54\x45\x58\x54\x2c\x44\x65\x6c\x5f\x54\x79\x70\x65\x20\x54\x45\x58\x54\x2c"\
 \
                                            \
"\x20\x44\x65\x6c\x5f\x43\x6f\x75\x6e\x74\x20\x54\x45\x58\x54\x2c\x44\x65\x6c\x5f\x69\x6e\x64\x65\x78\x20\x54\x45\x58\x54\x2c\x20\x46\x61\x69\x6c\x65\x64\x5f\x43\x6f\x75\x6e\x74\x20\x54\x45\x58\x54\x2c\x46\x61\x69\x6c\x65\x64\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_SMS_REP_SQL          \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x72\x65\x70\x6f\x72\x74\x28\x69\x64\x20\x49\x4e\x54\x45\x47\x45\x52\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59"\
 \
                                            \
"\x20\x41\x55\x54\x4f\x49\x4e\x43\x52\x45\x4d\x45\x4e\x54\x20\x4e\x4f\x54\x20\x4e\x55\x4c\x4c\x2c\x20\x20\x61\x64\x64\x72\x65\x73\x73\x20\x54\x45\x58\x54\x2c\x63\x6f\x6e\x74\x65\x6e\x74\x20\x54\x45\x58\x54\x2c\x59\x65\x61\x72\x20\x54\x45\x58\x54\x2c\x4d\x6f\x6e\x74\x68\x20\x54\x45\x58\x54\x2c"\
 \
                                            \
"\x20\x44\x61\x79\x20\x54\x45\x58\x54\x2c\x48\x6f\x75\x72\x20\x54\x45\x58\x54\x2c\x4d\x69\x6e\x75\x74\x65\x20\x54\x45\x58\x54\x2c\x53\x65\x63\x6f\x6e\x64\x20\x54\x45\x58\x54\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_PAR_SQL              \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x28\x69\x64\x20\x49\x4e\x54\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59\x2c\x20\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x20\x54\x45\x58\x54\x2c\x20\x53\x6d\x73\x5f\x53\x63\x61\x20\x54\x45\x58\x54\x2c\x20"\
                                            \
"\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x20\x54\x45\x58\x54\x2c\x54\x70\x5f\x56\x61\x6c\x69\x64\x69\x74\x79\x20\x54\x45\x58\x54\x2c\x53\x65\x6e\x64\x5f\x52\x65\x74\x72\x79\x20\x54\x45\x58\x54\x2c\x4f\x75\x74\x64\x61\x74\x65\x5f\x44\x65\x6c\x65\x74\x65\x20\x54\x45\x58\x54\x2c\x44\x65\x66\x61\x75\x6c\x74\x5f\x53\x74\x6f\x72\x65\x20\x54\x45\x58\x54\x2c\x4d\x61\x78\x5f\x43\x63\x5f\x52\x65\x66\x20\x54\x45\x58\x54\x2c\x20"\
 \
                                            \
"\x20\x4d\x61\x78\x5f\x53\x6d\x73\x5f\x52\x65\x66\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_INFO_SQL             \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x69\x6e\x66\x6f\x28\x69\x64\x20\x49\x4e\x54\x45\x47\x45\x52\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59"\
 \
                                            \
"\x20\x41\x55\x54\x4f\x49\x4e\x43\x52\x45\x4d\x45\x4e\x54\x20\x4e\x4f\x54\x20\x4e\x55\x4c\x4c\x2c\x20\x6e\x61\x6d\x65\x20\x54\x45\x58\x54\x2c\x20\x76\x61\x6c\x75\x65\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_CELL_BRO_SQL         \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x63\x65\x6c\x6c\x5f\x62\x72\x6f\x20\x28\x69\x64\x20\x49\x4e\x54\x45\x47\x45\x52\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59"\
 \
                                            \
"\x20\x41\x55\x54\x4f\x49\x4e\x43\x52\x45\x4d\x45\x4e\x54\x20\x4e\x4f\x54\x20\x4e\x55\x4c\x4c\x2c\x20\x6d\x73\x67\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_CREATE_SEND_CONTENT_SQL     \
"\x43\x52\x45\x41\x54\x45\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x4e\x4f\x54\x20\x45\x58\x49\x53\x54\x53\x20\x73\x65\x6e\x64\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x20\x28\x69\x64\x20\x49\x4e\x54\x45\x47\x45\x52\x20\x50\x52\x49\x4d\x41\x52\x59\x20\x4b\x45\x59"\
 \
                                            \
"\x20\x41\x55\x54\x4f\x49\x4e\x43\x52\x45\x4d\x45\x4e\x54\x20\x4e\x4f\x54\x20\x4e\x55\x4c\x4c\x2c\x20\x6d\x73\x67\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x20\x54\x45\x58\x54\x29\x3b"
#define ZTE_WMS_DROP_ALL_TABLE_SQL          \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x63\x6d\x64\x5f\x73\x74\x61\x74\x75\x73\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x72\x65\x70\x6f\x72\x74\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x69\x6e\x66\x6f\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x63\x65\x6c\x6c\x5f\x62\x72\x6f\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x65\x6e\x64\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x3b"
#define ZTE_WMS_DROP_TABLE_EXCEPTSMS_SQL    \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x63\x6d\x64\x5f\x73\x74\x61\x74\x75\x73\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x72\x65\x70\x6f\x72\x74\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x6d\x73\x5f\x69\x6e\x66\x6f\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x63\x65\x6c\x6c\x5f\x62\x72\x6f\x3b"\
                                            \
"\x44\x52\x4f\x50\x20\x54\x41\x42\x4c\x45\x20\x49\x46\x20\x45\x58\x49\x53\x54\x53\x20\x73\x65\x6e\x64\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x3b"
#define ZTE_WMS_DEL_SIM_SQL \
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x68\x65\x72\x65\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x73\x69\x6d\x27\x3b"
#define OUTDATEINTERVAL 7776000
typedef struct{char*buf_addr;int buf_len;}T_zUfiSms_BufInfo;typedef struct{int 
valid;char*strSQL;}T_zUfiSms_SQLMap;sqlite3*g_zUfiSms_DbPointer=
(0x150d+3177-0x2176);extern T_zUfiSms_ParaInfo g_zUfiSms_CurSmsPara;extern 
unsigned long g_zUfiSms_StoreCapablity[ZTE_WMS_MEMORY_MAX];extern 
T_zUfiSms_DelSms g_zUfiSms_DelMsg;static int isSucess(T_zUfiSms_DbResult dbRst){
return dbRst==ZTE_WMS_DB_OK?ZUFI_SUCC:ZUFI_FAIL;}time_t zte_getsecond(
T_zUfiSms_Date date){time_t timet;struct tm tmtime={(0x1ef1+177-0x1fa2)};int 
tmp_i=(0x270+4959-0x15cf);
#if (0xe04+1773-0x14f1)
if(atoi(date.year)>(0x11e0+3988-0x2111)||atoi(date.year)<(0x1ae6+1925-0x226b)){
printf("[SMS] getsecond error, year out of range: %d!!!",atoi(date.year));return
(0x54c+5337-0x1a25);}
#endif
tmp_i=atoi(date.year);if(tmp_i<(0x154+3927-0x10ab)||tmp_i>INT_MAX-
(0x3d7+4945-0x1727)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x79\x65\x61\x72\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0xca1+2163-0x1514);}tmtime.tm_year=tmp_i+(0x1a0a+2813-0x1d37)-
(0x13f9+2220-0x1539);tmp_i=atoi(date.month);if(tmp_i<(0x964+3986-0x18f6)||tmp_i>
INT_MAX-(0x11cc+1700-0x186f)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x6d\x6f\x6e\x74\x68\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0x6bb+8149-0x2690);}tmtime.tm_mon=tmp_i-(0xbcd+1221-0x1091);tmp_i
=atoi(date.day);if(tmp_i<(0xd68+1960-0x1510)||tmp_i>INT_MAX-(0x936+4135-0x195c))
{at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x64\x61\x79\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0x14c4+216-0x159c);}tmtime.tm_mday=tmp_i;tmp_i=atoi(date.hour);if
(tmp_i<(0xb93+6833-0x2644)||tmp_i>INT_MAX-(0x1167+2059-0x1971)){at_print(LOG_ERR
,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x68\x6f\x75\x72\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0xddc+3167-0x1a3b);}tmtime.tm_hour=tmp_i;tmp_i=atoi(date.min);if(
tmp_i<(0x2c3+3564-0x10af)||tmp_i>INT_MAX-(0xe04+4651-0x202e)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x6d\x69\x6e\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0x15d2+3648-0x2412);}tmtime.tm_min=tmp_i;tmp_i=atoi(date.sec);if(
tmp_i<(0x741+4759-0x19d8)||tmp_i>INT_MAX-(0x15e1+2504-0x1fa8)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x64\x61\x74\x65\x2e\x73\x65\x63\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return(0x17e3+1472-0x1da3);}tmtime.tm_sec=tmp_i;timet=mktime(&tmtime);
return timet;}T_zUfiSms_DbResult zUfiSms_OpenDb(void){int retry_times=
(0x439+7833-0x22d2);int open_rst=SQLITE_ERROR;if(g_zUfiSms_DbPointer!=NULL){
sqlite3_close(g_zUfiSms_DbPointer);g_zUfiSms_DbPointer=NULL;}do{open_rst=
sqlite3_open(ZTE_WMS_DB_PATH,&g_zUfiSms_DbPointer);}while(open_rst==
SQLITE_CANTOPEN&&retry_times++<(0x133c+624-0x15a2));printf(
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4f\x70\x65\x6e\x44\x62\x3a\x20\x72\x65\x74\x72\x79\x5f\x74\x69\x6d\x65\x73\x20\x3d\x20\x25\x64\x2c\x20\x6f\x70\x65\x6e\x5f\x72\x73\x74\x20\x3d\x20\x25\x64" "\n"
,retry_times,open_rst);return open_rst==SQLITE_OK?ZTE_WMS_DB_OK:
ZTE_SMS_DB_ERROR_NOT_OPEN_DB;}T_zUfiSms_DbResult zUfiSms_CloseDb(void){if(
sqlite3_close(g_zUfiSms_DbPointer)!=SQLITE_OK){return ZTE_SMS_DB_ERROR;}
g_zUfiSms_DbPointer=NULL;return ZTE_WMS_DB_OK;}T_zUfiSms_DbResult 
zUfiSms_ExecSql(const char*exec_sql,zte_wms_db_callback callback,void*fvarg){int
 try_times=(0xe66+5637-0x246b);int sqlRst=SQLITE_ERROR;while(try_times++<
(0x19f6+1792-0x20ec)){sqlRst=sqlite3_exec(g_zUfiSms_DbPointer,exec_sql,callback,
fvarg,NULL);if(sqlRst!=SQLITE_BUSY&&sqlRst!=SQLITE_LOCKED&&sqlRst!=SQLITE_IOERR)
{break;}printf(
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x45\x78\x65\x63\x53\x71\x6c\x3a\x20\x74\x72\x79\x5f\x74\x69\x6d\x65\x73\x3d\x25\x64\x2c\x20\x53\x51\x4c\x3d\x25\x73\x2c\x20\x45\x72\x72\x6d\x73\x67\x3d\x25\x73" "\n"
,try_times,exec_sql,sqlite3_errmsg(g_zUfiSms_DbPointer));sleep(
(0x640+3169-0x12a0));}if(sqlRst!=SQLITE_OK){printf(
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x45\x78\x65\x63\x53\x71\x6c\x3a\x20\x74\x72\x79\x5f\x74\x69\x6d\x65\x73\x3d\x25\x64\x2c\x20\x53\x51\x4c\x3d\x25\x73\x2c\x20\x45\x72\x72\x6d\x73\x67\x3d\x25\x73" "\n"
,try_times,exec_sql,sqlite3_errmsg(g_zUfiSms_DbPointer));return ZTE_SMS_DB_ERROR
;}else{
#ifdef WEBS_SECURITY
if(access(ZTE_WMS_TMP1_PATH,F_OK)==(0x736+104-0x79e)){slog(PB_PRINT,SLOG_ERR,
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x45\x78\x65\x63\x53\x71\x6c\x20\x64\x62\x20\x73\x74\x61\x79"
);if(remove(ZTE_WMS_TMP1_PATH)!=(0x1a62+377-0x1bdb)){slog(SMS_PRINT,SLOG_ERR,
"\x72\x65\x6d\x6f\x76\x65\x20\x57\x4d\x53\x5f\x54\x4d\x50\x31\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}}{char rnum_buf[(0xb72+6109-0x2337)]={(0x255+9240-0x266d)};char cmd[
(0x9cc+2273-0x122d)]={(0x379+7256-0x1fd1)};sc_cfg_get(
"\x72\x6e\x75\x6d\x5f\x61\x74",rnum_buf,sizeof(rnum_buf));snprintf(cmd,sizeof(
cmd),
"\x2f\x62\x69\x6e\x2f\x6f\x70\x65\x6e\x73\x73\x6c\x20\x65\x6e\x63\x20\x2d\x65\x20\x2d\x61\x65\x73\x32\x35\x36\x20\x2d\x73\x61\x6c\x74\x20\x2d\x69\x6e\x20\x25\x73\x20\x2d\x6f\x75\x74\x20\x25\x73\x20\x2d\x70\x61\x73\x73\x20\x70\x61\x73\x73\x3a\x25\x73"
,ZTE_WMS_DB_PATH,ZTE_WMS_TMP1_PATH,rnum_buf);zxic_system(cmd);if(access(
ZTE_WMS_TMP1_PATH,F_OK)==(0x9db+5925-0x2100)){if(remove(ZTE_WMS_SEC_PATH)!=
(0x248+7847-0x20ef)){slog(SMS_PRINT,SLOG_ERR,
"\x72\x65\x6d\x6f\x76\x65\x20\x57\x4d\x53\x5f\x53\x45\x43\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}if(rename(ZTE_WMS_TMP1_PATH,ZTE_WMS_SEC_PATH)!=(0x74+1760-0x754)){slog(
SMS_PRINT,SLOG_ERR,
"\x72\x65\x6e\x61\x6d\x65\x20\x57\x4d\x53\x5f\x54\x4d\x50\x31\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c"
);}}}
#endif	
return ZTE_WMS_DB_OK;}}int zUfiSms_GetFirstColumnInt(void*fvarg,int columns,char
**zresult,char**lname){if(columns>=(0x349+1212-0x804)){if(zresult[
(0x3f3+7388-0x20cf)]==NULL){*(int*)fvarg=(0x4ea+6684-0x1f06);}else{*(int*)fvarg=
atoi(zresult[(0x1ee7+1456-0x2497)]);}return SQLITE_OK;}else{return SQLITE_ERROR;
}}int zUfiSms_SetCmdStatus(T_zUfiSms_StatusInfo*ptSetStatus){T_zUfiSms_DbResult 
result=ZTE_WMS_DB_OK;char*strSQL=NULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x74\x43\x6d\x64\x53\x74\x61\x74\x75\x73\x20\x65\x6e\x74\x65\x72\x2e" "\n"
);strSQL=sqlite3_mprintf(
"\x49\x4e\x53\x45\x52\x54\x20\x4f\x52\x20\x52\x45\x50\x4c\x41\x43\x45\x20\x49\x4e\x54\x4f\x20\x25\x73\x28\x43\x6d\x64\x2c\x43\x6d\x64\x5f\x53\x74\x61\x74\x75\x73\x2c\x45\x72\x72\x5f\x43\x6f\x64\x65\x2c\x53\x65\x6e\x64\x5f\x46\x61\x69\x6c\x5f\x43\x6f\x75\x6e\x74\x2c\x44\x65\x6c\x5f\x43\x6f\x75\x6e\x74\x29\x20"
"\x56\x41\x4c\x55\x45\x53\x28\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x29\x3b"
,ZTE_WMS_DB_CMD_STATUS_TABLE,ptSetStatus->cmd,ptSetStatus->cmd_status,
ptSetStatus->err_code,ptSetStatus->send_failed_count,ptSetStatus->
delete_failed_count);result=zUfiSms_ExecSql(strSQL,NULL,NULL);sqlite3_free(
strSQL);return isSucess(result);}void zUfiSms_SetParameterNv(T_zUfiSms_ParaInfo*
para){if((0x12dc+1477-0x18a1)==(int)para->status_report_on){sc_cfg_set(
NV_REPORT_ENABLE,"\x30");}else{sc_cfg_set(NV_REPORT_ENABLE,"\x31");}if(
(0x3ab+4206-0x1419)==(int)para->sendfail_retry_on){sc_cfg_set(NV_SENDFAIL_RETRY,
"\x30");}else{sc_cfg_set(NV_SENDFAIL_RETRY,"\x31");}if((0xe95+4406-0x1fcb)==(int
)para->outdate_delete_on){sc_cfg_set(NV_OUTDATE_DELETE,"\x30");}else{sc_cfg_set(
NV_OUTDATE_DELETE,"\x31");}if(*(para->default_store)!='\0'){sc_cfg_set(
NV_DEFAULT_STORE,(char*)para->default_store);}if((0x18c+6156-0x1998)==(int)para
->mem_store){sc_cfg_set(NV_PRA_MEMSTORE,"\x30");}else{sc_cfg_set(NV_PRA_MEMSTORE
,"\x31");}if((0x2f6+5579-0x17c2)==(int)para->tp_validity_period){sc_cfg_set(
NV_SMS_VP,"\x6c\x6f\x6e\x67\x65\x73\x74");}else if((0x2eb+2884-0xd82)==(int)para
->tp_validity_period){sc_cfg_set(NV_SMS_VP,"\x6f\x6e\x65\x77\x65\x65\x6b");}else
 if((0x793+3089-0x12fd)==(int)para->tp_validity_period){sc_cfg_set(NV_SMS_VP,
"\x6f\x6e\x65\x5f\x64\x61\x79");}else if((0x130+5932-0x17cd)==(int)para->
tp_validity_period){sc_cfg_set(NV_SMS_VP,"\x74\x77\x65\x6c\x76\x65\x68");}printf
(
"\x77\x6d\x73\x5f\x64\x62\x5f\x73\x65\x74\x5f\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x73\x20\x3a\x3a\x20\x73\x65\x74\x20\x74\x70\x5f\x76\x61\x6c\x69\x64\x69\x74\x79\x5f\x70\x65\x72\x69\x6f\x64\x20\x25\x64" "\n"
,(int)para->tp_validity_period);}int zUfiSms_SetDbParameters(T_zUfiSms_ParaInfo*
para){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;char*strSQL=NULL;int count=
(0x187+3637-0xfbc);if(NULL==para){return-(0x7f9+1476-0xdbc);}zUfiSms_ExecSql(
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20"
 ZTE_WMS_DB_PARAMETER_TABLE"\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x31\x3b",
zUfiSms_GetFirstColumnInt,&count);if(count>(0x3b7+5346-0x1899)){strSQL=
sqlite3_mprintf(
"\x55\x50\x44\x41\x54\x45\x20\x25\x73\x20\x53\x45\x54\x20\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x3d\x27\x25\x64\x27\x2c\x53\x6d\x73\x5f\x53\x63\x61\x3d\x27\x25\x71\x27\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x64\x27\x2c\x54\x70\x5f\x56\x61\x6c\x69\x64\x69\x74\x79\x3d\x27\x25\x64\x27\x2c\x53\x65\x6e\x64\x5f\x52\x65\x74\x72\x79\x3d\x27\x25\x64\x27\x2c\x4f\x75\x74\x64\x61\x74\x65\x5f\x44\x65\x6c\x65\x74\x65\x3d\x27\x25\x64\x27\x2c\x44\x65\x66\x61\x75\x6c\x74\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x71\x27\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x31\x3b"
,ZTE_WMS_DB_PARAMETER_TABLE,(int)para->status_report_on,para->sca,(int)para->
mem_store,(int)para->tp_validity_period,(int)para->sendfail_retry_on,(int)para->
outdate_delete_on,para->default_store,ZTE_WMS_DB_PARAMETER_TABLE);}else{strSQL=
sqlite3_mprintf(
"\x49\x4e\x53\x45\x52\x54\x20\x49\x4e\x54\x4f\x20\x25\x73\x28\x69\x64\x2c\x20\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x2c\x53\x6d\x73\x5f\x53\x63\x61\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x2c\x54\x70\x5f\x56\x61\x6c\x69\x64\x69\x74\x79\x2c\x53\x65\x6e\x64\x5f\x52\x65\x74\x72\x79\x2c\x4f\x75\x74\x64\x61\x74\x65\x5f\x44\x65\x6c\x65\x74\x65\x2c\x44\x65\x66\x61\x75\x6c\x74\x5f\x53\x74\x6f\x72\x65\x2c\x4d\x61\x78\x5f\x43\x63\x5f\x52\x65\x66\x2c\x4d\x61\x78\x5f\x53\x6d\x73\x5f\x52\x65\x66\x29\x20"
"\x56\x41\x4c\x55\x45\x53\x28\x27\x31\x27\x2c\x20\x27\x25\x64\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x71\x27\x2c\x27\x30\x27\x2c\x27\x30\x27\x29\x3b"
,ZTE_WMS_DB_PARAMETER_TABLE,(int)para->status_report_on,para->sca,(int)para->
mem_store,(int)para->tp_validity_period,(int)para->sendfail_retry_on,(int)para->
outdate_delete_on,para->default_store);}result=zUfiSms_ExecSql(strSQL,NULL,NULL)
;sqlite3_free(strSQL);if(result==ZTE_WMS_DB_OK){zUfiSms_SetParameterNv(para);
return(0x93f+6082-0x2101);}return-(0x102a+4150-0x205f);}int 
zUfiSms_GetTotalCount(const char*pDbTable,int*pTotalCount){T_zUfiSms_DbResult 
result=ZTE_WMS_DB_OK;char*strSQL=NULL;strSQL=sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x71\x27\x3b"
,pDbTable);result=zUfiSms_ExecSql(strSQL,zUfiSms_GetFirstColumnInt,pTotalCount);
sqlite3_free(strSQL);return isSucess(result);}int zUfiSms_GetSmsMaxReferInDb(
void){int max_sms_ref=-(0x28c+808-0x5b3);zUfiSms_ExecSql(
"\x53\x45\x4c\x45\x43\x54\x20\x4d\x61\x78\x5f\x53\x6d\x73\x5f\x52\x65\x66\x20\x46\x52\x4f\x4d\x20"
 ZTE_WMS_DB_PARAMETER_TABLE
"\x20\x57\x48\x45\x52\x45\x20\x69\x64\x20\x3d\x20\x31\x3b",
zUfiSms_GetFirstColumnInt,&max_sms_ref);return max_sms_ref;}int 
zUfiSms_GetConcatInfo(char*mem_store,long id,T_zUfiSms_DbStoreStr*pac,char*
content,int len){sqlite3_stmt*stmt=NULL;char*strSQL=sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x69\x6e\x64\x2c\x43\x63\x5f\x53\x65\x71\x2c\x43\x63\x5f\x43\x6f\x6e\x74\x65\x6e\x74\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x71\x27\x3b"
,id,mem_store);(void)sqlite3_prepare(g_zUfiSms_DbPointer,strSQL,-
(0x20c9+93-0x2125),&stmt,(0x10d+2637-0xb5a));while(SQLITE_ROW==sqlite3_step(stmt
)){char*column_text=NULL;memset(pac->IndStr,(0x628+4276-0x16dc),sizeof(pac->
IndStr));memset(pac->Seg_Seq,(0xe5f+2858-0x1989),sizeof(pac->Seg_Seq));if((
column_text=sqlite3_column_text(stmt,(0x693+650-0x91d)))!=NULL)strncpy(pac->
IndStr,column_text,sizeof(pac->FormatInd)-(0x115a+5498-0x26d3));if((column_text=
sqlite3_column_text(stmt,(0xe95+1198-0x1342)))!=NULL)strncpy(pac->Seg_Seq,
column_text,sizeof(pac->FormatSeq)-(0x146c+2882-0x1fad));if((column_text=
sqlite3_column_text(stmt,(0x13a8+3594-0x21b0)))!=NULL)strncpy(content,
column_text,len-(0x1c59+934-0x1ffe));printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x43\x6f\x6e\x63\x61\x74\x49\x6e\x66\x6f\x3a\x25\x73\x2c\x25\x73" "\n"
,pac->IndStr,pac->Seg_Seq);}(void)sqlite3_finalize(stmt);sqlite3_free(strSQL);
return(0x784+7835-0x261f);}int zUfiSms_UpdateConcatSmsToDb(T_zUfiSms_DbStoreData
*db_data,const char*mem_store,char*format_concat,char*content,
T_zUfiSms_DbStoreStr*pac,int concat_num,long id){char*sql=NULL;
T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;sql=sqlite3_mprintf(
"\x55\x50\x44\x41\x54\x45\x20\x73\x6d\x73\x20\x53\x45\x54\x20\x69\x6e\x64\x3d\x27\x25\x73\x27\x2c\x54\x61\x67\x3d\x27\x25\x64\x27\x2c\x43\x63\x5f\x53\x65\x71\x3d\x27\x25\x73\x27\x2c\x43\x63\x5f\x4e\x75\x6d\x3d\x27\x25\x64\x27\x2c\x20"
"\x43\x63\x5f\x43\x6f\x6e\x74\x65\x6e\x74\x3d\x27\x25\x71\x27\x2c\x43\x6f\x6e\x74\x65\x6e\x74\x3d\x27\x25\x71\x27\x2c\x59\x65\x61\x72\x3d\x27\x25\x71\x27\x2c\x4d\x6f\x6e\x74\x68\x3d\x27\x25\x71\x27\x2c\x20"
"\x44\x61\x79\x3d\x27\x25\x71\x27\x2c\x48\x6f\x75\x72\x3d\x27\x25\x71\x27\x2c\x4d\x69\x6e\x75\x74\x65\x3d\x27\x25\x71\x27\x2c\x53\x65\x63\x6f\x6e\x64\x3d\x27\x25\x71\x27\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x3d\x27\x25\x71\x27\x20"
"\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x71\x27\x3b"
,pac->FormatInd,db_data->tag,pac->FormatSeq,concat_num,format_concat,content,
db_data->julian_date.year,db_data->julian_date.month,db_data->julian_date.day,
db_data->julian_date.hour,db_data->julian_date.min,db_data->julian_date.sec,
db_data->julian_date.timezone,id,mem_store);printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x55\x70\x64\x61\x74\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x3a\x25\x73\x2d\x2d\x25\x73" "\n"
,sql,pac->FormatSeq);result=zUfiSms_ExecSql(sql,NULL,NULL);sqlite3_free(sql);
return isSucess(result);}int zUfiSms_InsertConcatSmsToDb(T_zUfiSms_DbStoreData*
db_data,const char*mem_store,char*format_concat,char*content,
T_zUfiSms_DbStoreStr*pac,int concat_num){char*sql=NULL;T_zUfiSms_DbResult result
=ZTE_WMS_DB_OK;sql=sqlite3_mprintf(
"\x49\x4e\x53\x45\x52\x54\x20\x49\x4e\x54\x4f\x20\x73\x6d\x73\x28\x69\x6e\x64\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x2c\x54\x61\x67\x2c\x4e\x75\x6d\x62\x65\x72\x2c\x43\x63\x5f\x53\x6d\x73\x2c\x43\x63\x5f\x52\x65\x66\x2c\x43\x63\x5f\x54\x6f\x74\x61\x6c\x2c\x43\x63\x5f\x53\x65\x71\x2c\x43\x63\x5f\x4e\x75\x6d\x2c\x43\x63\x5f\x43\x6f\x6e\x74\x65\x6e\x74\x2c\x54\x70\x5f\x44\x63\x73\x2c\x20\x4d\x73\x67\x5f\x52\x65\x66\x2c"
"\x43\x6f\x6e\x74\x65\x6e\x74\x2c\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x5f\x52\x65\x63\x69\x76\x65\x64\x2c\x44\x72\x61\x66\x74\x5f\x47\x72\x6f\x75\x70\x5f\x49\x64\x2c\x59\x65\x61\x72\x2c\x4d\x6f\x6e\x74\x68\x2c\x44\x61\x79\x2c\x48\x6f\x75\x72\x2c\x4d\x69\x6e\x75\x74\x65\x2c\x53\x65\x63\x6f\x6e\x64\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x2c\x4d\x6b\x74\x69\x6d\x65\x2c\x44\x69\x73\x70\x6c\x61\x79\x4d\x6f\x64\x65\x29\x20"
"\x56\x41\x4c\x55\x45\x53\x28" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c\x20\x27\x25\x64\x27\x2c" "\'" "\x25\x71" "\'" "\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c" "\'" "\x25\x71" "\'" "\x2c\x27\x25\x64\x27\x2c" "\'" "\x25\x71" "\'" "\x2c\x27\x25\x64\x27\x2c\x20\x27\x25\x64\x27\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c\x20" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c" "\'" "\x25\x71" "\'" "\x2c\x27\x25\x64\x27\x2c\x20\x27\x25\x64\x27\x29\x3b"
,pac->FormatInd,mem_store,db_data->tag,db_data->number,db_data->concat_sms,
db_data->concat_info[(0xf49+1487-0x1518)],db_data->concat_info[
(0x3b9+2031-0xba7)],pac->FormatSeq,concat_num,format_concat,db_data->tp_dcs,
db_data->msg_ref,content,"\x30",db_data->draft_group_id,db_data->julian_date.
year,db_data->julian_date.month,db_data->julian_date.day,db_data->julian_date.
hour,db_data->julian_date.min,db_data->julian_date.sec,db_data->julian_date.
timezone,(unsigned int)zte_getsecond(db_data->julian_date),db_data->
msg_displaymode);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x49\x6e\x73\x65\x72\x74\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x20\x73\x71\x6c\x3d\x25\x73\x2e" "\n"
,sql);result=zUfiSms_ExecSql(sql,NULL,NULL);sqlite3_free(sql);return isSucess(
result);}int zUfiSms_GetConcatMaxReferInDb(void){int ConcatMaxRefer=
(0x1b30+2822-0x2636);int result=(0x659+5688-0x1c91);result=zUfiSms_ExecSql(
"\x53\x45\x4c\x45\x43\x54\x20\x4d\x61\x78\x5f\x43\x63\x5f\x52\x65\x66\x20\x46\x52\x4f\x4d\x20"
 ZTE_WMS_DB_PARAMETER_TABLE
"\x20\x57\x48\x45\x52\x45\x20\x69\x64\x20\x3d\x20\x31\x3b",
zUfiSms_GetFirstColumnInt,&ConcatMaxRefer);if(ZTE_WMS_DB_OK!=result){return-
(0x1853+1377-0x1db3);}return ConcatMaxRefer;}int zUfiSms_CreateTables(){int iMap
=(0x654+6262-0x1eca);const T_zUfiSms_SQLMap SQL_MAP[]={{(0x4bb+7592-0x2262),
ZTE_WMS_CREATE_TABLE_SMS_SQL},{(0xf1+8661-0x22c5),ZTE_WMS_DEL_SIM_SQL},{
(0x1624+1343-0x1b62),ZTE_WMS_CREATE_CMD_STATUS_SQL},{(0xc4c+6623-0x262a),
ZTE_WMS_CREATE_SMS_REP_SQL},{(0xfad+5446-0x24f2),ZTE_WMS_CREATE_PAR_SQL},{
(0x587+2886-0x10cc),ZTE_WMS_CREATE_INFO_SQL},{(0xa0a+2061-0x1216),
ZTE_WMS_CREATE_CELL_BRO_SQL},{(0x1e68+263-0x1f6e),
ZTE_WMS_CREATE_SEND_CONTENT_SQL},};for(iMap=(0xd5+6471-0x1a1c);iMap<sizeof(
SQL_MAP)/sizeof(T_zUfiSms_SQLMap);iMap++){if(SQL_MAP[iMap].valid==
(0x1341+405-0x14d5)){if(zUfiSms_ExecSql(SQL_MAP[iMap].strSQL,NULL,NULL)!=
ZTE_WMS_DB_OK){return ZUFI_FAIL;}}}return ZUFI_SUCC;}int zUfiSms_CreateAllTable(
void){if(ZTE_WMS_DB_OK!=zUfiSms_OpenDb()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x61\x6e\x20\x6e\x6f\x74\x20\x6f\x70\x65\x6e\x20\x64\x61\x74\x61\x62\x61\x73\x65\x2e\x72\x65\x74\x75\x72\x6e\x2e" "\n"
);return ZTE_SMS_DB_ERROR_NOT_OPEN_DB;}return zUfiSms_CreateTables();}int 
zUfiSms_DropAllTable(void){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;if(
ZTE_WMS_DB_OK!=zUfiSms_OpenDb()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x61\x6e\x20\x6e\x6f\x74\x20\x6f\x70\x65\x6e\x20\x64\x61\x74\x61\x62\x61\x73\x65\x2e\x72\x65\x74\x75\x72\x6e\x2e" "\n"
);return ZTE_SMS_DB_ERROR_NOT_OPEN_DB;}result=zUfiSms_ExecSql(
ZTE_WMS_DROP_ALL_TABLE_SQL,NULL,NULL);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x72\x6f\x70\x41\x6c\x6c\x54\x61\x62\x6c\x65\x3a\x20\x73\x71\x6c\x20\x3d\x20\x25\x73\x2c\x20\x72\x65\x73\x75\x6c\x74\x20\x3d\x20\x25\x64\x21\x20" "\n"
,ZTE_WMS_DROP_ALL_TABLE_SQL,result);return ZUFI_SUCC;}int 
zUfiSms_DropAllTableExceptSms(void){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;if(
ZTE_WMS_DB_OK!=zUfiSms_OpenDb()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x61\x6e\x20\x6e\x6f\x74\x20\x6f\x70\x65\x6e\x20\x64\x61\x74\x61\x62\x61\x73\x65\x2e\x72\x65\x74\x75\x72\x6e\x2e" "\n"
);return ZTE_SMS_DB_ERROR_NOT_OPEN_DB;}result=zUfiSms_ExecSql(
ZTE_WMS_DROP_TABLE_EXCEPTSMS_SQL,NULL,NULL);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x72\x6f\x70\x41\x6c\x6c\x54\x61\x62\x6c\x65\x3a\x20\x73\x71\x6c\x20\x3d\x20\x25\x73\x2c\x20\x72\x65\x73\x75\x6c\x74\x20\x3d\x20\x25\x64\x21\x20" "\n"
,ZTE_WMS_DROP_ALL_TABLE_SQL,result);return ZUFI_SUCC;}int zUfiSms_IsDbEmpty(
const char*tab,int*pCount){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;char*strSQL=
sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20\x25\x73\x3b"
,tab);result=zUfiSms_ExecSql(strSQL,zUfiSms_GetFirstColumnInt,pCount);
sqlite3_free(strSQL);return isSucess(result);}int zUfiSms_InsertNormalSmsToDb(
T_zUfiSms_DbStoreData*ptDbSaveData,const char*pStorePos,char*pContent){char*
strSQL=NULL;T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;strSQL=sqlite3_mprintf(
"\x49\x4e\x53\x45\x52\x54\x20\x49\x4e\x54\x4f\x20\x73\x6d\x73\x28\x69\x6e\x64\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x2c\x54\x61\x67\x2c\x4e\x75\x6d\x62\x65\x72\x2c\x43\x63\x5f\x53\x6d\x73\x2c\x43\x63\x5f\x52\x65\x66\x2c\x43\x63\x5f\x54\x6f\x74\x61\x6c\x2c\x43\x63\x5f\x53\x65\x71\x2c\x54\x70\x5f\x44\x63\x73\x2c\x20"
"\x4d\x73\x67\x5f\x52\x65\x66\x2c\x43\x6f\x6e\x74\x65\x6e\x74\x2c\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x5f\x52\x65\x63\x69\x76\x65\x64\x2c\x44\x72\x61\x66\x74\x5f\x47\x72\x6f\x75\x70\x5f\x49\x64\x2c\x59\x65\x61\x72\x2c\x4d\x6f\x6e\x74\x68\x2c\x44\x61\x79\x2c\x48\x6f\x75\x72\x2c\x4d\x69\x6e\x75\x74\x65\x2c\x53\x65\x63\x6f\x6e\x64\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x2c\x4d\x6b\x74\x69\x6d\x65\x2c\x44\x69\x73\x70\x6c\x61\x79\x4d\x6f\x64\x65\x29\x20\x56\x41\x4c\x55\x45\x53\x28\x27\x25\x64\x27\x2c\x27\x25\x71\x27\x2c\x20"
"\x27\x25\x64\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x20"
"\x27\x25\x71\x27\x2c\x27\x25\x71\x27\x2c\x27\x25\x75\x27\x2c\x20\x27\x25\x64\x27\x29\x3b"
,ptDbSaveData->index,pStorePos,ptDbSaveData->tag,ptDbSaveData->number,
ptDbSaveData->concat_sms,ptDbSaveData->concat_info[(0x1a57+1128-0x1ebf)],
ptDbSaveData->concat_info[(0x1035+4476-0x21b0)],ptDbSaveData->concat_info[
(0x67c+1983-0xe39)],ptDbSaveData->tp_dcs,ptDbSaveData->msg_ref,pContent,"\x30",
ptDbSaveData->draft_group_id,ptDbSaveData->julian_date.year,ptDbSaveData->
julian_date.month,ptDbSaveData->julian_date.day,ptDbSaveData->julian_date.hour,
ptDbSaveData->julian_date.min,ptDbSaveData->julian_date.sec,ptDbSaveData->
julian_date.timezone,(unsigned int)zte_getsecond(ptDbSaveData->julian_date),
ptDbSaveData->msg_displaymode);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x49\x6e\x73\x65\x72\x74\x4e\x6f\x72\x6d\x61\x6c\x53\x6d\x73\x54\x6f\x44\x62\x20\x73\x71\x6c\x3d\x25\x73" "\n"
,strSQL);result=zUfiSms_ExecSql(strSQL,NULL,NULL);sqlite3_free(strSQL);return 
isSucess(result);}int zUfiSms_UpdateNormalSmsToDb(T_zUfiSms_DbStoreData*
ptDbSaveData,const char*pStorePos,char*pContent,long iSmsId){char*strSQL=NULL;
T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;strSQL=sqlite3_mprintf(
"\x55\x50\x44\x41\x54\x45\x20\x73\x6d\x73\x20\x53\x45\x54\x20\x4e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x2c\x43\x6f\x6e\x74\x65\x6e\x74\x3d\x27\x25\x71\x27\x2c\x59\x65\x61\x72\x3d\x27\x25\x71\x27\x2c\x4d\x6f\x6e\x74\x68\x3d\x27\x25\x71\x27\x2c\x20"
"\x44\x61\x79\x3d\x27\x25\x71\x27\x2c\x48\x6f\x75\x72\x3d\x27\x25\x71\x27\x2c\x4d\x69\x6e\x75\x74\x65\x3d\x27\x25\x71\x27\x2c\x53\x65\x63\x6f\x6e\x64\x3d\x27\x25\x71\x27\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x3d\x27\x25\x71\x27\x20"
"\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x20\x61\x6e\x64\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x71\x27\x3b"
,ptDbSaveData->number,pContent,ptDbSaveData->julian_date.year,ptDbSaveData->
julian_date.month,ptDbSaveData->julian_date.day,ptDbSaveData->julian_date.hour,
ptDbSaveData->julian_date.min,ptDbSaveData->julian_date.sec,ptDbSaveData->
julian_date.timezone,iSmsId,pStorePos);if(NULL==strSQL){return ZUFI_FAIL;}printf
(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x55\x70\x64\x61\x74\x65\x4e\x6f\x72\x6d\x61\x6c\x53\x6d\x73\x54\x6f\x44\x62\x20\x73\x71\x6c\x3d\x25\x73" "\n"
,strSQL);result=zUfiSms_ExecSql(strSQL,NULL,NULL);sqlite3_free(strSQL);return 
isSucess(result);}int zUfiSms_GetFirstColumnStr(void*fvarg,int columns,char**
zresult,char**lname){if(columns>=(0x1578+4255-0x2616)&&fvarg!=NULL){if(zresult[
(0x1cd+2117-0xa12)]!=NULL){T_zUfiSms_BufInfo*para=(T_zUfiSms_BufInfo*)fvarg;
strncpy(para->buf_addr,zresult[(0x1975+3374-0x26a3)],para->buf_len-
(0x1bf5+996-0x1fd8));return SQLITE_OK;}}return SQLITE_ERROR;}int 
zUfiSms_GetStorePosById(char*item,char*item_data,int item_len,int id){
T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;T_zUfiSms_BufInfo buf_info={
(0x7c7+1092-0xc0b)};char*strSQL=NULL;if(NULL==item||NULL==item_data){return 
ZUFI_FAIL;}buf_info.buf_addr=item_data;buf_info.buf_len=item_len;strSQL=
sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x25\x71\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,item,id);result=zUfiSms_ExecSql(strSQL,zUfiSms_GetFirstColumnStr,&buf_info);
sqlite3_free(strSQL);if((ZTE_WMS_DB_OK!=result)||((0x18d0+3013-0x2495)==strcmp(
item_data,""))){at_print(LOG_ERR,
"\x67\x65\x74\x20\x74\x61\x62\x6c\x65\x5f\x6d\x65\x6d\x62\x65\x72\x20\x62\x79\x20\x69\x64\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);return ZUFI_FAIL;}return ZUFI_SUCC;}int zUfiSms_DeleteSmsInDb(void){char sql[
(0x1058+5872-0x26c8)]={(0x1168+2645-0x1bbd)};snprintf(sql,sizeof(sql),
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x61\x6e\x64\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SIM_TABLE,g_zUfiSms_DelMsg.sim_id[g_zUfiSms_DelMsg.sim_index-
(0x11c+6726-0x1b61)]);return isSucess(zUfiSms_ExecSql(sql,NULL,NULL));}int 
zUfiSms_DeleteAllSimSmsInDb(void){return isSucess(zUfiSms_ExecSql(
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27"
 ZTE_WMS_DB_SIM_TABLE"\x27\x3b",NULL,NULL));}int zUfiSms_DeleteNvSms(void){if(
g_zUfiSms_StoreCapablity[ZTE_WMS_MEMORY_NV]==g_zUfiSms_DelMsg.nv_count){if(
ZTE_WMS_DB_OK!=zUfiSms_ExecSql(
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27"
 ZTE_WMS_DB_NV_TABLE"\x27\x3b",NULL,NULL)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x4e\x76\x53\x6d\x73\x3a\x64\x65\x6c\x65\x74\x65\x20\x66\x61\x69\x6c" "\n"
);return WMS_CMD_FAILED;}}else{int i=(0x15a0+3828-0x2494);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x4e\x76\x53\x6d\x73\x3a\x64\x65\x6c\x65\x74\x65\x20\x6e\x76\x5f\x63\x6f\x75\x6e\x74\x3d\x25\x64" "\n"
,g_zUfiSms_DelMsg.nv_count);for(i=(0x325+1324-0x851);i<g_zUfiSms_DelMsg.nv_count
;i++){char sql[(0x11af+770-0x1431)]={(0xafb+4185-0x1b54)};snprintf(sql,sizeof(
sql),
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x61\x6e\x64\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_NV_TABLE,g_zUfiSms_DelMsg.nv_id[i]);if(ZTE_WMS_DB_OK!=
zUfiSms_ExecSql(sql,NULL,NULL)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x65\x78\x65\x63\x20\x73\x71\x6c\x20\x66\x61\x69\x6c\x65\x64\x2c\x69\x3d\x25\x64" "\n"
,i);return WMS_CMD_FAILED;}}}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x4e\x76\x53\x6d\x73\x3a\x64\x65\x6c\x65\x74\x65\x20\x73\x75\x63\x63\x65\x73\x73" "\n"
);return WMS_CMD_SUCCESS;}int zUfiSms_GetSmsIndex(int id,T_zUfiSms_ModifyTag*
ptModifyTag,int is_cc){char sql[(0x111+8164-0x2075)]={(0x2d1+7200-0x1ef1)};
T_zUfiSms_BufInfo buf_info={(0x2d4+1403-0x84f)};char str_index[
(0x80d+6838-0x22c1)*WMS_MESSAGE_LIST_MAX]={(0xc0b+799-0xf2a)};buf_info.buf_addr=
str_index;buf_info.buf_len=sizeof(str_index);snprintf(sql,sizeof(sql),
"\x53\x45\x4c\x45\x43\x54\x20\x69\x6e\x64\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,id);if(zUfiSms_ExecSql(sql,zUfiSms_GetFirstColumnStr,&buf_info)!=ZTE_WMS_DB_OK)
{at_print(LOG_ERR,
"\x6f\x70\x65\x6e\x20\x74\x61\x62\x6c\x65\x20\x73\x6d\x73\x20\x66\x61\x69\x6c\x65\x64"
);return ZUFI_FAIL;}if((0xa48+4054-0x1a1d)==is_cc){int i=(0x10a+4382-0x1228);int
 j=(0xd96+219-0xe71);int count=(0x493+3136-0x10d3);char**out_result=NULL;count=
zUfiSms_SplitString(str_index,&out_result,((char)(0x16bf+156-0x1720)));for(i=
(0x16af+201-0x1778);i<count;i++){if((0xc10+1572-0x1234)!=strcmp(out_result[i],""
)){ptModifyTag->indices[j++]=atoi(out_result[i]);}}ptModifyTag->num_of_indices=j
;free(out_result);}else{ptModifyTag->indices[(0x13e2+2238-0x1ca0)]=atoi(
str_index);ptModifyTag->num_of_indices=(0x1f0+5997-0x195c);}ptModifyTag->
total_indices=ptModifyTag->num_of_indices;printf(
"\x6d\x6f\x64\x69\x66\x79\x5f\x74\x61\x67\x5f\x70\x74\x72\x2d\x3e\x74\x6f\x74\x61\x6c\x5f\x69\x6e\x64\x69\x63\x65\x73\x3d\x25\x64" "\n"
,ptModifyTag->total_indices);return ZUFI_SUCC;}int zUfiSms_IsConcatSms(int id){
char sql[(0xc20+1690-0x123a)]={(0xca+7321-0x1d63)};int is_cc=(0xfaf+403-0x1142);
T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;snprintf(sql,sizeof(sql),
"\x53\x45\x4c\x45\x43\x54\x20\x43\x63\x5f\x53\x6d\x73\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,id);result=zUfiSms_ExecSql(sql,zUfiSms_GetFirstColumnInt,&
is_cc);if(ZTE_WMS_DB_OK!=result){at_print(LOG_ERR,
"\x6f\x70\x65\x6e\x20\x74\x61\x62\x6c\x65\x20\x25\x73\x20\x66\x61\x69\x6c\x65\x64"
,ZTE_WMS_DB_SMS_TABLE);return-(0xe8b+1772-0x1576);}return is_cc;}int 
zUfiSms_UpdateSmsTagInDb(unsigned long id,unsigned int tags){char sql[
(0x60b+1128-0x9f3)]={(0xea+8152-0x20c2)};snprintf(sql,sizeof(sql),
"\x55\x50\x44\x41\x54\x45\x20\x25\x73\x20\x53\x45\x54\x20\x54\x61\x67\x3d\x27\x25\x64\x27\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,tags,id);return isSucess(zUfiSms_ExecSql(sql,NULL,NULL));}
int zUfiSms_GetTagCountInDb(T_zUfiSms_MemoryType mem_store,unsigned int tags,int
*pTotalCount){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;char sql[
(0x189+9414-0x25cf)]={(0x823+2887-0x136a)};if(pTotalCount==NULL){return 
ZUFI_FAIL;}if(mem_store==ZTE_WMS_MEMORY_MAX){snprintf(sql,sizeof(sql),
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x54\x61\x67\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,tags);}else if(mem_store==ZTE_WMS_MEMORY_NV){snprintf(sql,
sizeof(sql),
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x41\x4e\x44\x20\x54\x61\x67\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,ZTE_WMS_DB_NV_TABLE,tags);}else{snprintf(sql,sizeof(sql),
"\x53\x45\x4c\x45\x43\x54\x20\x63\x6f\x75\x6e\x74\x28\x2a\x29\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x41\x4e\x44\x20\x54\x61\x67\x3d\x27\x25\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,ZTE_WMS_DB_SIM_TABLE,tags);}result=zUfiSms_ExecSql(sql,(
zte_wms_db_callback)zUfiSms_GetFirstColumnInt,pTotalCount);return isSucess(
result);}int zUfiSms_DeleteDraftSms(long iSmsId){char sql[(0x567+2335-0xe06)]={
(0xa13+6690-0x2435)};snprintf(sql,sizeof(sql),
"\x44\x45\x4c\x45\x54\x45\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x27\x25\x6c\x64\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,iSmsId);return isSucess(zUfiSms_ExecSql(sql,NULL,NULL));}
int zUfiSms_SetConcatMaxRefer(int ref){char sql[(0x1d70+1444-0x2294)]={
(0x1e61+1667-0x24e4)};if(ZTE_WMS_MAX_CONCAT_REF<ref){ref=(0xa29+6161-0x223a);}
snprintf(sql,sizeof(sql),
"\x55\x50\x44\x41\x54\x45\x20\x25\x73\x20\x53\x45\x54\x20\x4d\x61\x78\x5f\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x31\x3b"
,ZTE_WMS_DB_PARAMETER_TABLE,ref);return isSucess(zUfiSms_ExecSql(sql,NULL,NULL))
;}int zUfiSms_SetMaxReference(int ref){char sql[(0x11ab+3319-0x1e22)]={
(0xc4+4427-0x120f)};if(ZTE_WMS_MAX_SMS_REF<ref){ref=(0x56d+7393-0x224e);}
snprintf(sql,sizeof(sql),
"\x55\x50\x44\x41\x54\x45\x20\x25\x73\x20\x53\x45\x54\x20\x4d\x61\x78\x5f\x53\x6d\x73\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x57\x48\x45\x52\x45\x20\x69\x64\x3d\x31\x3b"
,ZTE_WMS_DB_PARAMETER_TABLE,ref);return isSucess(zUfiSms_ExecSql(sql,NULL,NULL))
;}int zUfiSms_InsertReportStatusToDb(unsigned char*pNumber,T_zUfiSms_Date*
ptSmsDate,int iReportStatus){char*strSQL=NULL;T_zUfiSms_DbResult result=
ZTE_WMS_DB_OK;strSQL=sqlite3_mprintf(
"\x49\x4e\x53\x45\x52\x54\x20\x49\x4e\x54\x4f\x20\x73\x6d\x73\x5f\x72\x65\x70\x6f\x72\x74\x28\x61\x64\x64\x72\x65\x73\x73\x2c\x63\x6f\x6e\x74\x65\x6e\x74\x2c\x59\x65\x61\x72\x2c\x4d\x6f\x6e\x74\x68\x2c\x44\x61\x79\x2c\x48\x6f\x75\x72\x2c\x4d\x69\x6e\x75\x74\x65\x2c\x53\x65\x63\x6f\x6e\x64\x2c\x54\x69\x6d\x65\x5a\x6f\x6e\x65\x29\x20"
"\x56\x41\x4c\x55\x45\x53\x28\x27\x25\x73\x27\x2c\x27\x25\x64\x27\x2c\x27\x25\x73\x27\x2c\x27\x25\x73\x27\x2c\x27\x25\x73\x27\x2c\x27\x25\x73\x27\x2c\x20\x27\x25\x73\x27\x2c\x27\x25\x73\x27\x2c\x27\x25\x73\x27\x29\x3b"
,pNumber,iReportStatus,ptSmsDate->year,ptSmsDate->month,ptSmsDate->day,ptSmsDate
->hour,ptSmsDate->min,ptSmsDate->sec,ptSmsDate->timezone);result=zUfiSms_ExecSql
(strSQL,NULL,NULL);sqlite3_free(strSQL);return isSucess(result);}int 
zUfiSms_GetFirstColumnParaInfo(void*fvarg,int columns,char**zresult,char**lname)
{if(columns>=(0x723+296-0x84a)&&fvarg!=NULL){if(zresult[(0xcd5+506-0xecf)]!=NULL
){T_zUfiSms_ParaInfo*para=(T_zUfiSms_ParaInfo*)fvarg;strncpy(para->sca,zresult[
(0x1ee0+1145-0x2359)],sizeof(para->sca)-(0x6f5+5509-0x1c79));para->mem_store=
atoi(zresult[(0x1d4c+1860-0x248f)]);para->tp_validity_period=atoi(zresult[
(0x1a80+2118-0x22c4)]);para->status_report_on=atoi(zresult[(0x7a5+2183-0x1029)])
;para->sendfail_retry_on=atoi(zresult[(0x1095+1524-0x1685)]);para->
outdate_delete_on=atoi(zresult[(0x2a6+5978-0x19fb)]);(void)strncpy(para->
default_store,zresult[(0x10d1+1658-0x1745)],sizeof(para->default_store)-
(0x122f+2478-0x1bdc));return SQLITE_OK;}}return SQLITE_ERROR;}int 
zUfiSms_GetDbParameters(void){char*strSQL=
"\x53\x45\x4c\x45\x43\x54\x20\x53\x6d\x73\x5f\x53\x63\x61\x2c\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x2c\x54\x70\x5f\x56\x61\x6c\x69\x64\x69\x74\x79\x2c\x53\x6d\x73\x5f\x52\x65\x70\x6f\x72\x74\x2c\x53\x65\x6e\x64\x5f\x52\x65\x74\x72\x79\x2c\x4f\x75\x74\x64\x61\x74\x65\x5f\x44\x65\x6c\x65\x74\x65\x2c\x44\x65\x66\x61\x75\x6c\x74\x5f\x53\x74\x6f\x72\x65\x20\x46\x52\x4f\x4d\x20"
 ZTE_WMS_DB_PARAMETER_TABLE"\x3b";memset(&g_zUfiSms_CurSmsPara,
(0x156f+4155-0x25aa),sizeof(T_zUfiSms_ParaInfo));return isSucess(zUfiSms_ExecSql
(strSQL,zUfiSms_GetFirstColumnParaInfo,&g_zUfiSms_CurSmsPara));}int 
zUfiSms_GetSendContent(void*fvarg,int column,char**zresult,char**lname){
T_zUfiSms_BufInfo*para=(T_zUfiSms_BufInfo*)fvarg;if(column>=(0x9bc+6628-0x239f)
&&para!=NULL)(void)strncpy(para->buf_addr,zresult[(0x21d1+84-0x2225)],para->
buf_len-(0x1e2c+897-0x21ac));return(0xb4+756-0x3a8);}int zUfiSms_GetSmsContent(
char*pSmsBuf,int len){T_zUfiSms_BufInfo buf_info={(0xa5a+6873-0x2533)};char*
strSQL=
"\x53\x45\x4c\x45\x43\x54\x20\x6d\x73\x67\x5f\x63\x6f\x6e\x74\x65\x6e\x74\x20\x46\x52\x4f\x4d\x20"
 ZTE_WMS_DB_SEND_CONTENT_TABLE"\x3b";buf_info.buf_addr=pSmsBuf;buf_info.buf_len=
len;memset(buf_info.buf_addr,(0x1127+444-0x12e3),len);return isSucess(
zUfiSms_ExecSql(strSQL,zUfiSms_GetSendContent,&buf_info));}int 
zUfiSms_SearchConcatSmsInDb(T_zUfiSms_DbStoreData*ptDbSaveData,char*pMemStore){
char*sql=NULL;sqlite3_stmt*stmt=NULL;int id=-(0x577+3174-0x11dc);switch(
ptDbSaveData->tag){case WMS_TAG_TYPE_MO_SENT_V01:case 
WMS_TAG_TYPE_MO_NOT_SENT_V01:{sql=sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x43\x63\x5f\x53\x65\x71\x2c\x69\x64\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x54\x6f\x74\x61\x6c\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x41\x4e\x44\x20\x28\x54\x61\x67\x20\x3d\x20\x27\x25\x64\x27\x20\x4f\x52\x20\x54\x61\x67\x3d\x20\x27\x25\x64\x27\x29\x3b"
,ptDbSaveData->number,ptDbSaveData->concat_info[(0x1106+4188-0x2162)],
ptDbSaveData->concat_info[(0x1703+1863-0x1e49)],pMemStore,
WMS_TAG_TYPE_MO_SENT_V01,WMS_TAG_TYPE_MO_NOT_SENT_V01);break;}case
(0xfd6+1897-0x173b):{sql=sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x43\x63\x5f\x53\x65\x71\x2c\x69\x64\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x54\x6f\x74\x61\x6c\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x41\x4e\x44\x20\x54\x61\x67\x20\x3d\x20\x27\x25\x64\x27\x3b"
,ptDbSaveData->number,ptDbSaveData->concat_info[(0x1c93+872-0x1ffb)],
ptDbSaveData->concat_info[(0xa74+1793-0x1174)],pMemStore,(0xc48+4654-0x1e72));
break;}case WMS_TAG_TYPE_MT_READ_V01:case WMS_TAG_TYPE_MT_NOT_READ_V01:{sql=
sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x43\x63\x5f\x53\x65\x71\x2c\x69\x64\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x54\x6f\x74\x61\x6c\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x20\x41\x4e\x44\x20\x28\x54\x61\x67\x20\x3d\x20\x27\x25\x64\x27\x20\x4f\x52\x20\x54\x61\x67\x3d\x20\x27\x25\x64\x27\x29\x3b"
,ptDbSaveData->number,ptDbSaveData->concat_info[(0x17d2+2541-0x21bf)],
ptDbSaveData->concat_info[(0xcd9+4409-0x1e11)],pMemStore,
WMS_TAG_TYPE_MT_READ_V01,WMS_TAG_TYPE_MT_NOT_READ_V01);break;}default:{sql=
sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x43\x63\x5f\x53\x65\x71\x2c\x69\x64\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x4e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x54\x6f\x74\x61\x6c\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x3b"
,ptDbSaveData->number,ptDbSaveData->concat_info[(0xea2+2684-0x191e)],
ptDbSaveData->concat_info[(0x15da+3330-0x22db)],pMemStore);at_print(LOG_ERR,
"\x74\x61\x67\x20\x25\x64\x20\x69\x73\x20\x75\x6e\x6b\x6e\x6f\x77\x6e",
ptDbSaveData->tag);break;}}printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x3a\x25\x73" "\n",sql);if(
sqlite3_prepare(g_zUfiSms_DbPointer,sql,-(0x798+1302-0xcad),&stmt,
(0x2c7+8741-0x24ec))!=SQLITE_OK){at_print(LOG_ERR,
"\x63\x61\x6e\x20\x6e\x6f\x74\x20\x65\x78\x65\x63\x20\x73\x71\x6c\x2c\x73\x71\x6c\x69\x74\x65\x33\x5f\x65\x72\x72\x6d\x73\x67\x3a\x25\x73\x2e" "\n"
,sqlite3_errmsg(g_zUfiSms_DbPointer));sqlite3_free(sql);return-
(0x1475+1977-0x1c2d);}while(SQLITE_ROW==sqlite3_step(stmt)){int j=
(0x9b0+4264-0x1a58);char**out_result=NULL;char*column_text=sqlite3_column_text(
stmt,(0x3a+8725-0x224f));int count=-(0x678+1941-0xe0c);if(column_text!=NULL)
count=zUfiSms_SplitString(column_text,&out_result,((char)(0xce7+4943-0x1ffb)));
for(j=(0x1c4f+1076-0x2083);j<count;j++){if((0x1bdf+820-0x1f13)==strcmp(
out_result[j],"")){if(j+(0xa92+1622-0x10e7)==ptDbSaveData->concat_info[
(0x33c+7468-0x2066)]){id=sqlite3_column_int(stmt,(0xd32+1673-0x13ba));break;}}}
if(out_result!=NULL){free(out_result);out_result=NULL;}if(-(0xe8f+3151-0x1add)!=
id){break;}}(void)sqlite3_finalize(stmt);sqlite3_free(sql);return id;}int 
zUfiSms_CheckDbOutdateSms_Callback(void*fvarg,int columns,char**zresult,char**
lname){if(fvarg!=NULL&&columns>=(0x2eb+7195-0x1f05)){if(zresult[
(0x1977+2318-0x2285)]!=NULL){T_zUfiSms_DelReq*result=(T_zUfiSms_DelReq*)fvarg;
result->id[result->all_or_count]=atoi(zresult[(0x2128+889-0x24a1)]);result->
all_or_count++;return SQLITE_OK;}}return SQLITE_ERROR;}VOID 
zUfiSms_CheckDbOutdateSms(const char*pDbTable,T_zUfiSms_DelReq*pSmsDel){char 
acSql[(0x1241+4540-0x237d)]={(0x1a9d+283-0x1bb8)};struct timeval tp;if(
(0x1c9+8388-0x228d)!=gettimeofday(&tp,NULL)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x67\x65\x74\x74\x69\x6d\x65\x6f\x66\x64\x61\x79\x20\x65\x72\x72\x6f\x72\x21\x21\x21"
);return;}if(tp.tv_sec<=OUTDATEINTERVAL){return;}snprintf(acSql,sizeof(acSql),
"\x53\x45\x4c\x45\x43\x54\x20\x69\x64\x20\x46\x52\x4f\x4d\x20\x25\x73\x20\x57\x48\x45\x52\x45\x20\x4d\x6b\x74\x69\x6d\x65\x3c\x27\x25\x75\x27\x20\x41\x4e\x44\x20\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65\x3d\x27\x25\x73\x27\x3b"
,ZTE_WMS_DB_SMS_TABLE,(unsigned int)(tp.tv_sec-OUTDATEINTERVAL),pDbTable);if(
ZTE_WMS_DB_OK!=zUfiSms_ExecSql(acSql,zUfiSms_CheckDbOutdateSms_Callback,pSmsDel)
){at_print(LOG_ERR,
"\x6f\x70\x65\x6e\x20\x74\x61\x62\x6c\x65\x20\x25\x73\x20\x66\x61\x69\x6c\x65\x64"
,ZTE_WMS_DB_CMD_STATUS_TABLE);}return;}int 
zUfiSms_GetCurrentRecvTotalSeq_Callback(void*fvarg,int column,char**zresult,char
**lname){if(fvarg!=NULL&&column>=(0x1faf+440-0x2165)){if(zresult[
(0x1cb+3380-0xeff)]!=NULL&&zresult[(0x327+6940-0x1e42)]!=NULL){SMS_MSG_INFO*msg=
(SMS_MSG_INFO*)fvarg;memset(msg->id,(0x79a+116-0x80e),sizeof(msg->id));memset(
msg->total_seq,(0x18d9+108-0x1945),sizeof(msg->total_seq));strncpy(msg->id,
zresult[(0x4b3+4652-0x16df)],sizeof(msg->id)-(0x808+1541-0xe0c));strncpy(msg->
total_seq,zresult[(0xff5+2545-0x19e5)],sizeof(msg->total_seq)-
(0x286+5119-0x1684));printf(
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x43\x75\x72\x72\x65\x6e\x74\x52\x65\x63\x76\x54\x6f\x74\x61\x6c\x53\x65\x71\x5f\x43\x61\x6c\x6c\x62\x61\x63\x6b\x20\x69\x64\x20\x3d\x20\x25\x73\x2c\x20\x74\x6f\x74\x61\x6c\x5f\x73\x65\x71\x20\x3d\x20\x25\x73" "\n"
,msg->id,msg->total_seq);return SQLITE_OK;}}return SQLITE_ERROR;}int 
zUfiSms_GetCurrentRecvTotalSeq(T_zUfiSms_DbStoreData*ptDbSaveData,SMS_MSG_INFO*
pmsg){T_zUfiSms_DbResult result=ZTE_WMS_DB_OK;char*strSQL=NULL;strSQL=
sqlite3_mprintf(
"\x53\x45\x4c\x45\x43\x54\x20\x69\x64\x2c\x20\x43\x63\x5f\x4e\x75\x6d\x20\x46\x52\x4f\x4d\x20\x73\x6d\x73\x20\x57\x48\x45\x52\x45\x20\x20\x6e\x75\x6d\x62\x65\x72\x3d\x27\x25\x71\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x52\x65\x66\x3d\x27\x25\x64\x27\x20\x41\x4e\x44\x20\x43\x63\x5f\x54\x6f\x74\x61\x6c\x3d\x27\x25\x64\x27\x3b"
,ptDbSaveData->number,ptDbSaveData->concat_info[(0x1a74+1613-0x20c1)],
ptDbSaveData->concat_info[(0x1095+4722-0x2306)]);printf(
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x43\x75\x72\x72\x65\x6e\x74\x52\x65\x63\x76\x54\x6f\x74\x61\x6c\x53\x65\x71\x20\x73\x71\x6c\x20\x3a\x20\x25\x73\x20" "\n"
,strSQL);result=zUfiSms_ExecSql(strSQL,zUfiSms_GetCurrentRecvTotalSeq_Callback,
pmsg);sqlite3_free(strSQL);return isSucess(result);}
