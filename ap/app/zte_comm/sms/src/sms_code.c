
#include <time.h>
#include <ctype.h>
#include "os_type_def.h"
#include "sms_code.h"
#ifdef WIN32
#include <wchar.h>
#endif
#define     NON_GSM                         (0x6e1+3779-0x1584)
#define     NON_GSM_P                       NON_GSM
extern T_zUfiSms_ConcatInfo g_zUfiSms_ConcatSms;static const wms_udh_s_type*
const_header;char g_zUfiSms_DigAscMap[(0x14d2+2346-0x1dec)]={
((char)(0x65a+74-0x674)),((char)(0x148d+1333-0x1991)),
((char)(0x1619+2308-0x1eeb)),((char)(0x2d1+4207-0x130d)),
((char)(0x17b2+2449-0x210f)),((char)(0x128c+4365-0x2364)),
((char)(0x1169+5006-0x24c1)),((char)(0x137+8303-0x216f)),
((char)(0x6ba+6560-0x2022)),((char)(0x8d8+403-0xa32)),
((char)(0x1383+4587-0x252d)),((char)(0x8ec+302-0x9d8)),
((char)(0x206+7721-0x1fec)),((char)(0x1f43+1642-0x2569)),
((char)(0xfa7+3669-0x1db7)),((char)(0x155d+3301-0x21fc))};extern SMS_PARAM 
g_zUfiSms_SendingSms;extern UINT16 g_zUfiSms_IsLanguageShift;extern int 
g_zUfiSms_Language;static int SerializeNumbers_sms(const char*pSrc,char*pDst,int
 nSrcLength);const unsigned short g_zUfiSms_AsciiToGsmdefaultTable[]={
((char)(0x1a1f+2770-0x24d1)),((char)(0xee0+3632-0x1cf0)),
((char)(0xa40+4737-0x1ca1)),((char)(0x32d+3839-0x120c)),
((char)(0x9b6+4680-0x1bde)),((char)(0x1bf0+2033-0x23c1)),
((char)(0x52f+6492-0x1e6b)),((char)(0x289+6287-0x1af8)),
((char)(0x45+8435-0x2118)),((char)(0x8d4+7646-0x2692)),
((char)(0x2a+6832-0x1aba)),((char)(0xedb+5854-0x2599)),
((char)(0x1053+282-0x114d)),((char)(0x3b9+1355-0x8e4)),
((char)(0x549+6046-0x1cc7)),((char)(0x7d2+1653-0xe27)),
((char)(0x10ef+4994-0x2451)),((char)(0x9a4+1442-0xf26)),
((char)(0x2b1+8889-0x254a)),((char)(0x8ac+3722-0x1716)),
((char)(0x678+6072-0x1e10)),((char)(0x1403+1544-0x19eb)),
((char)(0x489+621-0x6d6)),((char)(0xac0+6248-0x2308)),((char)(0x1c0+3050-0xd8a))
,((char)(0x9aa+7060-0x251e)),((char)(0xe72+2775-0x1929)),
((char)(0xdbc+3183-0x1a0b)),((char)(0x338+5075-0x16eb)),
((char)(0x3e9+659-0x65c)),((char)(0x2b8+8638-0x2456)),
((char)(0x6cf+5669-0x1cd4)),((char)(0x229f+631-0x24f6)),
((char)(0x6c7+4929-0x19e7)),(0xcc9+3174-0x190d),((char)(0x16e3+2341-0x1fe5)),
(0x2a7+4575-0x1484),((char)(0x1599+3496-0x231c)),((char)(0x3a9+142-0x411)),
(0x6fb+1708-0xd80),((char)(0x10fc+4708-0x2338)),((char)(0xed9+3709-0x1d2d)),
((char)(0x49b+7278-0x20df)),((char)(0x49c+5782-0x1b07)),
((char)(0xf49+5174-0x2353)),((char)(0x1c57+863-0x1f89)),
((char)(0x1d46+145-0x1da9)),((char)(0x806+2680-0x124f)),
((char)(0x1c10+1858-0x2322)),((char)(0xed+4099-0x10bf)),
((char)(0x1303+4673-0x2512)),((char)(0x1b1d+1944-0x2282)),
((char)(0x4d4+57-0x4d9)),((char)(0x1ea4+1527-0x2466)),((char)(0x435+2266-0xcd9))
,((char)(0x18a1+3429-0x25cf)),((char)(0x17f7+931-0x1b62)),
((char)(0x561+8427-0x2613)),((char)(0x395+8063-0x22da)),
((char)(0x397+7712-0x217c)),((char)(0x1a9c+373-0x1bd5)),
((char)(0x7c7+3769-0x1643)),((char)(0xc88+3604-0x1a5e)),
((char)(0x1be0+1582-0x21cf)),(0xd90+2900-0x18e4),((char)(0x1415+1912-0x1b4c)),
((char)(0x724+411-0x87d)),((char)(0x1840+550-0x1a23)),
((char)(0x1e04+2194-0x2652)),((char)(0x213+5786-0x1868)),
((char)(0x1656+3486-0x23ae)),((char)(0x227d+39-0x225d)),
((char)(0xee5+4556-0x2069)),((char)(0xc7f+6779-0x26b1)),((char)(0x22a+28-0x1fc))
,((char)(0x1751+3769-0x25bf)),((char)(0x133c+4988-0x266c)),
((char)(0x66c+3533-0x13ec)),((char)(0x14d3+1175-0x191c)),
((char)(0x4fc+2877-0xfea)),((char)(0x365+6669-0x1d22)),
((char)(0x11a7+5342-0x2634)),((char)(0x449+8199-0x23fe)),
((char)(0xa77+4285-0x1ae1)),((char)(0x8af+7529-0x25c4)),
((char)(0xa41+6448-0x231c)),((char)(0xe97+4266-0x1eeb)),
((char)(0xc5+3017-0xc37)),((char)(0xf40+16-0xef8)),((char)(0x5fb+3402-0x12ec)),
((char)(0xf33+1746-0x15ab)),6972,6959,6974,6932,(0x15d1+698-0x187a),
((char)(0x9d7+5306-0x1e71)),((char)(0x242+6751-0x1c40)),
((char)(0x16e1+2447-0x200e)),((char)(0x12ac+2497-0x1c0a)),
((char)(0x1a9+2578-0xb57)),((char)(0xc25+4639-0x1ddf)),
((char)(0x1abd+537-0x1c70)),((char)(0x20e9+576-0x22c2)),
((char)(0x9e0+5207-0x1dcf)),((char)(0x11d6+3877-0x2092)),
((char)(0x14b3+1444-0x19ed)),((char)(0xd09+3596-0x1aaa)),
((char)(0x314+5911-0x19bf)),((char)(0x2009+1850-0x26d6)),
((char)(0xb36+2356-0x13fc)),((char)(0xe82+5383-0x231a)),
((char)(0x1771+518-0x1907)),((char)(0x754+2306-0xfe5)),
((char)(0xe2b+6357-0x268e)),((char)(0x9c1+1088-0xd8e)),
((char)(0x16af+3039-0x221a)),((char)(0xf4b+5424-0x2406)),
((char)(0x85b+5587-0x1db8)),((char)(0x9c8+2512-0x1321)),
((char)(0x868+6983-0x2337)),((char)(0x1e6+7404-0x1e59)),
((char)(0x1ed1+1167-0x22e6)),6952,6976,6953,(0x2494+6744-0x23af),
((char)(0x559+1391-0xaa8)),((char)(0x18b3+1537-0x1e94)),
((char)(0x1419+4495-0x2588)),((char)(0x803+3540-0x15b7)),
((char)(0xcf+9769-0x26d8)),((char)(0x43a+6305-0x1cbb)),
((char)(0x18a4+528-0x1a94)),((char)(0x15ad+1024-0x198d)),
((char)(0x2065+357-0x21aa)),((char)(0xc8f+1090-0x10b1)),
((char)(0x1a5f+1998-0x220d)),((char)(0x8d1+4920-0x1be9)),
((char)(0x909+6244-0x214d)),((char)(0x1b6+675-0x439)),
((char)(0x311+8621-0x249e)),((char)(0x222c+1259-0x26f7)),
((char)(0xfdf+2453-0x1954)),((char)(0xf65+6028-0x26d1)),
((char)(0x664+529-0x855)),((char)(0x1a36+1686-0x20ac)),
((char)(0x1006+2248-0x18ae)),((char)(0x6b7+7451-0x23b2)),
((char)(0xc57+2134-0x148d)),((char)(0x525+5187-0x1948)),
((char)(0x7b1+3155-0x13e4)),((char)(0x1541+1516-0x1b0d)),
((char)(0x1f51+458-0x20fb)),((char)(0x7b9+5985-0x1efa)),
((char)(0x12e7+269-0x13d4)),((char)(0x15bd+960-0x195d)),
((char)(0xb3d+4293-0x1be2)),((char)(0x517+5480-0x1a5f)),
((char)(0xb03+2780-0x15bf)),((char)(0xf37+4739-0x219a)),(0x699+1305-0xb72),
((char)(0x3f0+5954-0x1b12)),(0x4dd+907-0x867),(0x964+6727-0x2387),
(0x871+3105-0x148f),((char)(0x3ad+454-0x553)),(0x8ea+3293-0x1568),
((char)(0x4a5+7389-0x2162)),((char)(0x75c+3407-0x148b)),
((char)(0x204b+1415-0x25b2)),((char)(0x6d+6344-0x1915)),
((char)(0x115+8631-0x22ac)),((char)(0x2c+3462-0xd92)),
((char)(0x285+7960-0x217d)),((char)(0x1777+1097-0x1ba0)),
((char)(0x15c1+3793-0x2472)),((char)(0x37d+2443-0xce8)),
((char)(0x113f+3244-0x1dcb)),((char)(0x1e32+1131-0x227d)),
((char)(0x1e5c+688-0x20ec)),((char)(0x153d+1505-0x1afe)),
((char)(0xa2b+3252-0x16bf)),((char)(0x6a0+3371-0x13ab)),(0xad1+2361-0x13ff),
((char)(0x65a+6536-0x1fc2)),((char)(0x9fd+3823-0x18cc)),
((char)(0xa27+2719-0x14a6)),((char)(0x6b9+6996-0x21ed)),((char)(0x75+732-0x331))
,((char)(0x20b4+1532-0x2690)),(0x1fb3+1382-0x24b9),((char)(0x8d5+1707-0xf60)),
((char)(0x8bf+1395-0xe12)),((char)(0x6c7+3638-0x14dd)),
((char)(0x1e7+8712-0x23cf)),(0x228+8122-0x2187),(0xb18+6706-0x253c),
(0x1647+4063-0x260a),(0x1def+872-0x214e),((char)(0x8ba+1065-0xcc3)),
(0x14b6+4093-0x2494),((char)(0x1b24+914-0x1e96)),((char)(0x7a4+2930-0x12f6)),
((char)(0xa8c+5717-0x20c1)),((char)(0x1dd3+261-0x1eb8)),
((char)(0x1766+1001-0x1b2f)),((char)(0xd9a+1526-0x1370)),
((char)(0x767+29-0x764)),(0xaea+4714-0x1cf7),((char)(0x705+1952-0xe85)),
((char)(0x76a+1112-0xba2)),((char)(0xb56+5100-0x1f22)),
((char)(0x1057+5029-0x23dc)),(0x872+452-0x9da),((char)(0xbff+3235-0x1882)),
((char)(0x580+7176-0x2168)),((char)(0x1460+4736-0x26c0)),
((char)(0xd03+4755-0x1f76)),((char)(0x1c5f+667-0x1eda)),(0x27b+709-0x4e2),
((char)(0x16f2+2832-0x21e2)),((char)(0x1939+2824-0x2421)),(0x1e72+1724-0x2510),
(0x6b6+1849-0xd70),((char)(0xfb0+5068-0x235c)),((char)(0x298+5352-0x1760)),
((char)(0xb99+6913-0x267a)),(0x13bf+2611-0x1d77),(0xc0f+3985-0x1b91),
(0x4b6+8697-0x2692),((char)(0x428+1065-0x831)),(0x147f+2463-0x1e1a),
(0x749+1621-0xd99),((char)(0x16eb+699-0x1986)),((char)(0x10b1+3185-0x1d02)),
(0xa56+6579-0x2402),((char)(0xe45+3386-0x1b5f)),((char)(0x1a34+300-0x1b40)),
((char)(0x20d+5067-0x15b8)),((char)(0x2e7+4811-0x1592)),(0x11f1+5525-0x2709),
(0xa2f+900-0xdab),((char)(0x591+3790-0x143f)),((char)(0x1029+3203-0x1c8c)),
((char)(0x780+932-0xb04)),(0xec+5550-0x161e),((char)(0x5bd+1718-0xc53)),
(0xbc2+4639-0x1dd5),(0x11ec+2287-0x1ad5),((char)(0x6e2+4592-0x18b2)),
((char)(0x13d7+2454-0x1d4d)),(0xecc+34-0xe70),((char)(0x5e1+2225-0xe72)),
((char)(0x9b8+7448-0x26b0)),((char)(0x970+7388-0x262c)),};const unsigned short 
zte_sms_GSM7_SPANISH_To_UCS2_Table_Ex[][(0x1c33+47-0x1c60)]={{
(0x1351+5060-0x270c),(0x1d80+2265-0x2572)},{(0x1f0+1537-0x7e7),
(0x1152+3569-0x1f37)},{(0x4e6+5653-0x1aee),(0x1c4d+974-0x200e)},{
(0x710+8105-0x26a5),(0xf71+5763-0x2596)},{(0x767+6750-0x219d),(0xc18+594-0xdef)}
,{(0x153a+2971-0x20ac),(0x17b5+1570-0x1d5a)},{(0xfd9+1652-0x161e),
(0x151+4082-0x10e7)},{(0x1908+739-0x1baf),(0xbd4+4915-0x1eac)},{
(0x6e9+7436-0x23b8),(0x3b0+6327-0x1be9)},{(0x1355+2087-0x1b3e),
(0x1655+2605-0x2025)},{(0x111a+2616-0x1b12),(0x18ed+307-0x19a4)},{
(0x1d25+1144-0x215c),(0x1b4f+471-0x1c65)},{(0xb38+6338-0x23b1),
(0x743+1256-0xb5e)},{(0x1ae7+1424-0x2028),(0xd34+6569-0x260a)},{
(0x1cf1+1555-0x22af),(0x984+4481-0x1a2b)},{(0x106+7232-0x1ce5),(0x50b+829-0x767)
},{(0x1a1+3099-0xd57),(0x21c8+3616-0xf3c)},{(0x17da+3023-0x2340),
(0x2e7+6132-0x19ee)},{(0xc01+333-0xcdf),(0x116c+633-0x12f2)},{
(0x115+5665-0x16c1),(0x1b7+3007-0xc7c)},};const unsigned short 
Ucs2_To_Gsm7_SPANISH_Table_UCS[][(0xe4+4152-0x111a)]={{(0x130d+4086-0x22f3),
(0x1647+1103-0x1702)},{(0xe05+184-0xeab),(0x47f+4514-0x127b)},{
(0x1ca+7686-0x1fbd),(0x8ff+6344-0x1e34)},{(0x13aa+2279-0x1c7d),
(0x475+5476-0x163e)},{(0x647+489-0x81b),(0x9f1+4956-0x19a4)},{
(0x1304+927-0x168d),(0xec2+170-0xbcc)},{(0x3dc+685-0x672),(0xb5a+4430-0x1900)},{
(0x1ce6+1892-0x2432),(0x171f+798-0x169a)},{(0x462+605-0x6a6),(0x175c+15-0x13d3)}
,{(0x1d54+1126-0x21a0),(0x3ea+5181-0x1489)},};const unsigned char 
Ucs2_To_Gsm7_SPANISH_Table_ASC[]={NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x588+4647-0x17a5),NON_GSM,NON_GSM,
(0xef0+1402-0x145d),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,(0x589+6126-0x1d57),(0x31d+3339-0x1007),(0x1832+589-0x1a5d),
(0x8d1+6905-0x23a7),(0xc89+3864-0x1b9f),(0xc92+889-0xfe6),(0x9f7+3005-0x158e),
(0x1c08+1793-0x22e2),(0x5dc+5229-0x1a21),(0x14f8+3137-0x2110),
(0xc35+4909-0x1f38),(0x578+7469-0x227a),(0x2f2+965-0x68b),(0x22fd+1027-0x26d3),
(0x1c4d+611-0x1e82),(0x71f+927-0xa8f),(0x96f+4763-0x1bda),(0x1b03+2795-0x25bd),
(0x664+2122-0xe7c),(0x2120+1012-0x24e1),(0xa74+1435-0xfdb),(0x46a+1841-0xb66),
(0x1643+1637-0x1c72),(0xa96+5257-0x1ee8),(0x95+9431-0x2534),(0x57c+819-0x876),
(0x45a+4082-0x1412),(0x1649+3953-0x257f),(0x291+908-0x5e1),(0xe0+7211-0x1cce),
(0x5b4+325-0x6bb),(0x101f+4086-0x1fd6),(0x253d+239-0x262c),(0x830+7266-0x2451),
(0x1f3c+1590-0x2530),(0x2ff+1299-0x7cf),(0x1ec8+2105-0x26bd),
(0x12a4+5133-0x266c),(0x1002+683-0x1267),(0x1604+599-0x1814),(0x141c+906-0x175e)
,(0x719+4971-0x1a3b),(0x18fb+878-0x1c1f),(0xbf3+3738-0x1a42),(0xf83+3423-0x1c96)
,(0xbac+1835-0x128a),(0x1cfa+1542-0x22b2),(0x8ac+953-0xc16),(0xe03+3116-0x19df),
(0xdec+5299-0x224e),(0xf3+5620-0x1695),(0x110a+232-0x119f),(0xf50+4616-0x2104),
(0xedb+4013-0x1e33),(0x1d62+980-0x20e0),(0x1669+1606-0x1c58),(0xad7+6331-0x233a)
,(0x12e9+76-0x12dc),(0x1a28+883-0x1d41),NON_GSM,NON_GSM,NON_GSM,NON_GSM,
(0x1188+2747-0x1c32),NON_GSM,(0xeb7+4813-0x2123),(0x94f+722-0xbbf),
(0xfbb+3552-0x1d38),(0x873+1626-0xe69),(0x94f+1327-0xe19),(0x40b+7284-0x2019),
(0x3f9+2944-0xf12),(0x14c0+49-0x1489),(0x568+4214-0x1575),(0x1cbd+2192-0x24e3),
(0x1296+4890-0x2545),(0x37a+6133-0x1b03),(0x73b+7350-0x2384),
(0x1a34+2493-0x2383),(0x29f+2842-0xd4a),(0x19b4+2734-0x23f2),
(0x153c+3998-0x2469),(0x837+3705-0x163e),(0x7a+2158-0x875),(0x174+4916-0x1434),
(0x42b+7711-0x21d5),(0x17f1+3664-0x25cb),(0x646+8006-0x2515),
(0x2329+1109-0x2706),(0xddd+2662-0x17ca),(0x1e37+2120-0x2605),NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0xd96+1280-0x127b),(0xcdf+2307-0x15a2),
NON_GSM,(0x38a+3500-0x1135),(0x1a0a+1086-0x1e24),(0x14fa+601-0x1750),NON_GSM,
(0x83c+1871-0xf2c),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x17ef+556-0x19bb),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,(0xae7+3673-0x18e5),(0x389+5146-0x1795),
(0x1763+769-0x1a48),(0x686+6223-0x1ecc),NON_GSM,(0x744+1807-0xe34),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x1e59+2057-0x2605),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,(0x1276+1365-0x176f),NON_GSM,(0x7ba+739-0xa92),NON_GSM,
NON_GSM,NON_GSM,(0x2a0+4484-0x13c6),NON_GSM,NON_GSM,(0xb88+5940-0x229e),
(0x614+3040-0x1175),NON_GSM,NON_GSM,NON_GSM,(0x814+444-0x955),(0x410+2819-0xf04)
,(0xd77+2892-0x18a6),NON_GSM,(0x1127+1397-0x1698),(0x16d+5304-0x1620),NON_GSM,
NON_GSM,(0x26f+4508-0x1404),NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x74c+4416-0x180f),
(0x5a2+1801-0xca3),NON_GSM,NON_GSM,NON_GSM,(0x1479+4202-0x2467),NON_GSM,
(0xed6+5179-0x2305),(0x7d+1507-0x65a),NON_GSM,NON_GSM,(0xb67+360-0xc51),NON_GSM,
NON_GSM,NON_GSM};const unsigned short zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[]
[(0x4b7+6375-0x1d9c)]={{(0x1dd7+2296-0x26ca),(0x8e5+1897-0xf64)},{
(0x3c1+5941-0x1aed),(0x160+1888-0x7d9)},{(0x1eca+476-0x209c),(0x44a+5876-0x1b32)
},{(0xe62+3345-0x1b68),(0x124+8289-0x20b1)},{(0x1070+1171-0x14f7),
(0x2d8+5672-0x180c)},{(0x1702+505-0x18ee),(0x1085+299-0x11a3)},{
(0xe8b+2592-0x189d),(0xab4+6866-0x24c5)},{(0x5f7+8344-0x2680),
(0x1005+2912-0x1a84)},{(0x212+2853-0xd25),(0x1161+128-0xe3b)},{
(0x73d+3005-0x12e7),(0x1060+694-0xf83)},{(0x54b+3571-0x132a),(0xf1c+1808-0x15ce)
},{(0x7d2+1572-0xde1),(0x101d+1921-0x13f5)},{(0x163a+3542-0x23fa),
(0x1587+1914-0x1961)},{(0xc78+2438-0x15e7),(0xd3c+5942-0x20ca)},{
(0x21a6+1034-0x2598),(0x892+8487-0x2616)},{(0x1c70+671-0x1ef6),
(0x4f6+6315-0x1a09)},{(0xb3d+6539-0x24a9),(0x9cc+6873-0x23db)},{
(0x218c+948-0x2518),(0x1504+4589-0x2676)},{(0x1d2d+2011-0x24df),
(0x11cd+84-0x11a4)},{(0xd2+8299-0x210e),(0x669+5706-0x1c57)},{(0x4bf+1447-0xa2a)
,(0x1de6+269-0x1e98)},{(0x351+1883-0xa6f),(0x980+3490-0x16a4)},{
(0x1c24+1365-0x213b),(0xadc+2359-0x13b6)},{(0x1730+2288-0x1fe0),
(0x1662+853-0x193b)},{(0x833+5484-0x1d5e),(0x3a8+6523-0x1c63)},{
(0xa8+6235-0x18ba),(0x1403+725-0x160b)},{(0xaaf+2180-0x12e4),
(0x1c1c+2032-0x2339)},{(0xbb+497-0x257),(0x179a+1976-0x1e78)},{
(0x8ba+1489-0xe30),(0xa66+4177-0x19f4)},{(0x16b9+1908-0x1dd1),
(0x2300+796-0x2547)},{(0x1bf3+1244-0x206e),(0x1706+2387-0x1f97)},{
(0x8b+7944-0x1f2e),(0x2455+2018-0xb8b)},{(0xdbb+4518-0x1ef8),(0x345+3765-0x110d)
},{(0x1d48+2166-0x254f),(0x2dc+8191-0x21e8)},{(0xfc1+5308-0x2408),
(0x7b2+4637-0x18d5)},{(0x308+4421-0x13d2),(0x1495+2047-0x1bb1)},{
(0x1858+1789-0x1ed9),(0x118b+899-0x1419)},{(0xa9+3661-0xe77),(0xf12+5525-0x23c5)
},};const unsigned short Ucs2_To_Gsm7_PORTUGUESE_Table_UCS[][(0xf24+4619-0x212d)
]={{(0xca1+2801-0x1782),(0x1922+2374-0x1ed4)},{(0x981+6629-0x2351),8929},{
(0x17da+3293-0x249f),8364},{(0xeda+278-0xf8b),8364},};const unsigned char 
Ucs2_To_Gsm7_PORTUGUESE_Table_ASC[]={NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x103f+873-0x139e),NON_GSM,NON_GSM,
(0x13e6+3480-0x2171),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,(0xfd9+404-0x114d),(0xe53+3224-0x1aca),(0x15a9+2577-0x1f98),
(0x71f+2152-0xf64),(0x1e0+8441-0x22d7),(0x13d7+441-0x156b),(0x253+4137-0x1256),
(0xac+504-0x27d),(0x8df+596-0xb0b),(0x93b+6327-0x21c9),(0x65b+8230-0x2657),
(0x184a+494-0x1a0d),(0x602+776-0x8de),(0x61a+5761-0x1c6e),(0x587+5303-0x1a10),
(0x693+6928-0x2174),(0x789+2576-0x1169),(0xe30+1433-0x1398),(0x1ba7+2615-0x25ac)
,(0x13f7+4669-0x2601),(0xc02+3144-0x1816),(0xc16+5287-0x2088),(0xd9+2878-0xbe1),
(0xa93+4291-0x1b1f),(0x399+8737-0x2582),(0x272+1057-0x65a),(0x306+207-0x39b),
(0x13b7+2048-0x1b7c),(0xe77+3453-0x1bb8),(0x1470+2095-0x1c62),
(0x3b5+5827-0x1a3a),(0x13c6+237-0x1474),(0x42b+8182-0x2421),(0x152b+1597-0x1b27)
,(0x76d+7962-0x2645),(0x5ec+4241-0x163a),(0x194f+2091-0x2136),
(0x1d5a+259-0x1e18),(0x99d+4296-0x1a1f),(0x25a2+353-0x26bc),(0x24b+6365-0x1ae0),
(0x15b1+4021-0x251d),(0x279+778-0x539),(0x1aa5+959-0x1e19),(0x1296+4234-0x22d4),
(0x14f2+2871-0x1fdc),(0x1196+238-0x1236),(0xe24+4735-0x2054),(0x7c+7499-0x1d77),
(0x177a+212-0x17fd),(0x548+1607-0xb3d),(0x7df+588-0x9d8),(0x442+6609-0x1dbf),
(0xb38+3435-0x184e),(0x327+2634-0xd1b),(0x5c8+5828-0x1c35),(0x16fb+2426-0x201d),
(0x517+7860-0x2372),(0x188f+1087-0x1c74),NON_GSM,(0xd56+1371-0x129a),NON_GSM,
(0x9db+2689-0x1446),(0x2297+565-0x24bb),(0x10a5+1236-0x14fc),(0xc86+4857-0x1f1e)
,(0x8a9+2698-0x12d1),(0x1a85+561-0x1c53),(0x246a+514-0x2608),
(0x125b+1879-0x194d),(0x193f+3280-0x25a9),(0xa10+2561-0x13aa),(0xe8+7645-0x1e5d)
,(0x567+6384-0x1dee),(0x515+8213-0x24c0),(0x64d+7483-0x231d),(0x731+4569-0x189e)
,(0x963+5272-0x1d8e),(0x25a8+33-0x255b),(0x1152+821-0x1418),(0x207c+6-0x2012),
(0x182a+3839-0x26b8),(0x7e6+6179-0x1f97),(0xdb0+5868-0x2429),(0x395+8587-0x24ac)
,(0x10fa+493-0x1272),(0x18d0+296-0x1982),(0x16fb+1114-0x1ade),
(0x18d3+3218-0x24ed),(0x776+629-0x972),(0x130d+4039-0x225a),NON_GSM,
(0x1a24+2469-0x23af),NON_GSM,(0x4d8+3580-0x1274),NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
(0x1f1b+1273-0x23f9),NON_GSM_P,NON_GSM,(0x738+8066-0x26b9),NON_GSM_P,
(0x1353+526-0x155e),NON_GSM,(0x759+4075-0x16e5),NON_GSM,NON_GSM,
(0x13bc+3342-0x20b8),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x3fc+5984-0x1b38)
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x10ff+2822-0x1bf1),
(0x2a8+6830-0x1d48),(0x1541+107-0x1590),(0x21ed+173-0x223f),NON_GSM_P,NON_GSM_P,
NON_GSM_P,(0x8b7+2880-0x13e4),NON_GSM,(0x19f4+739-0x1cb8),(0x2163+333-0x2292),
NON_GSM,NON_GSM,(0xaa9+1949-0x1206),NON_GSM,NON_GSM,NON_GSM,NON_GSM_P,NON_GSM,
(0x16cf+3725-0x2543),(0x1be8+2473-0x2586),(0xeeb+2415-0x17fe),NON_GSM_P,NON_GSM,
NON_GSM_P,NON_GSM,(0x958+5646-0x1f09),NON_GSM,(0x13d7+4032-0x2339),NON_GSM,
NON_GSM,NON_GSM_P,(0x1d78+2068-0x250d),(0x1060+4698-0x22ab),(0x5b9+5847-0x1c73),
(0x217f+253-0x2201),NON_GSM_P,NON_GSM_P,NON_GSM_P,(0x149a+4158-0x24cf),NON_GSM_P
,(0x8c5+5078-0x1c96),(0x1644+573-0x187d),NON_GSM,NON_GSM_P,(0x14c3+3188-0x2130),
NON_GSM,NON_GSM,NON_GSM,NON_GSM_P,NON_GSM_P,(0x2d0+3043-0xeab),
(0xa11+6752-0x2465),(0x1d55+536-0x1ef1),NON_GSM_P,NON_GSM,NON_GSM_P,NON_GSM_P,
(0xdf8+5798-0x2498),NON_GSM,(0x1b2b+3130-0x26e7),NON_GSM,NON_GSM,NON_GSM};const 
unsigned short zte_sms_GSMDefault_To_UCS2_Table_Ex[][(0xf5a+2011-0x1733)]={{
(0x11c3+4756-0x244d),(0x7a+8809-0x22d7)},{(0xe73+1272-0x1357),
(0x1261+3901-0x2140)},{(0xa7c+3231-0x16f3),(0x1b15+2836-0x25ae)},{
(0x1bd2+583-0x1df0),(0x19e6+1864-0x20b1)},{(0x17cb+198-0x1862),
(0x15a5+4430-0x2697)},{(0x367+1312-0x84b),(0x1785+1931-0x1eb5)},{
(0x5f8+5212-0x1a17),(0xd11+3801-0x1b6c)},{(0x528+8688-0x26da),
(0x11e8+3738-0x2025)},{(0xb02+5046-0x1e78),(0x1816+2834-0x22ac)},{
(0x143d+4783-0x2687),(0x269a+3418-0x1348)},};const unsigned short 
UCS2_To_GSMDefault_Table_UCS2[][(0x14b5+1776-0x1ba3)]={{(0xf44+6085-0x26f9),
(0xaf9+1096-0xbad)},{(0x101a+5079-0x23df),(0xb24+1230-0xc4c)},{
(0x864+4594-0x1a43),(0x16b2+4104-0x2327)},{(0x185a+1828-0x1f6a),
(0x140f+4711-0x22db)},{(0x199+4435-0x12d7),(0x1ed1+2850-0x264a)},{
(0xc7b+3602-0x1a77),(0xcef+5684-0x1f83)},{(0x203+8659-0x23bf),936},{
(0x1416+3641-0x2237),(0x9eb+230-0x72e)},{(0x39f+4475-0x1501),(0x4da+6770-0x1bb4)
},{(0x17c8+1676-0x1e3a),(0x553+5678-0x17e3)},};const unsigned char 
UCS2_To_GSMDefault_Table_ASC[]={NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x19a3+2083-0x21bc),NON_GSM,NON_GSM,
(0x933+3231-0x15c5),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,(0x1c5+8577-0x2326),(0x403+6044-0x1b7e),(0xd27+4664-0x1f3d),
(0x710+414-0x88b),(0xce3+4389-0x1e06),(0xf16+3238-0x1b97),(0x1ad6+1291-0x1fbb),
(0x1c6a+1197-0x20f0),(0xca4+1203-0x112f),(0x691+6956-0x2194),(0x25f+7704-0x204d)
,(0x69f+6750-0x20d2),(0xa3c+2389-0x1365),(0xc6+1476-0x65d),(0x84b+1413-0xda2),
(0x188+4607-0x1358),(0x3bb+1641-0x9f4),(0x33d+883-0x67f),(0xaf7+401-0xc56),
(0xb01+4272-0x1b7e),(0x110b+3655-0x1f1e),(0xb26+2500-0x14b5),
(0x115d+1067-0x1552),(0xdb+5292-0x1550),(0xd87+1046-0x1165),(0x654+6217-0x1e64),
(0x1928+2534-0x22d4),(0x335+8186-0x22f4),(0xd08+317-0xe09),(0x32a+3796-0x11c1),
(0xd17+5788-0x2375),(0xa2f+4848-0x1ce0),(0x1a5b+1325-0x1f88),
(0x14b7+4264-0x251e),(0xcc+403-0x21d),(0x20d9+46-0x20c4),(0x1035+658-0x1283),
(0x195a+2753-0x23d6),(0x1aad+2676-0x24db),(0xd9a+4961-0x20b4),
(0x1293+4919-0x2582),(0x58f+3577-0x133f),(0xd10+2553-0x16bf),
(0x1a47+2152-0x2264),(0x2ca+8692-0x2472),(0x8d8+2110-0x10c9),
(0x11a3+1583-0x1784),(0xc70+3622-0x1a47),(0x119b+4740-0x23cf),(0x99+6829-0x1af5)
,(0xc3b+5343-0x20c8),(0x14cb+2355-0x1dab),(0x1fa3+1544-0x2557),(0x769+576-0x954)
,(0x17fb+1369-0x1cfe),(0xad5+4552-0x1c46),(0xe8f+4916-0x216b),
(0x101+5286-0x154e),(0x6e6+4671-0x18cb),NON_GSM,NON_GSM,NON_GSM,NON_GSM,
(0xd57+3384-0x1a7e),NON_GSM,(0x5fc+1017-0x994),(0x1195+3695-0x1fa2),
(0x19d3+2269-0x224d),(0x1705+1920-0x1e21),(0xefa+1420-0x1421),
(0x1477+4051-0x23e4),(0x4d9+2924-0xfde),(0x15d6+2603-0x1f99),(0xcd5+169-0xd15),
(0x134c+1614-0x1930),(0x97f+7392-0x25f4),(0x1aaf+1561-0x205c),
(0x8e5+4312-0x1950),(0x1137+5031-0x2470),(0xb49+2825-0x15e3),(0x5d5+1661-0xbe2),
(0x1136+2560-0x1ac5),(0x8b7+1220-0xd09),(0xa27+7455-0x26d3),(0x15a4+815-0x185f),
(0x27f+4501-0x139f),(0x768+5743-0x1d61),(0x3e0+7459-0x208c),(0x16f9+2594-0x20a3)
,(0x382+5217-0x176a),(0xa72+2174-0x1276),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM
,NON_GSM,NON_GSM,(0xe0+2134-0x91b),(0x145b+638-0x1699),NON_GSM,
(0xa21+5333-0x1ef5),(0x8eb+5481-0x1e30),(0x273+9030-0x25b6),NON_GSM,
(0x323+8165-0x22a9),NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x1fd7+839-0x22be),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,(0x10e8+4886-0x23a3),(0x1b02+2933-0x2669),
(0x6a0+1811-0xd97),(0x9d9+6282-0x225a),NON_GSM,(0x36+4372-0x112b),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,NON_GSM,(0x128a+4363-0x2338),NON_GSM,
NON_GSM,NON_GSM,NON_GSM,(0x5d3+5262-0x1a05),NON_GSM,(0x1d0d+830-0x2040),NON_GSM,
NON_GSM,NON_GSM,(0x1a74+2723-0x24b9),NON_GSM,NON_GSM,(0x1cc+658-0x440),
(0x2f5+3176-0xede),NON_GSM,NON_GSM,NON_GSM,(0x1561+2644-0x1f3a),
(0x768+6324-0x200d),(0x58b+1857-0xcaf),NON_GSM,(0x64a+6395-0x1f41),
(0x12d3+1336-0x1806),NON_GSM,NON_GSM,(0x1107+3293-0x1ddd),NON_GSM,NON_GSM,
NON_GSM,NON_GSM,(0xe37+4900-0x20de),(0x1bfd+252-0x1cf1),NON_GSM,NON_GSM,NON_GSM,
(0x2f5+3962-0x11f3),NON_GSM,(0x10ea+5528-0x2676),(0x8b7+5911-0x1fc8),NON_GSM,
NON_GSM,(0x8a7+579-0xa6c),NON_GSM,NON_GSM,NON_GSM};const unsigned short 
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table[]={(0x310+1885-0xa2d),(0xd3a+2561-0x1698),
(0x1056+2188-0x18be),(0x1a2d+1610-0x1fd2),(0x1c54+1377-0x20cb),
(0x114f+4158-0x20a4),(0x200+3320-0xdfe),(0xf7+3955-0xf7d),(0x12b7+3950-0x2132),
(0x579+7715-0x22b5),(0x10ff+1825-0x1816),(0x101a+5502-0x24c4),
(0x133c+340-0x139c),(0x12f+8113-0x20d3),(0x48d+479-0x5ab),(0x260+5175-0x15b6),
(0x1d4c+1940-0x214c),(0x7fd+4053-0x1773),(0x4db+434-0x5e3),(0x664+7502-0x22eb),
(0x434+3694-0x11e2),8734,(0x188b+256-0x192d),(0x163d+2915-0x2144),
(0x2461+4731-0x1630),(0x53c+4980-0x17dd),(0xed0+1580-0x1480),(0x249+2595-0xbcc),
(0x1a94+2006-0x21a8),(0x56f+4196-0x14f1),(0xc58+1750-0x1264),(0xe73+880-0x111a),
(0x374+6186-0x1b7e),(0x12f3+4916-0x2606),(0xe11+1352-0x1337),(0x1a6+2488-0xb3b),
(0x18bb+3176-0x2473),(0x875+7188-0x2464),(0x4e1+3919-0x140a),(0xb93+6206-0x23aa)
,(0xe23+1239-0x12d2),(0x86f+3562-0x1630),(0x87a+7254-0x24a6),(0x84d+5630-0x1e20)
,(0x5b2+6700-0x1fb2),(0x188c+1296-0x1d6f),(0x875+5711-0x1e96),
(0x1711+3048-0x22ca),(0x13a2+106-0x13dc),(0x1735+2481-0x20b5),
(0x5a0+6765-0x1fdb),(0x165+9673-0x26fb),(0xe18+3928-0x1d3c),(0x1c88+1520-0x2243)
,(0x6e2+3964-0x1628),(0x1b6f+966-0x1efe),(0xbf1+4703-0x1e18),
(0x18e6+3024-0x247d),(0xe4+1348-0x5ee),(0x13f6+723-0x168e),(0x5cf+1300-0xaa7),
(0x85b+2212-0x10c2),(0x13f+6331-0x19bc),(0x13a1+554-0x158c),(0x98c+4507-0x1a5a),
(0xbc2+4748-0x1e0d),(0x1d87+1764-0x2429),(0x17f5+1256-0x1c9a),(0xb6c+507-0xd23),
(0x1269+2618-0x1c5e),(0x8e4+6816-0x233e),(0x582+7828-0x23cf),(0xfda+4246-0x2028)
,(0x1300+2056-0x1abf),(0x1376+3750-0x21d2),(0x56f+8261-0x2569),
(0x7b7+2691-0x11ee),(0x71a+771-0x9d0),(0x400+7761-0x2203),(0x352+3541-0x10d8),
(0x1826+3278-0x24a4),(0x3b0+1218-0x821),(0xe14+6203-0x25fd),(0x13e7+1730-0x1a56)
,(0xe42+3144-0x1a36),(0x9a9+6498-0x22b6),(0x455+1520-0x9ef),(0xa3d+1147-0xe61),
(0xff5+4630-0x21b3),(0xa0a+6734-0x23ff),(0x70c+4033-0x1673),(0x9c0+5696-0x1f3d),
(0x7d5+2643-0x1153),(0x3a3+7540-0x203d),(0x1a2f+1410-0x1ed5),(0x1553+949-0x1861)
,(0x396+727-0x5ef),(0x24d+1342-0x72a),(0x2571+256-0x260f),(0xa55+4111-0x1a01),
(0x1d4+1234-0x642),(0xa23+2803-0x14b1),(0x25c6+119-0x25d7),(0x1ff4+1546-0x2597),
(0x239+7550-0x1f4f),(0x416+838-0x6f3),(0x927+1135-0xd2c),(0xb60+6866-0x25c7),
(0x2ac+4431-0x138f),(0x1dcf+994-0x2144),(0x12e6+4947-0x25cb),(0x581+4945-0x1863)
,(0xfd+2723-0xb30),(0xb23+4786-0x1d64),(0x120b+3977-0x2122),(0x1279+5291-0x26b1)
,(0x5df+2266-0xe45),(0x10a4+5075-0x2402),(0x55f+7772-0x2345),(0xabc+5879-0x213c)
,(0x1d3+3821-0x1048),(0xcfc+951-0x103a),(0x16c1+3579-0x2442),(0x913+7512-0x2588)
,(0xc8f+6833-0x264b),(0x5a4+3488-0x12e4),(0x1376+2577-0x1c8b),
(0x192c+1407-0x1dcb)};const unsigned short zte_sms_GSMDefault_To_UCS2_Table[]={
(0x1c1+5766-0x1807),(0x6f0+4735-0x18cc),(0x91+6463-0x19ac),(0x126a+5113-0x25be),
(0x1945+2137-0x20b6),(0x1cb2+1967-0x2378),(0x6d4+2690-0x105d),(0x148+1031-0x463)
,(0xcda+5246-0x2066),(0x150+504-0x281),(0x6c5+4456-0x1823),(0x19e0+2382-0x2256),
(0x4f1+7530-0x2163),(0x1862+370-0x19c7),(0x9cf+5848-0x1fe2),(0x5c5+2935-0x1057),
(0x86f+1057-0x8fc),(0xc35+1411-0x1159),(0x8f1+634-0x7c5),(0xb04+5621-0x1d66),
(0xe3b+6503-0x2407),(0x1ffa+1070-0x207f),(0x10db+5671-0x2362),(0x57b+1984-0x993)
,(0x1266+4811-0x218e),(0xb61+5290-0x1c73),(0x1802+1787-0x1b5f),
(0x22f4+906-0x25de),(0x108a+4245-0x2059),(0x7aa+3463-0x144b),(0x11c2+944-0x1493)
,(0x22a8+321-0x2320),(0x1c6f+2159-0x24be),(0xc0c+4225-0x1c6c),
(0x1c94+669-0x1f0f),(0x25a+3477-0xfcc),(0xb82+3193-0x1757),(0x12fa+4126-0x22f3),
(0x10b+9457-0x25d6),(0xf5f+3686-0x1d9e),(0x1515+401-0x167e),(0x65a+8281-0x268a),
(0x126d+1053-0x1660),(0x6a3+1573-0xc9d),(0x1842+3019-0x23e1),(0x1200+491-0x13be)
,(0x1543+4027-0x24d0),(0x134c+3288-0x1ff5),(0x2a7+5081-0x1650),
(0x1c1f+1885-0x234b),(0x1414+1147-0x185d),(0x9fc+710-0xc8f),(0x419+8684-0x25d1),
(0x4f1+386-0x63e),(0xe10+1749-0x14af),(0xd38+3925-0x1c56),(0xf3c+5668-0x2528),
(0xf37+3425-0x1c5f),(0xb3c+2644-0x1556),(0x20a4+1634-0x26cb),
(0x142b+1113-0x1848),(0x41c+6719-0x1e1e),(0x4e9+714-0x775),(0x3a5+4400-0x1496),
(0x7f6+4581-0x193a),(0x154+2388-0xa67),(0x1114+3604-0x1ee6),(0x1172+3539-0x1f02)
,(0xda3+5817-0x2418),(0x1559+2301-0x1e11),(0xda8+1079-0x1199),
(0xb95+2989-0x16fb),(0x1db+6108-0x196f),(0xc9c+4643-0x1e76),(0xa82+446-0xbf6),
(0x347+6738-0x1d4e),(0x689+8209-0x264e),(0x18a8+736-0x1b3b),(0x9a3+6726-0x239b),
(0x4a6+8222-0x2475),(0x13a5+1864-0x1a9d),(0x95+8239-0x2073),(0x1624+1095-0x1a19)
,(0x1689+2128-0x1e86),(0x487+5740-0x1a9f),(0x81+2638-0xa7a),(0x1f53+1971-0x26b0)
,(0x3d5+294-0x4a4),(0x144f+1797-0x1afc),(0x1c2f+701-0x1e93),(0xce6+816-0xfbc),
(0xa12+5943-0x2085),(0xcba+1801-0x12ed),(0x14ec+4066-0x23fd),(0xb99+6553-0x2456)
,(0x14c5+1362-0x1970),(0x805+708-0xa0a),(0x1ef8+1752-0x256f),
(0x110c+1527-0x16a1),(0xc19+1409-0x1137),(0x5da+5806-0x1c24),
(0x1478+1241-0x18ec),(0x9d2+7063-0x2503),(0x1b2+5508-0x16cf),(0x726+2148-0xf22),
(0xdd8+3682-0x1bd1),(0x18c9+1166-0x1ced),(0x33a+2227-0xb82),(0x112f+4191-0x2122)
,(0x1507+1886-0x1bf8),(0xaec+3958-0x19f4),(0x1737+3102-0x22e6),
(0x194+6998-0x1c7a),(0x1663+2994-0x21a4),(0x1a2+2357-0xa65),(0x24a+254-0x2d5),
(0x4d7+3988-0x13f7),(0x13e1+2074-0x1b86),(0xbc0+4040-0x1b12),
(0x1012+4793-0x2254),(0x15b8+7-0x1547),(0x166b+2246-0x1eb8),(0xebc+5059-0x2205),
(0x1fb4+718-0x219e),(0x1309+1405-0x1790),(0x2392+699-0x255c),(0x191+8766-0x22d3)
,(0xfb+2093-0x848)};int Bytes2String(const unsigned char*pSrc,char*pDst,int 
nSrcLength){const char tab[]="0123456789ABCDEF";int i=(0xb61+3283-0x1834);if(
pSrc==NULL||pDst==NULL||nSrcLength<(0x11cc+3061-0x1dc1)){return-
(0x46+6087-0x180c);}for(i=(0x1035+5570-0x25f7);i<nSrcLength;i++){*pDst++=tab[*
pSrc>>(0x1a36+832-0x1d72)];*pDst++=tab[*pSrc&(0x343+2617-0xd6d)];pSrc++;}*pDst=
'\0';return nSrcLength*(0x2d0+6724-0x1d12);}int String2Bytes(const char*pSrc,
unsigned char*pDst,int nSrcLength){int i=(0x15c+5226-0x15c6);if(pSrc==NULL||pDst
==NULL||nSrcLength<(0x1f17+1752-0x25ef)){return-(0x7f8+805-0xb1c);}for(i=
(0x312+8254-0x2350);i<nSrcLength;i+=(0x24a+6305-0x1ae9)){if(*pSrc>=
((char)(0xc89+160-0xcf9))&&*pSrc<=((char)(0x1399+3915-0x22ab))){*pDst=(*pSrc-
((char)(0x103b+1279-0x150a)))<<(0x1f3+2118-0xa35);}else{*pDst=((toupper(*pSrc)-
((char)(0x158d+1270-0x1a42)))+(0x20d+1598-0x841))<<(0x1741+2520-0x2115);}pSrc++;
if(*pSrc>=((char)(0x413+6880-0x1ec3))&&*pSrc<=((char)(0x18f+5962-0x18a0))){*pDst
|=*pSrc-((char)(0x5db+5286-0x1a51));}else{*pDst|=(toupper(*pSrc)-
((char)(0x18bb+3546-0x2654)))+(0x126+245-0x211);}pSrc++;pDst++;}return 
nSrcLength/(0xdda+3074-0x19da);}int EncodeUcs2(const char*pSrc,unsigned char*
pDst,int nSrcLength){if(pSrc==NULL||pDst==NULL||nSrcLength<(0xd1d+4138-0x1d47)){
return-(0x19c2+1835-0x20ec);}(void)String2Bytes(pSrc,pDst,(int)nSrcLength);
return nSrcLength/(0x270+8855-0x2505);}int Encode7bit(const char*pSrc,unsigned 
char*pDst,int nSrcLength){int nSrc;int nDst;int nChar;unsigned char nLeft=
(0xff5+2576-0x1a05);if(pSrc==NULL||pDst==NULL||nSrcLength<(0x943+209-0xa14)){
return-(0x28c+4474-0x1405);}nSrc=(0x5a0+4776-0x1848);nDst=(0x1d3d+682-0x1fe7);
while(nSrc<nSrcLength){nChar=nSrc&(0x7bf+3727-0x1647);if(nChar==
(0xca5+5769-0x232e)){nLeft=*pSrc;if((g_zUfiSms_ConcatSms.total_msg>
(0xe39+3974-0x1dbe))&&(nSrc==(nSrcLength-(0x43b+4087-0x1431)))){nDst++;}}else{*
pDst=(*pSrc<<((0x1401+3064-0x1ff1)-nChar))|nLeft;nLeft=*pSrc>>nChar;pDst++;nDst
++;}pSrc++;nSrc++;}return nDst;}SINT32 zUfiSms_EncodePdu_DeliverReport(CHAR*pDst
,UINT8 TP_FCS){SINT32 nLength=(0x14bf+3812-0x23a3);SINT32 nDstLength=
(0xb66+3545-0x193f);UINT8 buf[(0xa5c+1241-0xe35)]={(0x728+5644-0x1d34)};if(NULL
==pDst){return-(0x15f6+1360-0x1b45);}if(TP_FCS!=(0x5f+3068-0xc5b)){buf[
(0x18a+6686-0x1ba8)]=(0xf1c+328-0x1064);buf[(0xadc+3621-0x1900)]=TP_FCS;buf[
(0xcb6+4438-0x1e0a)]=(0x44a+3976-0x13d2);nDstLength+=Bytes2String(buf,&pDst[
nDstLength],(0x7e3+3933-0x173d));}else{buf[(0xd03+200-0xdcb)]=(0x686+265-0x78f);
buf[(0x241f+469-0x25f3)]=(0x824+5506-0x1da6);nDstLength+=Bytes2String(buf,&pDst[
nDstLength],(0xcca+369-0xe39));}return nDstLength;}unsigned long 
zUfiSms_ConvertAsciiToGsmDefault(const unsigned char*inputs,unsigned char*
outputs,unsigned long len){unsigned long i=(0x62c+1135-0xa9b);unsigned long j=
(0x1103+1205-0x15b8);unsigned long k=(0x1a9+5491-0x171c);if(NULL==inputs||NULL==
outputs){printf(
"\x73\x6d\x73\x3a\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73");
return(0x1c38+758-0x1f2e);}for(i=(0xc05+1355-0x1150);i<len;i++){j=inputs[i];if(
g_zUfiSms_AsciiToGsmdefaultTable[j]<(0x13dd+242-0x13d0)){outputs[k]=
g_zUfiSms_AsciiToGsmdefaultTable[j];}else{outputs[k]=(
g_zUfiSms_AsciiToGsmdefaultTable[j]&65280)>>(0xee3+4737-0x215c);k++;outputs[k]=(
g_zUfiSms_AsciiToGsmdefaultTable[j]&(0xb26+1644-0x1093));}k++;}return k;}
unsigned long zUfiSms_ConvertUcs2ToSpanish(const unsigned char*def,unsigned char
*gsm_default,unsigned long len){unsigned long i=(0x6f2+4517-0x1897);unsigned 
long k=(0x19aa+1858-0x20ec);unsigned long p=(0x1373+460-0x153f);unsigned long 
tmp=(0x1120+4884-0x2434);unsigned long s1=(0x826+2156-0x1092),s2=
(0x83f+4428-0x198b);unsigned long q=(0x73b+1705-0xde4);s1=sizeof(
zte_sms_GSM7_SPANISH_To_UCS2_Table_Ex)/sizeof(
zte_sms_GSM7_SPANISH_To_UCS2_Table_Ex[(0xb02+667-0xd9d)]);s2=sizeof(
Ucs2_To_Gsm7_SPANISH_Table_UCS)/sizeof(Ucs2_To_Gsm7_SPANISH_Table_UCS[
(0xb31+469-0xd06)]);for(i=(0x5e7+1644-0xc53);i<len;i++){if(def[i]==
(0x86+6022-0x180c)){i++;if(Ucs2_To_Gsm7_SPANISH_Table_ASC[def[i]]!=NON_GSM){
gsm_default[k]=Ucs2_To_Gsm7_SPANISH_Table_ASC[def[i]];k++;continue;}else if((
Ucs2_To_Gsm7_SPANISH_Table_ASC[def[i]]==NON_GSM)&&(def[i]==(0x85c+2637-0x1289)))
{gsm_default[k]=(0xfe9+2713-0x1a62);k++;continue;}for(q=(0x3ba+5921-0x1adb);q<s1
;q++){if(def[i]==zte_sms_GSM7_SPANISH_To_UCS2_Table_Ex[q][(0x1213+2550-0x1c08)])
{gsm_default[k]=(0x8d8+875-0xc28);k++;gsm_default[k]=
zte_sms_GSM7_SPANISH_To_UCS2_Table_Ex[q][(0x89b+760-0xb93)];break;}}}else{tmp=(
def[i]<<(0x808+7758-0x264e))+def[i+(0xb7+1961-0x85f)];i++;if(tmp==8364){
gsm_default[k]=(0xf9+5498-0x1658);k++;gsm_default[k]=(0x6ab+6942-0x2164);k++;}
for(p=(0x11c+7329-0x1dbd);p<s2;p++){if(tmp==Ucs2_To_Gsm7_SPANISH_Table_UCS[p][
(0xa7f+155-0xb19)]){gsm_default[k]=Ucs2_To_Gsm7_SPANISH_Table_UCS[p][
(0x9f5+534-0xc0b)];break;}}}k++;}gsm_default[k]='\0';return k;}unsigned long 
zUfiSms_ConvertUcs2ToPortuguese(const unsigned char*def,unsigned char*
gsm_default,unsigned long len){unsigned long i=(0x416+1520-0xa06);unsigned long 
k=(0x12fc+4042-0x22c6);unsigned long p=(0x11bf+2653-0x1c1c);unsigned long tmp=
(0xca+7762-0x1f1c);unsigned long s1=(0x55d+1741-0xc2a),s2=(0x1146+3184-0x1db6);
unsigned long q=(0x17db+1378-0x1d3d);s1=sizeof(
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex)/sizeof(
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[(0x1224+1247-0x1703)]);s2=sizeof(
Ucs2_To_Gsm7_PORTUGUESE_Table_UCS)/sizeof(Ucs2_To_Gsm7_PORTUGUESE_Table_UCS[
(0x1e9+3893-0x111e)]);for(i=(0x1512+1414-0x1a98);i<len;i++){if(def[i]==
(0x22d7+929-0x2678)){i++;if(Ucs2_To_Gsm7_PORTUGUESE_Table_ASC[def[i]]!=NON_GSM){
gsm_default[k]=Ucs2_To_Gsm7_PORTUGUESE_Table_ASC[def[i]];k++;continue;}else if((
Ucs2_To_Gsm7_PORTUGUESE_Table_ASC[def[i]]==NON_GSM)&&(def[i]==
(0xed1+2882-0x19f3))){gsm_default[k]=(0x4b9+6883-0x1f7c);k++;continue;}for(q=
(0x19e1+1089-0x1e22);q<s1;q++){if(def[i]==
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[q][(0x1264+2332-0x1b7f)]){gsm_default[k
]=(0x1220+2261-0x1ada);k++;gsm_default[k]=
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[q][(0x1680+2174-0x1efe)];break;}}}else{
tmp=(def[i]<<(0x2ad+5055-0x1664))+def[i+(0x1114+1890-0x1875)];i++;if(tmp==
(0x2686+4355-0x16dd)){gsm_default[k]=(0x140+8843-0x23b0);k++;gsm_default[k]=
(0x8d2+3935-0x17cc);k++;continue;}for(p=(0xe0d+2711-0x18a4);p<s2;p++){if(tmp==
Ucs2_To_Gsm7_PORTUGUESE_Table_UCS[p][(0x15e6+3388-0x2321)]){gsm_default[k]=
Ucs2_To_Gsm7_PORTUGUESE_Table_UCS[p][(0x44f+5736-0x1ab7)];break;}}}k++;}
gsm_default[k]='\0';return k;}unsigned long zUfiSms_ConvertUcs2ToGsmDefault(
const unsigned char*def,unsigned char*gsm_default,unsigned long len){unsigned 
long i=(0x162c+749-0x1919);unsigned long k=(0x1305+1741-0x19d2);unsigned long p=
(0x1ed9+1813-0x25ee);unsigned long tmp=(0x152+2863-0xc81);unsigned long s1=
(0x6c5+6451-0x1ff8),s2=(0x3d6+6799-0x1e65);unsigned long q=(0x18c3+2538-0x22ad);
s1=sizeof(zte_sms_GSMDefault_To_UCS2_Table_Ex)/sizeof(
zte_sms_GSMDefault_To_UCS2_Table_Ex[(0x669+7916-0x2555)]);s2=sizeof(
UCS2_To_GSMDefault_Table_UCS2)/sizeof(UCS2_To_GSMDefault_Table_UCS2[
(0x1329+3419-0x2084)]);for(i=(0x151+646-0x3d7);i<len;i++){if(def[i]==
(0x1a23+1235-0x1ef6)){i++;if(UCS2_To_GSMDefault_Table_ASC[def[i]]!=NON_GSM){
gsm_default[k]=UCS2_To_GSMDefault_Table_ASC[def[i]];k++;continue;}else if((
UCS2_To_GSMDefault_Table_ASC[def[i]]==NON_GSM)&&(def[i]==(0xc58+6703-0x2667))){
gsm_default[k]=(0x84+5612-0x1650);k++;continue;}for(q=(0x1c67+1994-0x2431);q<s1;
q++){if(def[i]==zte_sms_GSMDefault_To_UCS2_Table_Ex[q][(0x11da+3341-0x1ee6)]){
gsm_default[k]=(0x60c+5767-0x1c78);k++;gsm_default[k]=
zte_sms_GSMDefault_To_UCS2_Table_Ex[q][(0x141d+3416-0x2175)];break;}}}else{tmp=(
def[i]<<(0x18d6+2200-0x2166))+def[i+(0x14cd+4650-0x26f6)];i++;if(tmp==
(0x2596+1142-0x960)){gsm_default[k]=(0x175a+2207-0x1fde);k++;gsm_default[k]=
(0x1229+2834-0x1cd6);k++;continue;}for(p=(0x3da+5726-0x1a38);p<s2;p++){if(tmp==
UCS2_To_GSMDefault_Table_UCS2[p][(0xcc8+4298-0x1d91)]){gsm_default[k]=
UCS2_To_GSMDefault_Table_UCS2[p][(0x188b+753-0x1b7c)];break;}}}k++;}gsm_default[
k]='\0';return k;}UINT8 zUfiSms_TsIntToBcd(const UINT8 i){return(UINT8)(((i%
(0xe28+1092-0x1262))+((i/(0x889+1016-0xc77))<<(0x723+7124-0x22f3))));}void 
zUfiSms_DecodeRelativeTime(UINT8 iValidTime,T_zUfiSms_TimeStamp*ptTimeStamp){
uint32 i=(0x944+483-0xb27);if(ptTimeStamp!=NULL){memset((void*)ptTimeStamp,
(0x256+2129-0xaa7),sizeof(wms_timestamp_s_type));if(iValidTime<(0xa0c+573-0xbb9)
){i=(iValidTime+(0x1714+2717-0x21b0))*(0x3b2+8906-0x2677);ptTimeStamp->hour=(
UINT8)zUfiSms_TsIntToBcd((UINT8)(i/(0xd81+6020-0x24c9)));ptTimeStamp->minute=(
UINT8)zUfiSms_TsIntToBcd((UINT8)(i%(0x15ed+2220-0x1e5d)));}else if(iValidTime<
(0xcca+3512-0x19db)){i=(iValidTime-(0x20f+9430-0x2656))*(0x781+1909-0xed8);
ptTimeStamp->hour=(UINT8)zUfiSms_TsIntToBcd((UINT8)((0x9ef+7310-0x2671)+i/
(0x14cf+3219-0x2126)));ptTimeStamp->minute=(UINT8)zUfiSms_TsIntToBcd((UINT8)(i%
(0xaa9+4083-0x1a60)));}else if(iValidTime<(0x147b+1609-0x19ff)){i=iValidTime-
(0xfa+6899-0x1b47);ptTimeStamp->month=(UINT8)zUfiSms_TsIntToBcd((UINT8)(i/
(0x4bc+8439-0x2595)));ptTimeStamp->day=(UINT8)zUfiSms_TsIntToBcd((UINT8)(i%
(0x15d7+4175-0x2608)));}else{i=(iValidTime-(0xa91+3699-0x1844))*
(0xa02+6142-0x21f9);ptTimeStamp->year=(UINT8)zUfiSms_TsIntToBcd((UINT8)(i/
(0x4d3+1937-0xaf7)));ptTimeStamp->month=(UINT8)zUfiSms_TsIntToBcd((UINT8)((i%
(0x78f+4454-0x1788))/(0x1a44+3196-0x26a2)));ptTimeStamp->day=(UINT8)
zUfiSms_TsIntToBcd((UINT8)((i%(0x2538+107-0x2436))%(0x2035+32-0x2037)));}}else{
printf(
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x72\x65\x6c\x61\x74\x69\x76\x65\x5f\x74\x69\x6d\x65"
);}}int zUfiSms_CharToInt(char*pCharArray,int iLen,unsigned char*pIntArray){int 
i=(0xf06+5398-0x241c);if(pIntArray==NULL||pCharArray==NULL){return ZUFI_FAIL;}
for(i=(0x40b+1231-0x8da);i<iLen;i++){pIntArray[i]=pCharArray[i]-
((char)(0x12f6+4689-0x2517));}return ZUFI_SUCC;}void zUfiSms_FillGlobalTpudGsm7(
T_zUfiSms_SubmitTpdu*ptSubmit,T_zUfiSms_ConcatInfo*ptConcatSms,
T_zUfiSms_DbStoreData*ptDbSaveData){int i=(0x13d6+2892-0x1f22);if(ptConcatSms->
total_msg>(0x286+6950-0x1dab)){g_zUfiSms_SendingSms.TP_UDHI=(0xa7a+6560-0x2419);
g_zUfiSms_SendingSms.TP_UD[(0x7af+2069-0xfc4)]=(0x570+8057-0x24e4);
g_zUfiSms_SendingSms.TP_UD[(0x70c+2801-0x11fc)]=(0x1a0+8818-0x240d);
g_zUfiSms_SendingSms.TP_UD[(0x58+3294-0xd34)]=(0x16a+256-0x267);
g_zUfiSms_SendingSms.TP_UD[(0x168b+1950-0x1e26)]=(char)ptDbSaveData->concat_info
[(0xdfc+3247-0x1aab)]%(0x586+2614-0xebd);g_zUfiSms_SendingSms.TP_UD[
(0x1176+1153-0x15f3)]=(char)ptConcatSms->total_msg;g_zUfiSms_SendingSms.TP_UD[
(0x8d2+7434-0x25d7)]=(char)ptConcatSms->current_sending+(0x14e6+2025-0x1cce);
g_zUfiSms_SendingSms.TP_UD[(0x162b+1507-0x1c08)]=(0x1603+2473-0x1fa6);for(i=
(0x45+691-0x2f8);i<ptSubmit->user_data.sm_len;i++){g_zUfiSms_SendingSms.TP_UD[i+
(0xcf1+2732-0x1796)]=ptSubmit->user_data.sm_data[i];}g_zUfiSms_SendingSms.
TP_UDLength=ptSubmit->user_data.sm_len+(0x1be5+1743-0x22ad);}else{for(i=
(0x2d8+8009-0x2221);i<ptSubmit->user_data.sm_len;i++){g_zUfiSms_SendingSms.TP_UD
[i]=ptSubmit->user_data.sm_data[i];}g_zUfiSms_SendingSms.TP_UDLength=ptSubmit->
user_data.sm_len;}}void zUfiSms_FillGlobalTpudUcs2(T_zUfiSms_SubmitTpdu*ptSubmit
,T_zUfiSms_ConcatInfo*ptConcatSms,T_zUfiSms_DbStoreData*ptDbSaveData){if(
ptConcatSms->total_msg>(0x13a1+1538-0x19a2)){g_zUfiSms_SendingSms.TP_UDHI=
(0x8b5+2552-0x12ac);g_zUfiSms_SendingSms.TP_UD[(0x254+2607-0xc83)]=
(0x40f+6908-0x1f06);g_zUfiSms_SendingSms.TP_UD[(0x12c2+3885-0x21ee)]=
(0x1f8a+951-0x233c);g_zUfiSms_SendingSms.TP_UD[(0xbc1+4047-0x1b8e)]=
(0x10fc+3475-0x1e8c);g_zUfiSms_SendingSms.TP_UD[(0x986+963-0xd46)]=(char)
ptDbSaveData->concat_info[(0xa3a+7119-0x2609)]%(0x1371+3058-0x1e64);
g_zUfiSms_SendingSms.TP_UD[(0x5bc+6209-0x1df9)]=(char)ptConcatSms->total_msg;
g_zUfiSms_SendingSms.TP_UD[(0x1622+1297-0x1b2e)]=(char)ptConcatSms->
current_sending+(0x394+4406-0x14c9);(void)Bytes2String(ptSubmit->user_data.
sm_data,&g_zUfiSms_SendingSms.TP_UD[(0x1df7+1210-0x22ab)],ptSubmit->user_data.
sm_len);}else{(void)Bytes2String(ptSubmit->user_data.sm_data,
g_zUfiSms_SendingSms.TP_UD,ptSubmit->user_data.sm_len);}}unsigned char 
zUfiSms_Low2High(unsigned char x){if(x>=((char)(0x518+2256-0xd87))&&x<
((char)(0xaf0+1088-0xec9))){x=(x-((char)(0x182d+1028-0x1bd0)))+
((char)(0x665+7702-0x243a));}return x;}unsigned char zUfiSms_Char2Dec(unsigned 
char x){unsigned char d=(0x4c6+8234-0x24f0);if(x>=((char)(0x712+4208-0x1741))&&x
<((char)(0x510+1996-0xc95))){d=(x-((char)(0x50f+359-0x635)))+(0x1e4+2036-0x9ce);
}else{d=x-((char)(0x431+6142-0x1bff));}return d;}unsigned char zUfiSms_Char2Byte
(unsigned char a,unsigned char b){unsigned char data=(0x10e2+3418-0x1e3c);
unsigned char l=(0xd8c+4976-0x20fc),h=(0xef2+805-0x1217);a=zUfiSms_Low2High(a);b
=zUfiSms_Low2High(b);h=zUfiSms_Char2Dec(a);l=zUfiSms_Char2Dec(b);data=h*
(0xbc7+256-0xcb7)+l;return data;}void zUfiSms_Str2Bytes(unsigned char*text,int 
text_len,unsigned char*data,int data_len){int i=(0x121a+3118-0x1e48);while(
(0x1d7f+925-0x211a)*i+(0x262+4-0x265)<text_len){data[i]=zUfiSms_Char2Byte(text[
(0x589+4428-0x16d3)*i],text[(0x8e3+3569-0x16d2)*i+(0x17aa+1761-0x1e8a)]);i++;}}
int zUfiSms_SplitString(char*input,char***output,char cMatchChar){int src=
(0x18d+8444-0x2289);int dst=(0x4a8+2855-0xfcf);int count=(0x1ee6+175-0x1f95);int
 size=(0xb4f+407-0xcde);char quoted=(0x454+6509-0x1dc1);char**tmpout=NULL;*
output=(char**)malloc(sizeof(char*)*size);if(NULL==*output){return-
(0x1285+2894-0x1dd2);}(*output)[count++]=input;for(src=dst=(0x1d80+1898-0x24ea);
input[src];){char cInputChar=input[src];if(!quoted&&cInputChar==cMatchChar){
input[dst++]=(0x1c78+692-0x1f2c);while(input[++src]&&(int)isspace(input[src])){}
;if(count>=size){size+=(0xa3c+245-0xb21);tmpout=(char**)realloc(*output,sizeof(
char*)*size);if(NULL==tmpout){return-(0x483+1229-0x94f);}*output=tmpout;}(*
output)[count++]=input+dst;}else if(!quoted&&(cInputChar=='\''||cInputChar==
((char)(0x193a+3387-0x2653)))){quoted=cInputChar;src++;}else if(cInputChar==
quoted){quoted=(0x11c1+1409-0x1742);src++;}else{if(cInputChar=='\\'&&quoted!=
'\''){src++;cInputChar=input[src];if(!cInputChar){free(*output);*output=NULL;
return-(0x9d4+3812-0x18b7);}}input[dst++]=cInputChar;src++;}}input[dst]=
(0x2c+1820-0x748);if(quoted){free(*output);*output=NULL;return-(0x485+878-0x7f2)
;}return count;}int zUfiSms_atohex(char c){int result=(0x11f0+4735-0x246f);if(c
>=((char)(0xaf2+6702-0x24f0))&&c<=((char)(0x212c+1098-0x253d))){result=c-
((char)(0x104+7016-0x1c3c));}else if(c>=((char)(0x35c+8870-0x25a1))&&c<=
((char)(0x1e4+9030-0x24c4))){result=(c-((char)(0x11d1+3840-0x2070)))+
(0x278+6976-0x1dae);}else if(c>=((char)(0x127+7201-0x1d07))&&c<=
((char)(0x5b4+6559-0x1f0d))){result=(c-((char)(0x203a+1049-0x2412)))+
(0x176d+500-0x1957);}else{at_print(LOG_DEBUG,
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x61\x74\x6f\x68\x65\x78\x20\x65\x72\x72\x6f\x72\x2c\x63\x61\x6e\x20\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x63\x68\x61\x72\x3a\x25\x63" "\n"
,c);return result;}return result;}int zUfiSms_DispatchWtoi(unsigned char*in_ptr,
int iLength,unsigned char*out_ptr){int low=(0x2189+241-0x227a);int high=
(0x1a73+559-0x1ca2);if(in_ptr==NULL||out_ptr==NULL){printf(
"\x73\x6d\x73\x3a\x69\x6e\x76\x61\x69\x6c\x64\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72" "\n"
);return ZUFI_FAIL;}while(iLength>(0x153+6179-0x1976)){low=in_ptr[iLength-
(0x275+537-0x48d)]&(0xa69+6104-0x2232);high=(in_ptr[iLength-(0x43d+823-0x773)]&
(0x19ff+3410-0x2661))>>(0xe2+593-0x32f);out_ptr[(0xa45+5902-0x2151)*iLength-
(0x54b+4097-0x154b)]=g_zUfiSms_DigAscMap[low];out_ptr[(0xbc6+1328-0x10f4)*
iLength-(0x710+4789-0x19c3)]=g_zUfiSms_DigAscMap[high];iLength--;}return 
ZUFI_SUCC;}unsigned int zte_wms_convert_PORTUGUESE_To_UCS2(const unsigned char*
gsmdef,unsigned char*ucs2,unsigned int len){unsigned int i=(0x9bc+4062-0x199a);
unsigned int j=(0x160+248-0x258);unsigned int k=(0x1b28+1375-0x2087);unsigned 
int p=(0x1de9+2218-0x2693);unsigned int s=(0x169a+865-0x19fb);s=sizeof(
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex)/sizeof(
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[(0x610+3450-0x138a)]);for(i=
(0x20d2+999-0x24b9);i<len;i++){j=gsmdef[i];if(j==(0x193+2038-0x96e)){i++;for(p=
(0x34a+3182-0xfb8);p<s;p++){if(zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[p][
(0x21cb+1083-0x2606)]==gsmdef[i]){ucs2[k]=
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[p][(0x283+4905-0x15ab)]>>
(0x185+6568-0x1b25);k++;ucs2[k]=(unsigned char)(
zte_sms_GSM7_PORTUGUESE_To_UCS2_Table_Ex[p][(0x4a1+7266-0x2102)]);break;}}}else{
ucs2[k]=zte_sms_GSM7_PORTUGUESE_To_UCS2_Table[j]>>(0x913+3691-0x1776);k++;ucs2[k
]=(unsigned char)(zte_sms_GSM7_PORTUGUESE_To_UCS2_Table[j]);}k++;}ucs2[k]='\0';
return k;}unsigned int zte_wms_convert_PORTUGUESE_To_UCS2_USE_GSM7_SS_PORTU_LS(
const unsigned char*gsmdef,unsigned char*ucs2,unsigned int len){unsigned int i=
(0x6a8+1030-0xaae);unsigned int j=(0x346+4609-0x1547);unsigned int k=
(0xa53+6844-0x250f);unsigned int p=(0xeeb+2282-0x17d5);unsigned int s=
(0x4fb+3827-0x13ee);s=sizeof(zte_sms_GSMDefault_To_UCS2_Table_Ex)/sizeof(
zte_sms_GSMDefault_To_UCS2_Table_Ex[(0x199a+28-0x19b6)]);for(i=
(0x2532+281-0x264b);i<len;i++){j=gsmdef[i];if(j==(0x13f5+1837-0x1b07)){i++;for(p
=(0x836+5515-0x1dc1);p<s;p++){if(zte_sms_GSMDefault_To_UCS2_Table_Ex[p][
(0x24da+211-0x25ad)]==gsmdef[i]){ucs2[k]=zte_sms_GSMDefault_To_UCS2_Table_Ex[p][
(0x1036+2233-0x18ee)]>>(0x1be2+1486-0x21a8);k++;ucs2[k]=(unsigned char)(
zte_sms_GSMDefault_To_UCS2_Table_Ex[p][(0x1a9+7056-0x1d38)]);break;}}}else{ucs2[
k]=zte_sms_GSM7_PORTUGUESE_To_UCS2_Table[j]>>(0x527+2601-0xf48);k++;ucs2[k]=(
unsigned char)(zte_sms_GSM7_PORTUGUESE_To_UCS2_Table[j]);}k++;}ucs2[k]='\0';
return k;}unsigned long zte_wms_convert_GSMDefault_to_UCS2(const unsigned char*
gsmdef,unsigned char*ucs2,unsigned long len){unsigned long i=(0x8a+5855-0x1769);
unsigned long j=(0x1034+288-0x1154);unsigned long k=(0x7ef+4948-0x1b43);unsigned
 long p=(0x125c+288-0x137c);unsigned long s=(0xe8a+2925-0x19f7);unsigned long 
is_find=(0x1db4+1152-0x2234);s=sizeof(zte_sms_GSMDefault_To_UCS2_Table_Ex)/
sizeof(zte_sms_GSMDefault_To_UCS2_Table_Ex[(0xc2f+4396-0x1d5b)]);for(i=
(0x7e3+3729-0x1674);i<len;i++){j=gsmdef[i];if(j==(0x107+1101-0x539)){i++;for(p=
(0x2337+791-0x264e);p<s;p++){if(zte_sms_GSMDefault_To_UCS2_Table_Ex[p][
(0x1f9+8387-0x22bc)]==gsmdef[i]){ucs2[k]=zte_sms_GSMDefault_To_UCS2_Table_Ex[p][
(0x641+1660-0xcbc)]>>(0x1065+2084-0x1881);k++;ucs2[k]=(unsigned char)(
zte_sms_GSMDefault_To_UCS2_Table_Ex[p][(0xfa2+5499-0x251c)]);is_find=
(0x550+3588-0x1353);break;}}if(!is_find){at_print(LOG_DEBUG,
"\x73\x6d\x73\x3a\x20\x64\x61\x74\x61\x20\x3d\x20\x25\x64\x20\x6e\x6f\x74\x20\x66\x69\x6e\x64\x20\x69\x6e\x20\x67\x73\x6d\x64\x65\x66\x61\x75\x6c\x74\x20\x65\x78\x74\x65\x6e\x73\x69\x6f\x6e\x20\x74\x61\x62\x6c\x65" "\n"
,gsmdef[i]);i--;ucs2[k]=zte_sms_GSMDefault_To_UCS2_Table[j]>>
(0x1b3c+1315-0x2057);k++;ucs2[k]=(unsigned char)(
zte_sms_GSMDefault_To_UCS2_Table[j]);}}else{ucs2[k]=
zte_sms_GSMDefault_To_UCS2_Table[j]>>(0x1982+1129-0x1de3);k++;ucs2[k]=(unsigned 
char)(zte_sms_GSMDefault_To_UCS2_Table[j]);}k++;}ucs2[k]='\0';return k;}void 
zUfiSms_ConvertUcs2(char*data,UINT16 sms_len,char*out_content){char 
ascii_content[(0xa43+5542-0x1fe7)*ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX+
(0x1ac0+821-0x1df4)]={(0x80a+7178-0x2414)};UINT16 len=(0x320+6280-0x1ba8);switch
(g_zUfiSms_Language){case DCS_PORTUGUESE:if(g_zUfiSms_IsLanguageShift==
WMS_UDH_NAT_LANG_SS){len=zte_wms_convert_PORTUGUESE_To_UCS2((const UINT8*)data,(
UINT8*)ascii_content,sms_len);}else if(g_zUfiSms_IsLanguageShift==
WMS_UDH_NAT_LANG_LS){len=zte_wms_convert_PORTUGUESE_To_UCS2_USE_GSM7_SS_PORTU_LS
((const UINT8*)data,(UINT8*)ascii_content,sms_len);}break;case DCS_USC:default:
len=zte_wms_convert_GSMDefault_to_UCS2((const UINT8*)data,(UINT8*)ascii_content,
sms_len);break;}(void)zUfiSms_DispatchWtoi((char*)ascii_content,len,(char*)
out_content);}boolean zUfiSms_DecodeContent(char*msg_content,UINT16 sms_len,
boolean isEsc,char*out_content){boolean endEsc=FALSE;char*p=NULL;static char 
data[(0x11fa+3032-0x1dd1)+(0xf00+1912-0x15d8)+(0xb32+6571-0x24dc)]={
(0x197+8036-0x20fb)};int len=(0x1db6+1153-0x2237);if(msg_content==NULL||
out_content==NULL||sms_len>(0xb2f+5656-0x2007)||sms_len<(0x18dd+2505-0x22a4)){
return endEsc;}len=sms_len;memset(data,(0xa48+6967-0x257f),(0xfd9+3718-0x1dbd));
p=data;if(isEsc){*p=(0x14a+1300-0x643);p++;}zUfiSms_Str2Bytes((unsigned char*)
msg_content,len,(unsigned char*)p,(0xfac+5206-0x2362));if(p[len/
(0xbe6+1040-0xff4)-(0xf29+4207-0x1f97)]==(0x1825+1453-0x1db7)){endEsc=TRUE;}
zUfiSms_ConvertUcs2(data,len/(0x215+7560-0x1f9b)+(isEsc?(0xcdc+4050-0x1cad):
(0x792+7865-0x264b))-(endEsc?(0x730+6537-0x20b8):(0x15f5+1942-0x1d8b)),
out_content);return endEsc;}byte*zUfiSms_SmsiUtilitoa(uint32 v,byte*s,UINT16 r){
byte buf[(0x69c+5059-0x1a3e)],c;int n;n=sizeof(buf)-(0x516+5284-0x19b9);buf[n]=
'\0';do{c=(byte)(v%r);if(n<=(0xaf9+3537-0x18ca)){printf(
"\x4f\x56\x45\x52\x46\x4c\x4f\x57\x20");break;}buf[--n]=(byte)((c>
(0x273+1984-0xa2a))?c+((char)(0x219+4282-0x1292))-(0x1e6d+1296-0x2373):c+
((char)(0x1e5f+1719-0x24e6)));}while((v/=r)>(0x1a45+535-0x1c5c));while((*s++=buf
[n++])!=(0x1348+1815-0x1a5f));return(s-(0x24c+8976-0x255b));}byte*
zUfiSms_SmsiAddrToStr(wms_address_s_type addr,byte*res_ptr,UINT8*type_of_addr){
byte bcd_idx=(0x19bb+161-0x1a5c);UINT8 temp=(0x1de3+1267-0x22d6);*type_of_addr=
(0x141d+99-0x1480);temp=(UINT8)((uint32)addr.number_type&(0xb2+186-0x165));*
type_of_addr=(UINT8)((*type_of_addr|temp)<<(0x9b7+7077-0x2558));temp=(UINT8)((
uint32)addr.number_plan&(0x2080+960-0x2439));*type_of_addr=*type_of_addr|temp;*
type_of_addr=*type_of_addr|(0x13ca+2877-0x1e87);while(bcd_idx<addr.
number_of_digits){if(addr.digits[bcd_idx]==(0xb26+1098-0xf66)){addr.digits[
bcd_idx]=(0xd8a+2812-0x1886);}res_ptr=zUfiSms_SmsiUtilitoa((uint32)addr.digits[
bcd_idx],res_ptr,(0x562+1041-0x963));bcd_idx++;}return res_ptr;}byte*
zUfiSms_SmsiUtilitoaFill(word v,byte*rb_ptr){int n;byte c,*ptr;ptr=rb_ptr+
(0x3a2+3076-0xfa4);*ptr='\0';for(n=(0x1111+4987-0x248c);n<(0x2a5+7512-0x1ffb);++
n){c=(byte)(v%(0x1bcd+666-0x1e5d));v/=(0x250d+263-0x260a);*--ptr=(c+
((char)(0x6a4+2510-0x1042)));}return rb_ptr+(0x1aa3+2942-0x261f);}void 
zUfiSms_SprintfTime(char*str_time,int len,int t){if(t<(0x16c9+2059-0x1eca)){
snprintf(str_time,len,"\x30\x25\x78",t);}else{snprintf(str_time,len,"\x25\x78",t
);}}static void zUfiSms_ParseDeliverConcat8(T_zUfiSms_UdhConcat8*concat_8,
T_zUfiSms_DbStoreData*db_data){int mux=(0x8ef+6921-0x23f8);concat_8->seq_num--;
if(concat_8->total_sm>ZTE_WMS_CONCAT_SMS_COUNT_MAX){mux=(concat_8->seq_num-
concat_8->seq_num%ZTE_WMS_CONCAT_SMS_COUNT_MAX)/ZTE_WMS_CONCAT_SMS_COUNT_MAX;}
db_data->concat_sms=(0x1609+1570-0x1c2a);db_data->concat_info[
(0x22c4+1010-0x26b6)]=(0xf09+4065-0x1deb)*mux+concat_8->msg_ref;db_data->
concat_info[(0x111b+4282-0x21d3)]=concat_8->seq_num%ZTE_WMS_CONCAT_SMS_COUNT_MAX
+(0x138c+2681-0x1e04);db_data->concat_info[(0x103d+3129-0x1c75)]=concat_8->
total_sm-ZTE_WMS_CONCAT_SMS_COUNT_MAX*mux>ZTE_WMS_CONCAT_SMS_COUNT_MAX-
(0x1544+4468-0x26b7)?ZTE_WMS_CONCAT_SMS_COUNT_MAX:(concat_8->total_sm%
ZTE_WMS_CONCAT_SMS_COUNT_MAX);}static void zUfiSms_ParserLangSs(wms_udh_s_type*
user_data_header){if(user_data_header==NULL){return;}switch(user_data_header->u.
nat_lang_ss.nat_lang_id){case WMS_UDH_NAT_LANG_PORTUGUESE:g_zUfiSms_Language=
DCS_PORTUGUESE;break;default:break;}}static void zUfiSms_ParserLangLs(
T_zUfiSms_Udh*user_data_header){if(user_data_header==NULL){return;}switch(
user_data_header->u.nat_lang_ss.nat_lang_id){case WMS_UDH_NAT_LANG_PORTUGUESE:
g_zUfiSms_Language=DCS_PORTUGUESE;break;default:break;}}static void 
zUfiSms_ParseDeliverConcat16(T_zUfiSms_UdhConcat16*concat_16,
T_zUfiSms_DbStoreData*db_data){int mux=(0x22f0+687-0x259f);concat_16->seq_num--;
db_data->concat_sms=(0xda3+4908-0x20ce);
#if (0x26c+3405-0xfb9)
db_data->concat_info[(0x8e6+7706-0x2700)]=concat_16->msg_ref;db_data->
concat_info[(0xa23+6572-0x23ce)]=concat_16->total_sm;db_data->concat_info[
(0x11d0+1230-0x169c)]=concat_16->seq_num;
#endif
if(concat_16->total_sm>ZTE_WMS_CONCAT_SMS_COUNT_MAX){mux=(concat_16->seq_num-
concat_16->seq_num%ZTE_WMS_CONCAT_SMS_COUNT_MAX)/ZTE_WMS_CONCAT_SMS_COUNT_MAX;}
db_data->concat_info[(0x11fd+2105-0x1a36)]=(0xff2+1268-0x13e7)*mux+concat_16->
msg_ref;db_data->concat_info[(0x7f1+238-0x8dd)]=concat_16->seq_num%
ZTE_WMS_CONCAT_SMS_COUNT_MAX+(0xe02+4793-0x20ba);db_data->concat_info[
(0x10eb+2506-0x1ab4)]=concat_16->total_sm-ZTE_WMS_CONCAT_SMS_COUNT_MAX*mux>
ZTE_WMS_CONCAT_SMS_COUNT_MAX-(0xc36+2666-0x169f)?ZTE_WMS_CONCAT_SMS_COUNT_MAX:(
concat_16->total_sm%ZTE_WMS_CONCAT_SMS_COUNT_MAX);}static int 
zUfiSms_ParseUdhiData(T_zUfiSms_Udh*user_data_header,T_zUfiSms_DbStoreData*
db_data){if(NULL==user_data_header||NULL==db_data){printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73\x2e");return ZUFI_FAIL
;}switch(user_data_header->header_id){case WMS_UDH_CONCAT_8:db_data->concat_sms=
(0x2434+470-0x2609);zUfiSms_ParseDeliverConcat8(&(user_data_header->u.concat_8),
db_data);break;case WMS_UDH_CONCAT_16:db_data->concat_sms=(0x938+1226-0xe01);
zUfiSms_ParseDeliverConcat16(&(user_data_header->u.concat_16),db_data);break;
case WMS_UDH_NAT_LANG_SS:g_zUfiSms_IsLanguageShift=WMS_UDH_NAT_LANG_SS;
zUfiSms_ParserLangSs(user_data_header);break;case WMS_UDH_NAT_LANG_LS:
g_zUfiSms_IsLanguageShift=WMS_UDH_NAT_LANG_LS;zUfiSms_ParserLangLs(
user_data_header);break;default:printf(
"\x6e\x6f\x74\x20\x73\x75\x70\x70\x6f\x72\x74\x20\x74\x68\x6f\x73\x65\x20\x75\x73\x65\x72\x20\x68\x65\x61\x64\x65\x72"
);break;}return ZUFI_SUCC;}int zUfiSms_FormatDeliverDbdata(
T_zUfiSms_ClientTsData*ts_data_ptr,T_zUfiSms_DbStoreData*db_data){int result=
ZUFI_SUCC;wms_address_s_type*address_ptr=NULL;wms_gw_alphabet_e_type tp_dcs=
WMS_GW_ALPHABET_MAX32;int i=(0xce8+3333-0x19ed);int ind=(0x1746+200-0x180e);if((
NULL==ts_data_ptr)||(NULL==db_data)){printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73\x2e");return ZUFI_FAIL
;}address_ptr=&(ts_data_ptr->u.gw_pp.u.deliver.address);if(
WMS_NUMBER_INTERNATIONAL==address_ptr->number_type){memset(db_data->number,
(0xdaa+1007-0x1199),ZTE_WMS_ADDRESS_LEN_MAX+(0x4e5+1378-0xa46));db_data->number[
(0xe43+5348-0x2327)]=((char)(0x53+4034-0xfea));for(i=(0x540+7037-0x20bd);i<
address_ptr->number_of_digits;i++){if((0x10a5+443-0x1256)==address_ptr->digits[i
]){db_data->number[i+(0x210d+226-0x21ee)]=((char)(0x374+7027-0x1eb7));}else{
db_data->number[i+(0x8b2+4737-0x1b32)]=((char)(0x1dd+855-0x504))+address_ptr->
digits[i];}}}else if(WMS_NUMBER_ALPHANUMERIC==address_ptr->number_type){memcpy(
db_data->number,address_ptr->digits,address_ptr->number_of_digits);}else{if(
address_ptr->digit_mode==WMS_DIGIT_MODE_8_BIT){memcpy(&(db_data->number[
(0x136c+3698-0x21dd)]),address_ptr->digits,address_ptr->number_of_digits);}else{
for(i=(0x587+8043-0x24f2);i<address_ptr->number_of_digits;i++){if(
(0xbb4+2750-0x1668)==address_ptr->digits[i]){db_data->number[i]=
((char)(0x2ac+907-0x607));}else{db_data->number[i]=((char)(0x169+6281-0x19c2))+
address_ptr->digits[i];}}}}(void)zUfiSms_UtilTimeStamp(ts_data_ptr->u.gw_pp.u.
deliver.timestamp,db_data->tp_scts,&db_data->julian_date);if(ts_data_ptr->u.
gw_pp.u.deliver.user_data_header_present){db_data->tp_udhi=(0xba9+2801-0x1699);}
else{db_data->tp_udhi=(0x567+8179-0x255a);}if(db_data->tp_udhi==
(0x11d0+350-0x132d)){for(ind=(0xc7f+4562-0x1e51);ind<ts_data_ptr->u.gw_pp.u.
deliver.user_data.num_headers;ind++){result=zUfiSms_ParseUdhiData(&(ts_data_ptr
->u.gw_pp.u.deliver.user_data.headers[ind]),db_data);if(
ZTE_WMS_CONCAT_SMS_COUNT_MAX<db_data->concat_info[(0x6a5+7396-0x2388)]){printf(
"\x74\x68\x65\x20\x63\x6f\x6e\x63\x61\x74\x20\x73\x6d\x73\x20\x73\x65\x67\x6d\x65\x6e\x74\x20\x69\x73\x20\x25\x64\x2c\x61\x6e\x64\x20\x6c\x61\x72\x67\x65\x72\x20\x74\x68\x65\x6e\x20\x74\x68\x65\x20\x20\x73\x75\x70\x70\x6f\x72\x74\x65\x64\x20\x25\x64\x20\x73\x65\x67\x6d\x65\x6e\x74\x73\x2c\x73\x6f\x20\x64\x69\x64\x20\x6e\x6f\x74\x20\x64\x65\x61\x6c\x20\x74\x68\x65\x20\x63\x6f\x6e\x63\x61\x74\x20\x73\x6d\x73\x2e\x20"
,db_data->concat_info[(0xcbf+5689-0x22f7)],ZTE_WMS_CONCAT_SMS_COUNT_MAX);if(
WMS_STORAGE_TYPE_NV_V01==db_data->mem_store){zUfiSms_DelModemSms(db_data->index)
;}result=ZUFI_FAIL;}}}tp_dcs=ts_data_ptr->u.gw_pp.u.deliver.dcs.alphabet;db_data
->sms_class=ts_data_ptr->u.gw_pp.u.deliver.dcs.msg_class;if(
WMS_GW_ALPHABET_8_BIT>=tp_dcs){db_data->tp_dcs=(unsigned char)
(0x4f5+8340-0x2588);}else if(WMS_GW_ALPHABET_UCS2==tp_dcs){db_data->tp_dcs=(
unsigned char)(0x9b9+4086-0x19ad);}else{printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x74\x70\x5f\x64\x63\x73\x3d\x25\x64",tp_dcs);}
db_data->tp_pid=(unsigned char)ts_data_ptr->u.gw_pp.u.deliver.pid;if(ts_data_ptr
->u.gw_pp.u.deliver.dcs.alphabet==WMS_GW_ALPHABET_UCS2){result=
zUfiSms_DispatchWtoi(ts_data_ptr->u.gw_pp.u.deliver.user_data.sm_data,
ts_data_ptr->u.gw_pp.u.deliver.user_data.sm_len,db_data->sms_content);db_data->
alphabet=WMS_GW_ALPHABET_UCS2;}else if(ts_data_ptr->u.gw_pp.u.deliver.dcs.
alphabet==WMS_GW_ALPHABET_8_BIT){for(ind=(0x12b2+3573-0x20a7);ind<ts_data_ptr->u
.gw_pp.u.deliver.user_data.sm_len;ind++){db_data->sms_content[
(0x119+6371-0x19f8)*ind]=((char)(0x396+1473-0x927));db_data->sms_content[
(0x1010+3488-0x1dac)*ind+(0x101a+4769-0x22ba)]=((char)(0x7c8+5026-0x1b3a));
db_data->sms_content[(0x1a58+1670-0x20da)*ind+(0x84f+1089-0xc8e)]=
g_zUfiSms_DigAscMap[((ts_data_ptr->u.gw_pp.u.deliver.user_data.sm_data[ind]&
(0x1e54+1821-0x2481))>>(0x1174+1489-0x1741))];db_data->sms_content[
(0xce2+6390-0x25d4)*ind+(0x1b7b+23-0x1b8f)]=g_zUfiSms_DigAscMap[(ts_data_ptr->u.
gw_pp.u.deliver.user_data.sm_data[ind]&(0x64f+335-0x78f))];db_data->alphabet=
WMS_GW_ALPHABET_UCS2;}}else if(ts_data_ptr->u.gw_pp.u.deliver.dcs.alphabet==
WMS_GW_ALPHABET_7_BIT_DEFAULT){result=zUfiSms_DispatchWtoi(ts_data_ptr->u.gw_pp.
u.deliver.user_data.sm_data,ts_data_ptr->u.gw_pp.u.deliver.user_data.sm_len,
db_data->sms_content);db_data->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;db_data->
tp_dcs=(unsigned char)(0xe8d+5523-0x241e);}return result;}int 
zUfiSms_FormatSubmitDbdata(T_zUfiSms_ClientTsData*ts_data_ptr,
T_zUfiSms_DbStoreData*db_data){int result=ZUFI_SUCC;wms_address_s_type*
address_ptr=NULL;wms_gw_alphabet_e_type tp_dcs=WMS_GW_ALPHABET_MAX32;int i=
(0x11fa+4825-0x24d3);int ind=(0x633+3396-0x1377);if((NULL==ts_data_ptr)||(NULL==
db_data)){printf("\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73\x2e")
;return ZUFI_FAIL;}address_ptr=&(ts_data_ptr->u.gw_pp.u.submit.address);if((
WMS_NUMBER_INTERNATIONAL==address_ptr->number_type)){db_data->number[
(0x552+6765-0x1fbf)]=((char)(0x104a+3707-0x1e9a));for(i=(0xd13+4240-0x1da3);i<
address_ptr->number_of_digits;i++){if((0x1748+2252-0x200a)==address_ptr->digits[
i]){db_data->number[i+(0x1434+36-0x1457)]=((char)(0x1253+1823-0x1942));}else{
db_data->number[i+(0x99a+263-0xaa0)]=((char)(0xfdd+3413-0x1d02))+address_ptr->
digits[i];}}}else{for(i=(0x522+2610-0xf54);i<address_ptr->number_of_digits;i++){
if((0x856+63-0x88b)==address_ptr->digits[i]){db_data->number[i]=
((char)(0x4ec+8173-0x24a9));}else{db_data->number[i]=((char)(0x2451+206-0x24ef))
+address_ptr->digits[i];}}}tp_dcs=ts_data_ptr->u.gw_pp.u.submit.dcs.alphabet;
db_data->sms_class=ts_data_ptr->u.gw_pp.u.submit.dcs.msg_class;if(
WMS_GW_ALPHABET_8_BIT>=tp_dcs){db_data->tp_dcs=(unsigned char)(0x136+2074-0x94f)
;}else if(WMS_GW_ALPHABET_UCS2==tp_dcs){db_data->tp_dcs=(unsigned char)
(0x10c8+5249-0x2547);}else{printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x74\x70\x5f\x64\x63\x73\x3d\x25\x64",tp_dcs);}
db_data->tp_pid=(unsigned char)ts_data_ptr->u.gw_pp.u.submit.pid;db_data->
msg_ref=(unsigned char)ts_data_ptr->u.gw_pp.u.submit.message_reference;if(
ts_data_ptr->u.gw_pp.u.submit.user_data_header_present){db_data->tp_udhi=
(0x15a5+1406-0x1b22);}else{db_data->tp_udhi=(0x7e9+7166-0x23e7);}if(db_data->
tp_udhi==(0x3fb+6314-0x1ca4)){for(ind=(0x1d3a+2284-0x2626);ind<ts_data_ptr->u.
gw_pp.u.submit.user_data.num_headers;ind++){result=zUfiSms_ParseUdhiData(&(
ts_data_ptr->u.gw_pp.u.submit.user_data.headers[ind]),db_data);if(
ZTE_WMS_CONCAT_SMS_COUNT_MAX<db_data->concat_info[(0x191a+1840-0x2049)]){printf(
"\x74\x68\x65\x20\x63\x6f\x6e\x63\x61\x74\x20\x73\x6d\x73\x20\x73\x65\x67\x6d\x65\x6e\x74\x20\x69\x73\x20\x6c\x61\x72\x67\x65\x72\x20\x74\x68\x65\x6e\x20\x74\x68\x65\x20\x20\x73\x75\x70\x70\x6f\x72\x74\x65\x64\x20\x73\x65\x67\x6d\x65\x6e\x74\x73\x2c\x73\x6f\x20\x64\x69\x64\x20\x6e\x6f\x74\x20\x64\x65\x61\x6c\x20\x74\x68\x65\x20\x63\x6f\x6e\x63\x61\x74\x20\x73\x6d\x73\x2e"
);if(WMS_STORAGE_TYPE_NV_V01==db_data->mem_store){zUfiSms_DelModemSms(db_data->
index);}result=ZUFI_FAIL;}}}if(ts_data_ptr->u.gw_pp.u.submit.dcs.alphabet==
WMS_GW_ALPHABET_UCS2){result=zUfiSms_DispatchWtoi(ts_data_ptr->u.gw_pp.u.submit.
user_data.sm_data,ts_data_ptr->u.gw_pp.u.submit.user_data.sm_len,db_data->
sms_content);db_data->alphabet=WMS_GW_ALPHABET_UCS2;}else if(ts_data_ptr->u.
gw_pp.u.submit.dcs.alphabet==WMS_GW_ALPHABET_8_BIT){for(ind=(0x96f+2450-0x1301);
ind<ts_data_ptr->u.gw_pp.u.submit.user_data.sm_len;ind++){db_data->sms_content[
(0x1680+2639-0x20cb)*ind]=((char)(0x1698+562-0x189a));db_data->sms_content[
(0x74d+3053-0x1336)*ind+(0x13cb+2642-0x1e1c)]=((char)(0x1244+2710-0x1caa));
db_data->sms_content[(0x2235+1092-0x2675)*ind+(0xb05+6268-0x237f)]=
g_zUfiSms_DigAscMap[((ts_data_ptr->u.gw_pp.u.submit.user_data.sm_data[ind]&
(0x1998+910-0x1c36))>>(0x2368+244-0x2458))];db_data->sms_content[
(0x22+5321-0x14e7)*ind+(0xa1f+487-0xc03)]=g_zUfiSms_DigAscMap[(ts_data_ptr->u.
gw_pp.u.submit.user_data.sm_data[ind]&(0xcc3+5357-0x21a1))];db_data->alphabet=
WMS_GW_ALPHABET_UCS2;}}else if(ts_data_ptr->u.gw_pp.u.submit.dcs.alphabet==
WMS_GW_ALPHABET_7_BIT_DEFAULT){result=zUfiSms_DispatchWtoi(ts_data_ptr->u.gw_pp.
u.submit.user_data.sm_data,ts_data_ptr->u.gw_pp.u.submit.user_data.sm_len,
db_data->sms_content);db_data->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;db_data->
tp_dcs=(unsigned char)(0x653+2192-0xee1);}return result;}int InvertNumbers(const
 char*pSrc,char*pDst,int nSrcLength){int nDstLength;char ch;int i=
(0x1783+1047-0x1b9a);if(pSrc==NULL||pDst==NULL||nSrcLength<(0x86f+2423-0x11e6)){
return-(0x313+5805-0x19bf);}nDstLength=nSrcLength;for(i=(0xa82+1004-0xe6e);i<
nSrcLength;i+=(0xb3+9429-0x2586)){ch=*pSrc++;*pDst++=*pSrc++;*pDst++=ch;}if(
nSrcLength&(0x1fc+922-0x595)){*(pDst-(0x3c0+663-0x655))=
((char)(0x575+1971-0xce2));nDstLength++;}*pDst='\0';return nDstLength;}int 
code_is_gsm7(const SMS_PARAM*pSrc,unsigned char buf[],int nLength){if(pSrc->
TP_UDHI==(0xaea+2243-0x13ac)){buf[(0x1857+3369-0x257d)]=(unsigned char)nLength;
buf[(0x67a+6333-0x1f33)]=(unsigned char)pSrc->TP_UD[(0x24c6+121-0x253f)];buf[
(0x692+7651-0x2470)]=(0x2fb+6562-0x1c9d);buf[(0x5da+3776-0x1494)]=(unsigned char
)pSrc->TP_UD[(0x1672+3980-0x25fc)];buf[(0x2246+388-0x23c3)]=(unsigned char)pSrc
->TP_UD[(0x5ed+1547-0xbf5)];buf[(0x25+2431-0x99c)]=(unsigned char)pSrc->TP_UD[
(0x1e8+4148-0x1218)];buf[(0x1bff+1218-0x20b8)]=(unsigned char)pSrc->TP_UD[
(0x4eb+2510-0xeb4)];buf[(0xa15+386-0xb8d)]=(unsigned char)pSrc->TP_UD[
(0x10d0+5335-0x25a0)];buf[(0x8e2+2148-0x113c)]=(unsigned char)(buf[
(0x2595+347-0x26e6)]<<(0x745+949-0xaf9));nLength=nLength-(0xfd+7259-0x1d51);
nLength=Encode7bit(&(pSrc->TP_UD[(0x2d6+4751-0x155d)]),&buf[(0x2ab+663-0x537)],
nLength+(0x436+787-0x748))+(0x1365+3510-0x2117)+(0xfaa+2630-0x19ea);
#if (0x1547+1229-0x1a14)
nLength+=(0x924+6922-0x242c);
#endif
}else{nLength=pSrc->TP_UDLength;buf[(0x23ca+677-0x266c)]=nLength;nLength=
Encode7bit(pSrc->TP_UD,&buf[(0xdd2+2844-0x18ea)],nLength+(0x155b+1378-0x1abc))+
(0x758+6549-0x20e9);}at_print(LOG_DEBUG,
"\x62\x75\x66\x20\x69\x73\x20\x25\x73" "\n",buf);return nLength;}int 
code_is_ucs2(const SMS_PARAM*pSrc,unsigned char buf[],int nLength){nLength=
strlen(pSrc->TP_UD);if(pSrc->TP_UDHI==(0x103+2242-0x9c4)){buf[
(0x3ab+5328-0x1878)]=(unsigned char)nLength;buf[(0x82f+2987-0x13d6)]=(unsigned 
char)pSrc->TP_UD[(0x63c+3650-0x147e)];buf[(0x185+1914-0x8fa)]=
(0xcb1+5050-0x206b);buf[(0x17ad+875-0x1b12)]=(unsigned char)pSrc->TP_UD[
(0xde6+3405-0x1b31)];buf[(0x1a68+1208-0x1f19)]=(unsigned char)pSrc->TP_UD[
(0x164+6816-0x1c01)];buf[(0x1b2f+565-0x1d5c)]=(unsigned char)pSrc->TP_UD[
(0xd8d+6393-0x2682)];buf[(0x14fc+64-0x1533)]=(unsigned char)pSrc->TP_UD[
(0x15b4+2294-0x1ea5)];buf[(0x93a+2210-0x11d9)]=(unsigned char)(EncodeUcs2(&(pSrc
->TP_UD[(0x1574+1217-0x1a2f)]),&buf[(0x346+37-0x361)],nLength-
(0x57b+6460-0x1eb1))+(0x5f2+7283-0x225f));nLength=buf[(0x135f+4783-0x260b)]+
(0x1304+4336-0x23f0);}else{buf[(0x269+7799-0x20dd)]=EncodeUcs2(pSrc->TP_UD,&buf[
(0x9bd+1543-0xfc0)],nLength);nLength=buf[(0x1553+1012-0x1944)]+(0x386+85-0x3d7);
}return nLength;}int Encode8bit(const char*pSrc,unsigned char*pDst,int 
nSrcLength){if(pSrc==NULL||pDst==NULL||nSrcLength<(0x9dd+109-0xa4a)){return-
(0x563+597-0x7b7);}memcpy(pDst,pSrc,nSrcLength);return nSrcLength;}int 
EncodePdu_Submit(const SMS_PARAM*pSrc,char*pDst){int nLength=
(0x1b56+1083-0x1f91);int nDstLength=(0x94b+5776-0x1fdb);unsigned char buf[
(0x919+6842-0x22d3)]={(0xc92+903-0x1019)};char tmpSCA[(0x754+3769-0x15e9)]={
(0x716+2858-0x1240)};int check_udl=(0x14cf+3675-0x232a);memset(tmpSCA,
(0x5c+3073-0xc5d),sizeof(tmpSCA));if(pSrc==NULL||pDst==NULL){return-
(0xbaf+4034-0x1b70);}
#if (0xbb9+967-0xf7f)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x20\x45\x6e\x63\x6f\x64\x65\x50\x64\x75\x5f\x53\x75\x62\x6d\x69\x74\x20\x6d\x61\x6b\x65\x20\x70\x64\x75\x20\x64\x61\x74\x61" "\n"
);printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x74\x70\x75\x64\x3a\x25\x73" "\n",pSrc
->TP_UD);printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x70\x64\x73\x74\x31\x3a\x25\x73" "\n",
pDst);printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x73\x63\x61\x3a\x25\x73" "\n",pSrc->
SCA);
#endif
nLength=strlen(pSrc->SCA);buf[(0x239+4763-0x14d4)]=(char)(((nLength)&
(0xfff+2595-0x1a21))==(0xa3b+2478-0x13e9)?(nLength):nLength+(0x11dd+3679-0x203b)
)/(0x284+7115-0x1e4d)+(0x5f2+5117-0x19ee);buf[(0x68c+528-0x89b)]=
(0x1638+1747-0x1c8a);strncpy(tmpSCA,pSrc->SCA,sizeof(tmpSCA)-(0x2bd+4504-0x1454)
);if(!(strncmp(pSrc->SCA,"\x30\x30\x38\x36",(0xfd2+911-0x135d)))){memset(tmpSCA,
(0xa9b+7264-0x26fb),sizeof(tmpSCA));nLength=nLength-(0xd04+3719-0x1b8a);
#if (0x795+5347-0x1c77)
nLength=nLength-(0x325+52-0x358);strncpy(tmpSCA,&(pSrc->SCA[(0x15dc+2806-0x20d0)
]),sizeof(tmpSCA)-(0x66a+8334-0x26f7));
#else
tmpSCA[(0x11b8+1379-0x171b)]=((char)(0xf6a+2661-0x19a4));strcpy(&(tmpSCA[
(0x3c3+8677-0x25a7)]),&(pSrc->SCA[(0x4b3+7151-0x20a0)]));
#endif
buf[(0x1e37+1202-0x22e9)]=(char)((nLength&(0xaaa+2944-0x1629))==
(0x1f65+1365-0x24ba)?nLength:nLength+(0x7cd+5552-0x1d7c))/(0x525+2673-0xf94)+
(0x1e62+1394-0x23d3);buf[(0x17b0+958-0x1b6d)]=(0x137+7001-0x1bff);}else if(
((char)(0x1a61+1983-0x21f5))==pSrc->SCA[(0x5f0+6441-0x1f19)]){memset(tmpSCA,
(0xb87+1741-0x1254),sizeof(tmpSCA));
#if (0x499+6094-0x1c66)
nLength=nLength-(0x1549+724-0x181c);strncpy(tmpSCA,&(pSrc->SCA[
(0x8f9+4502-0x1a8e)]),sizeof(tmpSCA)-(0x1740+3635-0x2572));
#else
strcpy(tmpSCA,pSrc->SCA);
#endif
buf[(0x1500+2934-0x2076)]=(char)((nLength&(0x411+4374-0x1526))==
(0x27a+192-0x33a)?(nLength):nLength+(0x859+630-0xace))/(0x589+8443-0x2682)+
(0x5d9+2407-0xf3f);buf[(0xf34+5290-0x23dd)]=(0x13b7+2130-0x1b78);}
#if (0x14d0+1439-0x1a6e)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x70\x64\x73\x74\x32\x3a\x25\x73" "\n",
pDst);
#endif
if(nLength<(0x40c+18-0x41d)||nLength>=sizeof(tmpSCA))return-(0xa32+6705-0x2462);
nDstLength=Bytes2String(buf,pDst,(0x590+1070-0x9bc));nDstLength+=InvertNumbers(
tmpSCA,&pDst[nDstLength],nLength);
#if (0x687+3857-0x1597)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x70\x64\x73\x74\x33\x3a\x25\x73" "\n",
pDst);
#endif
if(pSrc->TPA[(0x170+8025-0x20c9)]==((char)(0xa67+4400-0x1b6c))){nLength=strlen(&
(pSrc->TPA[(0x1637+2806-0x212c)]));}else{nLength=strlen(pSrc->TPA);}if(pSrc->
TP_UDHI==(0x809+2571-0x1214)){if(pSrc->TP_SRR==(0x10c8+2827-0x1bd3)){buf[
(0x1558+2070-0x1d6e)]=(0x1d2f+2055-0x2525);}if(pSrc->TP_SRR==(0x24f7+33-0x2517))
{buf[(0xf75+3227-0x1c10)]=(0x840+5538-0x1db1);}}if(pSrc->TP_UDHI==
(0x1b79+1810-0x228a)){if(pSrc->TP_SRR==(0xa86+1734-0x114c)){buf[
(0xb52+1218-0x1014)]=(0x1c8c+2576-0x264b);}if(pSrc->TP_SRR==(0x705+5906-0x1e16))
{buf[(0xc37+1631-0x1296)]=(0x1191+4247-0x21b7);}}buf[(0x17db+1900-0x1f46)]=
(0x211b+61-0x2158);buf[(0x38+863-0x395)]=(char)nLength;if(pSrc->TPA[
(0x158c+1652-0x1c00)]==((char)(0x756+4963-0x1a8e))){buf[(0xd9a+4607-0x1f96)]=
(0xcf7+5464-0x21be);nDstLength+=Bytes2String(buf,&pDst[nDstLength],
(0x98+8642-0x2256));nDstLength+=InvertNumbers(&(pSrc->TPA[(0xe78+3342-0x1b85)]),
&pDst[nDstLength],nLength);}else if(!(strncmp(pSrc->TPA,"\x30\x30\x38\x36",
(0xcd3+3373-0x19fc)))){buf[(0x1894+888-0x1c0a)]=(char)nLength-
(0xc21+3739-0x1aba);buf[(0xcc2+6346-0x2589)]=(0xfa8+5850-0x25f1);nDstLength+=
Bytes2String(buf,&pDst[nDstLength],(0x1364+93-0x13bd));nDstLength+=InvertNumbers
(&(pSrc->TPA[(0xfaf+1215-0x146c)]),&pDst[nDstLength],nLength-
(0x113b+1108-0x158d));}else{buf[(0x20c4+1334-0x25f7)]=(0x3d7+3275-0x1021);
nDstLength+=Bytes2String(buf,&pDst[nDstLength],(0x1362+4771-0x2601));nDstLength
+=InvertNumbers(pSrc->TPA,&pDst[nDstLength],nLength);}
#if (0x775+4131-0x1797)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x70\x64\x73\x74\x34\x3a\x25\x73" "\n",
pDst);
#endif
nLength=(int)pSrc->TP_UDLength;buf[(0x142b+647-0x16b2)]=pSrc->TP_PID;buf[
(0xd37+6290-0x25c8)]=pSrc->TP_DCS;buf[(0x13fb+4283-0x24b4)]=pSrc->TP_VP;if(pSrc
->TP_DCS==CODE_GSM7){nLength=code_is_gsm7(pSrc,buf,nLength);}else if(pSrc->
TP_DCS==CODE_UCS2){nLength=code_is_ucs2(pSrc,buf,nLength);}else{nLength=strlen(
pSrc->TP_UD);if(pSrc->TP_UDHI==(0x46c+3856-0x137b)){buf[(0x13fb+1492-0x19cc)]=(
unsigned char)nLength;buf[(0x1264+5146-0x267a)]=(unsigned char)pSrc->TP_UD[
(0x4ab+5735-0x1b12)];buf[(0xca5+405-0xe35)]=(0x199c+1954-0x213e);buf[
(0x54a+3402-0x128e)]=(unsigned char)pSrc->TP_UD[(0xb58+4593-0x1d47)];buf[
(0x19db+1046-0x1dea)]=(unsigned char)pSrc->TP_UD[(0x3df+1375-0x93b)];buf[
(0xf+3210-0xc91)]=(unsigned char)pSrc->TP_UD[(0x699+3953-0x1606)];buf[
(0x780+1935-0xf06)]=(unsigned char)pSrc->TP_UD[(0x615+6733-0x205d)];if(nLength-
(0x12c3+4093-0x22ba)<=(0x507+3140-0x114b)||nLength-(0x487+2065-0xc92)>=sizeof(
buf)-(0x1eed+611-0x2146))return-(0x919+4323-0x19fb);buf[(0xaf5+5986-0x2254)]=(
unsigned char)(Encode8bit(&(pSrc->TP_UD[(0x1d52+1425-0x22dd)]),&buf[
(0x2c3+7817-0x2142)],(unsigned short)(nLength-(0x15ec+2559-0x1fe5)))+
(0x121+8160-0x20fb));nLength=buf[(0xdb6+3364-0x1ad7)]+(0x6bd+3169-0x131a);}else{
if(nLength<=(0x11aa+3689-0x2013)||nLength>=sizeof(buf)-(0x1603+4163-0x2642))
return-(0xf09+2565-0x190d);buf[(0x1331+2048-0x1b2e)]=Encode8bit(pSrc->TP_UD,&buf
[(0x6a5+5064-0x1a69)],nLength);nLength=buf[(0x12da+4383-0x23f6)]+
(0x1720+3600-0x252c);}}check_udl=nLength-(0x13f6+2359-0x1d29);nDstLength+=
Bytes2String(buf,&pDst[nDstLength],nLength);sc_cfg_set(NV_CHECK_UDL,"");if(
check_udl>(0x1881+1332-0x1d29)){sc_cfg_set(NV_CHECK_UDL,"\x65\x72\x72\x6f\x72");
}
#if (0x1c6+1529-0x7be)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x20\x45\x6e\x63\x6f\x64\x65\x50\x64\x75\x5f\x53\x75\x62\x6d\x69\x74\x20\x65\x6e\x64\x20\x6d\x61\x6b\x65\x20\x70\x64\x75\x20\x64\x61\x74\x61" "\n"
);printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x6c\x65\x6e\x3a\x25\x64\x2c\x20\x74\x70\x75\x64\x3a\x25\x73" "\n"
,nDstLength,pSrc->TP_UD);
#endif
#if (0xca0+5564-0x225b)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x70\x64\x73\x74\x35\x3a\x25\x73" "\n",
pDst);
#endif
return nDstLength;}int Decode7bit(const unsigned char*pSrc,char*pDst,int 
nSrcLength){int nSrc;int nDst;int nByte;unsigned char nLeft;if(pSrc==NULL||pDst
==NULL||nSrcLength<(0x13cb+4665-0x2604)){return-(0x2016+979-0x23e8);}nSrc=
(0xc31+5809-0x22e2);nDst=(0x1a42+414-0x1be0);nByte=(0x1078+681-0x1321);nLeft=
(0xbc7+5171-0x1ffa);while(nSrc<nSrcLength){*pDst=((*pSrc<<nByte)|nLeft)&
(0x1793+1490-0x1ce6);nLeft=*pSrc>>((0x26c+4848-0x1555)-nByte);pDst++;nDst++;
nByte++;if(nByte==(0xb7d+5489-0x20e7)){*pDst=nLeft;pDst++;nDst++;nByte=
(0x72c+783-0xa3b);nLeft=(0x2c1+7432-0x1fc9);}pSrc++;nSrc++;}*pDst='\0';return 
nDst;}int DecodePushPdu(const char*pSrcPdu,SMS_PARAM*pDst){int nDstLength=
(0x4d0+329-0x619);unsigned char tmp=(0x2b2+4139-0x12dd);int ud_length=
(0x78b+5240-0x1c03);unsigned char buf[(0x1c75+118-0x1beb)]={(0xc99+569-0xed2)};
char temp_num[(0x1b19+179-0x1b68)]={(0x3db+637-0x658)};unsigned char first_octet
=(0x34b+5324-0x1817);unsigned char udhl=(0x756+1047-0xb6d);unsigned int halftmp=
(0x397+7624-0x215f);char tp_ra[(0x7b+9823-0x26d7)]={(0x1b4+8886-0x246a)};int 
tmplen=(0xb24+683-0xdcf);unsigned char IEIDL;int pushType=(0xff2+57-0x102b);
const char*pSrc=pSrcPdu;if(pSrcPdu==NULL||pDst==NULL){printf(
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x70\x61\x72\x61\x20\x6e\x75\x6c\x6c\x2e\x20" "\n"
);return-(0x129f+2609-0x1ccf);}String2Bytes(pSrc,&tmp,(0x911+424-0xab7));
at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x74\x6d\x70\x20\x3d\x20\x25\x64\x2e" "\n"
,tmp);if(tmp==(0x284+3306-0xf6e)){pSrc+=(0x53+489-0x23a);}else{tmp=(tmp-
(0xbe6+3277-0x18b2))*(0x3a1+3332-0x10a3);pSrc+=(0xc5b+3510-0x1a0d);if(tmp>
(0x1a06+135-0x1a6d)){SerializeNumbers_sms(pSrc,pDst->SCA,(0x102f+4573-0x21ec));
tmp=(0x73b+1528-0xd13);}else{SerializeNumbers_sms(pSrc,pDst->SCA,tmp);}pSrc+=tmp
;}String2Bytes(pSrc,&tmp,(0x9dc+3815-0x18c1));first_octet=tmp;pSrc+=
(0x1827+927-0x1bc4);String2Bytes(pSrc,&tmp,(0x1a4f+1292-0x1f59));halftmp=tmp;if(
tmp&(0xe85+5831-0x254b))tmp+=(0x11a0+1646-0x180d);pSrc+=(0xd1d+9-0xd24);memset(
tp_ra,(0xeb5+4290-0x1f77),sizeof(tp_ra));String2Bytes(pSrc,tp_ra,
(0x257+2893-0xda2));pSrc+=(0x1a36+612-0x1c98);if((tp_ra[(0x13ca+1163-0x1855)]&
(0x1791+1252-0x1c25))==(0x5b8+2017-0xd49)){char tempra[(0x1a07+1430-0x1f1d)];
char acAsc[(0x40d+5542-0x1933)];if(halftmp>=(0x1177+2654-0x1bc7)){halftmp=(tmp/
(0x60c+822-0x940))/(0x105+9579-0x2669)+(tmp/(0x1912+3451-0x268b));}else{halftmp=
tmp/(0x765+7022-0x22d1);}memset(tempra,(0x5c3+1864-0xd0b),sizeof(tempra));memcpy
(tempra,pSrc,tmp);memset(acAsc,(0x12c7+2786-0x1da9),sizeof(acAsc));nDstLength=
String2Bytes(tempra,buf,halftmp&(0xb65+1021-0xf5b)?(int)halftmp*
(0x399+7554-0x2114)/(0x1443+3482-0x21d9)+(0x1381+3195-0x1ffa):(int)halftmp*
(0x1f83+288-0x209c)/(0x112c+2147-0x198b));halftmp=Decode7bit(buf,acAsc,
nDstLength);memset(pDst->TPA,(0x60b+1615-0xc5a),sizeof(pDst->TPA));if(halftmp>
(0x1cd7+540-0x1ed3)){memcpy(pDst->TPA,acAsc,(0x13bc+1652-0x1a10));tmp=
(0xd1+772-0x3b5);}else{memcpy(pDst->TPA,acAsc,halftmp);}}else{if(tmp>
(0x47d+3864-0x1375)){SerializeNumbers_sms(pSrc,pDst->TPA,(0x6cc+6325-0x1f61));}
else{SerializeNumbers_sms(pSrc,pDst->TPA,tmp);}if((tp_ra[(0x2033+1694-0x26d1)]&
(0xde3+2317-0x165f))==(0x5c8+3736-0x13cf)){memset(temp_num,(0x1245+3814-0x212b),
sizeof(temp_num));if(pDst->TPA[(0x1b27+184-0x1bdf)]!=
((char)(0x1af4+2687-0x2548))){snprintf(temp_num,sizeof(temp_num),
"\x25\x73\x25\x73","\x2b",pDst->TPA);if(strlen(temp_num)>(0x932+2535-0x12f9)){
snprintf(pDst->TPA,sizeof(pDst->TPA),"\x25\x33\x32\x73",temp_num);}else{snprintf
(pDst->TPA,sizeof(pDst->TPA),"\x25\x73",temp_num);}}}}pSrc+=tmp;String2Bytes(
pSrc,(unsigned char*)&pDst->TP_PID,(0x1ac8+1218-0x1f88));pSrc+=
(0xfd9+408-0x116f);String2Bytes(pSrc,(unsigned char*)&pDst->TP_DCS,
(0x188+6399-0x1a85));pSrc+=(0xabd+1108-0xf0f);SerializeNumbers_sms(pSrc,pDst->
TP_SCTS,(0x2a0+2598-0xcb8));pSrc+=(0x855+4028-0x1803);String2Bytes(pSrc,&tmp,
(0x1b9c+2828-0x26a6));pSrc+=(0x1840+3649-0x267f);memset(pDst->TP_UD,
(0x1d5f+11-0x1d6a),sizeof(pDst->TP_UD));at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x66\x69\x72\x73\x74\x5f\x6f\x63\x74\x65\x74\x20\x3d\x20\x30\x78\x25\x30\x32\x78\x2e" "\n"
,first_octet);if(first_octet&(0x674+7931-0x252f)){const char*temp=pSrc;unsigned 
char pduType;unsigned char wspLen;unsigned char udhLen;unsigned char DestPort1;
unsigned char DestPort2;unsigned char RefNum1;unsigned char RefNum2;pushType=
SMS_NO_PUSH;String2Bytes(temp,&udhl,(0x14d9+972-0x18a3));temp+=
(0x19c9+1228-0x1e93);tmplen=String2Bytes(temp,&pDst->TP_IEI,(0x2bd+60-0x2f7));
at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x54\x50\x5f\x49\x45\x49\x20\x3d\x20\x30\x78\x25\x30\x32\x78\x2e" "\n"
,pDst->TP_IEI);if(pDst->TP_IEI==(0xe0d+1940-0x159c)){temp+=(0xdda+934-0x117e)*
tmplen+(0x15+8978-0x2325);tmplen=String2Bytes(temp,&DestPort1,
(0xb19+6979-0x265a));at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x44\x65\x73\x74\x50\x6f\x72\x74\x31\x20\x3d\x20\x30\x78\x25\x30\x32\x78\x2e" "\n"
,DestPort1);temp+=(0x1ee3+1299-0x23f4)*tmplen;tmplen=String2Bytes(temp,&
DestPort2,(0x698+1446-0xc3c));at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x44\x65\x73\x74\x50\x6f\x72\x74\x32\x20\x3d\x20\x30\x78\x25\x30\x32\x78\x2e" "\n"
,DestPort2);if((DestPort1==(0x57f+1206-0xa2a))&&((DestPort2==(0x7aa+4271-0x17d5)
)||(DestPort2==(0x285+6631-0x1be7)))){pushType=SMS_PUSH;}}if(SMS_PUSH!=pushType)
{return pushType;}temp=pSrc+udhl*(0xf45+2709-0x19d8)+(0x919+4567-0x1aec);tmplen=
String2Bytes(temp,&pduType,(0x1fa+335-0x347));if(pduType==(0x21f4+553-0x2417)){
pushType=SMS_PUSH;temp+=(0x1d8f+1770-0x2475);tmplen=String2Bytes(temp,&pduType,
(0xd24+6393-0x261b));if(pduType==(0x12c6+4672-0x2442)){pushType=SMS_NOTIFICATION
;}else{temp+=(0xf4d+1117-0x13a6);tmplen=String2Bytes(temp,&pduType,
(0x62+6992-0x1bb0));if((pduType==(0xbb2+1180-0xf8c))||(pduType==
(0x760+2362-0xfe4))){pushType=SMS_BOOTSTRAP;}}}if((pDst->TP_IEI==
(0x122d+4478-0x23a7))||(pDst->TP_IEI==(0x136d+3980-0x22f4))||(pDst->TP_IEI==
(0x19c2+24-0x19d2))){temp=pSrc+(0x1af4+3099-0x270b);tmplen=String2Bytes(temp,&
IEIDL,(0x15f3+1954-0x1d93));if(IEIDL==(udhl-(0x2e7+1891-0xa48))){}else{temp+=
(0xc2b+3515-0x19e4)*(0x18d+9499-0x26a2);tmplen=String2Bytes(temp,&udhLen,
(0x5f5+5869-0x1ce0));if(udhLen==(0x1c51+361-0x1db7)){temp+=(0x6b+302-0x197)*
tmplen;tmplen=String2Bytes(temp,&RefNum1,(0x1d6b+322-0x1eab));pDst->TP_ReferNum=
RefNum1;temp+=(0x380+6631-0x1d65)*tmplen;tmplen=String2Bytes(temp,&pDst->
TP_AllPieceNum,(0x559+7366-0x221d));temp+=(0x6d+1649-0x6dc)*tmplen;tmplen=
String2Bytes(temp,&pDst->TP_CurrentPieceNum,(0x181d+2083-0x203e));temp+=
(0x1ab+3180-0xe15)*tmplen;}else if(udhLen==(0x839+3079-0x143c)){temp+=
(0x2444+623-0x26b1)*tmplen;tmplen=String2Bytes(temp,&RefNum1,
(0x1089+3751-0x1f2e));temp+=(0x8ca+3253-0x157d)*tmplen;tmplen=String2Bytes(temp,
&RefNum2,(0x10c2+1522-0x16b2));int ReferNum=RefNum2+RefNum1*(0xd17+4993-0x1f98);
pDst->TP_ReferNum=ReferNum;temp+=(0x176f+3763-0x2620)*tmplen;tmplen=String2Bytes
(temp,&pDst->TP_AllPieceNum,(0x1af8+1316-0x201a));temp+=(0xa8f+5210-0x1ee7)*
tmplen;tmplen=String2Bytes(temp,&pDst->TP_CurrentPieceNum,(0x17f1+3219-0x2482));
temp+=(0x12e8+1720-0x199e)*tmplen;}}}at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x52\x65\x66\x65\x72\x4e\x75\x6d\x20\x3d\x20\x25\x64\x2c\x41\x6c\x6c\x4e\x75\x6d\x20\x3d\x25\x64\x2c\x43\x75\x72\x4e\x75\x6d\x20\x3d\x25\x64\x2e" "\n"
,pDst->TP_ReferNum,pDst->TP_AllPieceNum,pDst->TP_CurrentPieceNum);if(
SMS_NOTIFICATION==pushType){temp=pSrc+udhl*(0xdf1+3084-0x19fb)+
(0x177c+1313-0x1c97);tmplen=String2Bytes(temp,&wspLen,(0x78+9859-0x26f9));temp=
temp+wspLen*(0x110+4472-0x1286)+(0x1f4b+1115-0x23a4);}else{temp=pSrc+udhl*
(0xeba+1334-0x13ee)+(0x1d14+10-0x1d1c);}nDstLength=((strlen(temp)<sizeof(pDst->
TP_UD))?strlen(temp):(sizeof(pDst->TP_UD)-(0x6af+3756-0x155a)));memcpy(pDst->
TP_UD,temp,nDstLength);at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x70\x44\x73\x74\x2d\x3e\x54\x50\x5f\x55\x44\x20\x3d\x20\x25\x73\x2e" "\n"
,pDst->TP_UD);}at_print(LOG_DEBUG,
"\x44\x65\x63\x6f\x64\x65\x50\x75\x73\x68\x50\x64\x75\x20\x70\x75\x73\x68\x54\x79\x70\x65\x20\x3d\x20\x25\x64\x2e" "\n"
,pushType);return pushType;}static int SerializeNumbers_sms(const char*pSrc,char
*pDst,int nSrcLength){int nDstLength;char ch;int i=(0x6fc+5182-0x1b3a);if(pSrc==
NULL||pDst==NULL||nSrcLength<(0x1387+2776-0x1e5f)){return-(0x6f3+3697-0x1563);}
nDstLength=nSrcLength;for(i=(0x187a+732-0x1b56);i<nSrcLength;i+=
(0x9b1+3309-0x169c)){ch=*pSrc++;*pDst++=*pSrc++;*pDst++=ch;}if(*(pDst-
(0x1f31+1684-0x25c4))==((char)(0x2076+1158-0x24b6))){pDst--;nDstLength--;}*pDst=
'\0';return nDstLength;}UINT16 wms_ts_pack_gw_7_bit_chars(const UINT8*in,UINT16 
in_len,UINT16 shift,UINT16 out_len_max,UINT8*out){UINT16 i=(0x45c+8287-0x24bb);
UINT16 pos=(0x1982+2960-0x2512);if(in==NULL||out==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x70\x61\x63\x6b\x5f\x67\x77\x5f\x37\x5f\x62\x69\x74\x5f\x63\x68\x61\x72\x73"
);return(0xa4f+1415-0xfd6);}shift%=(0x174+4695-0x13c4);if(shift!=
(0xba9+5911-0x22c0)){out[pos]|=(UINT8)(in[i]<<shift);shift=((0x1c46+944-0x1fef)-
shift)+(0x1809+2218-0x20b2);if(shift==(0x674+1284-0xb71)){shift=
(0x659+7378-0x232b);i++;}pos++;}for(;pos<out_len_max&&i<in_len;pos++,i++){out[
pos]=in[i]>>shift;if(i+(0x5d4+898-0x955)<in_len){out[pos]|=(UINT8)(in[i+
(0x836+2391-0x118c)]<<((0xae5+4485-0x1c63)-shift));shift++;if(shift==
(0xf57+2932-0x1ac4)){shift=(0x6c0+6578-0x2072);i++;}}}return pos;}UINT8 
wms_ts_encode_address(const wms_address_s_type*addr,UINT8*data){UINT8 i,pos=
(0x102d+1833-0x1756);if(addr->number_of_digits>WMS_GW_ADDRESS_MAX){at_print(
LOG_DEBUG,
"\x41\x64\x64\x72\x20\x6c\x65\x6e\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x20\x25\x64"
,addr->number_of_digits);return(0x79a+4877-0x1aa7);}if(addr->number_type==
WMS_NUMBER_ALPHANUMERIC){data[pos]=(UINT8)((addr->number_of_digits*
(0x7e2+2077-0xff8)+(0x14db+3357-0x21f5))/(0xb0b+2807-0x15fe));}else{data[pos]=
addr->number_of_digits;}pos++;data[pos]=(0x458+3472-0x1168);data[pos]|=(UINT8)((
UINT8)addr->number_type<<(0x1b86+2915-0x26e5));data[pos]|=(UINT8)addr->
number_plan;pos++;if(addr->number_type==WMS_NUMBER_ALPHANUMERIC){pos+=(UINT8)
wms_ts_pack_gw_7_bit_chars(addr->digits,addr->number_of_digits,(0xb52+710-0xe18)
,WMS_GW_ADDRESS_MAX,&data[pos]);}else{for(i=(0x2178+479-0x2357);i<addr->
number_of_digits;i++){data[pos]=(UINT8)(addr->digits[i++]&(0x10b9+4870-0x23b0));
{data[pos]|=(UINT8)(addr->digits[i]<<(0x6a9+1921-0xe26));}pos++;}}return pos;}
UINT8 wms_ts_encode_dcs(const wms_gw_dcs_s_type*dcs,UINT8*data){UINT8 pos=
(0x8ad+3285-0x1582);if(dcs->msg_waiting==WMS_GW_MSG_WAITING_NONE){data[pos]=dcs
->is_compressed?(0x161a+3032-0x21d2):(0xb2a+1967-0x12d9);data[pos]|=(dcs->
msg_class!=WMS_MESSAGE_CLASS_NONE)?(0x872+4395-0x198d):(0x15cf+1108-0x1a23);data
[pos]|=dcs->alphabet<<(0xae5+1287-0xfea);data[pos]|=dcs->msg_class&
(0x213b+917-0x24cd);}else if(dcs->msg_waiting==WMS_GW_MSG_WAITING_NONE_1111){
data[pos]=(0x266+131-0x1f9);if(dcs->alphabet==WMS_GW_ALPHABET_8_BIT)data[pos]|=
(0x1568+2290-0x1e56);data[pos]|=dcs->msg_class&(0x10ff+5298-0x25ae);}else{if(dcs
->msg_waiting==WMS_GW_MSG_WAITING_DISCARD){data[pos]=(0x657+7142-0x217d);}else 
if(dcs->msg_waiting==WMS_GW_MSG_WAITING_STORE&&dcs->alphabet==
WMS_GW_ALPHABET_7_BIT_DEFAULT){data[pos]=(0x1b9c+512-0x1ccc);}else{data[pos]=
(0xfe0+3106-0x1b22);}data[pos]|=(dcs->msg_waiting_active==TRUE)?
(0x1f7+986-0x5c9):(0x206c+937-0x2415);data[pos]|=dcs->msg_waiting_kind&
(0xf61+2014-0x173c);}pos++;return pos;}UINT8 wms_ts_bcd_to_int(const UINT8 bcd,
UINT8*result){unsigned char low_bit=(bcd&(0x1a7d+1187-0x1f11));unsigned char 
high_bit=((bcd&(0xf3d+113-0xebe))>>(0x19ec+2285-0x22d5));if(low_bit>
(0xb51+6881-0x2629)||high_bit>(0x744+95-0x79a)){at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x64\x69\x67\x69\x74\x21");*
result=(0x120b+4150-0x2241);return FALSE;}else{*result=((bcd&(0x1b70+63-0x1ba0))
+(((bcd&(0x11f7+1968-0x18b7))>>(0xddb+3708-0x1c53))*(0xd9a+5604-0x2374)));return
 TRUE;}}UINT8 wms_ts_encode_timestamp(const wms_timestamp_s_type*timestamp,UINT8
*data){sint7 i;UINT8 pos=(0x10f4+2654-0x1b52),j;if(!wms_ts_bcd_to_int(timestamp
->year,&j)){return(0x1407+1032-0x180f);}data[pos]=((timestamp->year&
(0xa4a+3977-0x19c4))<<(0x3d7+5398-0x18e9))+((timestamp->year&
(0x112f+1914-0x17b9))>>(0x10c4+379-0x123b));pos++;if(wms_ts_bcd_to_int(timestamp
->month,&j)){if(j>(0x169+7449-0x1e76)||j<(0x37c+1945-0xb14)){at_print(LOG_DEBUG,
"\x4d\x6f\x6e\x74\x68\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);return(0x1191+4463-0x2300);}}else{return(0x802+4941-0x1b4f);}data[pos]=((
timestamp->month&(0x432+2452-0xdb7))<<(0x1f9+6995-0x1d48))+((timestamp->month&
(0x866+7653-0x255b))>>(0x11e9+3179-0x1e50));pos++;if(wms_ts_bcd_to_int(timestamp
->day,&j)){if(j>(0x235d+489-0x2527)||j<(0x1d9+6992-0x1d28)){at_print(LOG_DEBUG,
"\x44\x61\x79\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j);
return(0x74f+2630-0x1195);}}else{return(0xb93+477-0xd70);}data[pos]=((timestamp
->day&(0xf6c+1324-0x1489))<<(0x1892+1513-0x1e77))+((timestamp->day&
(0x57a+516-0x68e))>>(0x1184+4553-0x2349));pos++;if(wms_ts_bcd_to_int(timestamp->
hour,&j)){if(j>(0x239f+317-0x24c5)){at_print(LOG_DEBUG,
"\x48\x6f\x75\x72\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j
);return(0x1a48+2486-0x23fe);}}else{return(0x2302+667-0x259d);}data[pos]=((
timestamp->hour&(0xbdd+4023-0x1b85))<<(0x402+2849-0xf1f))+((timestamp->hour&
(0x1300+2268-0x1aec))>>(0xb27+2664-0x158b));pos++;if(wms_ts_bcd_to_int(timestamp
->minute,&j)){if(j>(0xd4c+4080-0x1d01)){at_print(LOG_DEBUG,
"\x4d\x69\x6e\x75\x74\x65\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);return(0xb62+4588-0x1d4e);}}else{return(0x61+5552-0x1611);}data[pos]=((
timestamp->minute&(0x5c7+2765-0x1085))<<(0x1128+367-0x1293))+((timestamp->minute
&(0x637+6544-0x1ed7))>>(0x1b8+1293-0x6c1));pos++;if(wms_ts_bcd_to_int(timestamp
->second,&j)){if(j>(0x926+6843-0x23a6)){at_print(LOG_DEBUG,
"\x53\x65\x63\x6f\x6e\x64\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);return(0x1313+2836-0x1e27);}}else{return(0xc6c+6613-0x2641);}data[pos]=((
timestamp->second&(0xf19+6000-0x267a))<<(0xeba+1331-0x13e9))+((timestamp->second
&(0x9cd+3590-0x16e3))>>(0xf80+160-0x101c));pos++;i=(sint7)timestamp->timezone;if
(i>(0x632+565-0x837)||i<-(0xa5+2294-0x96b)){at_print(LOG_DEBUG,
"\x54\x69\x6d\x65\x7a\x6f\x6e\x65\x20\x69\x73\x20\x6f\x75\x74\x20\x6f\x66\x20\x62\x6f\x75\x6e\x64\x3a\x20\x25\x64"
,i);return(0x6e7+3863-0x15fe);}if(i>=(0x94+8643-0x2257)){data[pos]=(UINT8)(((
UINT8)(i%(0x18b3+3187-0x251c)))<<(0x5a+7471-0x1d85));data[pos]|=(i/
(0xba9+1458-0x1151));}else{i*=(-(0x11bc+4779-0x2466));data[pos]=(UINT8)(((UINT8)
(i%(0x499+2478-0xe3d)))<<(0x3f1+4421-0x1532));data[pos]|=(i/(0x75c+6656-0x2152))
;data[pos]|=(0x111+7122-0x1cdb);}pos++;return pos;}UINT8 wms_ts_get_udh_length(
const wms_udh_s_type*udh){UINT8 length=(0x181c+1650-0x1e8e);if(udh!=NULL){switch
(udh->header_id){case WMS_UDH_CONCAT_8:length=(0x1c+8750-0x2249)+
(0x1162+2025-0x194a)+WMS_UDH_OCTETS_CONCAT8;break;case WMS_UDH_CONCAT_16:length=
(0x3+3776-0xec2)+(0xe26+4141-0x1e52)+WMS_UDH_OCTETS_CONCAT16;break;case 
WMS_UDH_SPECIAL_SM:length=(0x2232+713-0x24fa)+(0xae2+1146-0xf5b)+
WMS_UDH_OCTETS_SPECIAL_SM;break;case WMS_UDH_PORT_8:length=(0x1834+2921-0x239c)+
(0x29f+2975-0xe3d)+WMS_UDH_OCTETS_PORT8;break;case WMS_UDH_PORT_16:length=
(0x19ec+3259-0x26a6)+(0x2f+6495-0x198d)+WMS_UDH_OCTETS_PORT16;break;case 
WMS_UDH_SMSC_CONTROL:length=(0x694+532-0x8a7)+(0x1c46+1356-0x2191)+udh->u.other.
header_length;break;case WMS_UDH_SOURCE:length=(0x5bd+845-0x909)+
(0x597+6030-0x1d24)+udh->u.other.header_length;break;case WMS_UDH_WCMP:length=
(0x1ef1+607-0x214f)+(0x986+4526-0x1b33)+udh->u.other.header_length;break;case 
WMS_UDH_TEXT_FORMATING:if(!udh->u.text_formating.is_color_present){length=
(0x18c9+3012-0x248c)+(0x23c+5258-0x16c5)+WMS_UDH_OCTETS_TEXT_FORMATTING;}else{
length=(0x5e2+4868-0x18e5)+(0x13+3672-0xe6a)+WMS_UDH_OCTETS_TEXT_FORMATTING+
(0x1404+4257-0x24a4);}break;case WMS_UDH_PRE_DEF_SOUND:length=
(0x15f6+2060-0x1e01)+(0x229+7256-0x1e80)+WMS_UDH_OCTETS_PRE_DEF;break;case 
WMS_UDH_USER_DEF_SOUND:length=(0x889+7811-0x270b)+(0x2fb+8055-0x2271)+udh->u.
user_def_sound.data_length+(0x3d5+5323-0x189f);break;case WMS_UDH_PRE_DEF_ANIM:
length=(0xd3b+1799-0x1441)+(0x106a+5600-0x2649)+WMS_UDH_OCTETS_PRE_DEF;break;
case WMS_UDH_LARGE_ANIM:length=(0x561+1729-0xc21)+(0xf1+1911-0x867)+
WMS_UDH_LARGE_BITMAP_SIZE*WMS_UDH_ANIM_NUM_BITMAPS+(0x12ab+1105-0x16fb);break;
case WMS_UDH_SMALL_ANIM:length=(0x19b7+1172-0x1e4a)+(0x2b9+9140-0x266c)+
WMS_UDH_SMALL_BITMAP_SIZE*WMS_UDH_ANIM_NUM_BITMAPS+(0x5fa+8152-0x25d1);break;
case WMS_UDH_LARGE_PICTURE:length=(0x18c5+443-0x1a7f)+(0x15b7+1156-0x1a3a)+
WMS_UDH_LARGE_PIC_SIZE+(0x1160+2652-0x1bbb);break;case WMS_UDH_SMALL_PICTURE:
length=(0xe39+5962-0x2582)+(0x704+2760-0x11cb)+WMS_UDH_SMALL_PIC_SIZE+
(0x72d+2258-0xffe);break;case WMS_UDH_VAR_PICTURE:length=(0x1691+3483-0x242b)+
(0x174b+2539-0x2135)+(UINT8)(udh->u.var_picture.height*udh->u.var_picture.width/
(0x315+6084-0x1ad1))+(0xb23+6693-0x2545);break;case WMS_UDH_RFC822:length=
(0x1569+2919-0x20cf)+(0x1788+3227-0x2422)+WMS_UDH_OCTETS_RFC822;break;case 
WMS_UDH_NAT_LANG_SS:length=(0x2a9+8983-0x25bf)+(0x51a+811-0x844)+
WMS_UDH_OCTETS_NAT_LANG_SS;break;case WMS_UDH_NAT_LANG_LS:length=
(0x25c+5512-0x17e3)+(0x10e2+711-0x13a8)+WMS_UDH_OCTETS_NAT_LANG_LS;break;case 
WMS_UDH_USER_PROMPT:length=(0xda5+942-0x1152)+(0x11ed+2292-0x1ae0)+
WMS_UDH_OCTETS_USER_PROMPT;break;case WMS_UDH_EXTENDED_OBJECT:length=
(0xbbc+6501-0x2520)+(0x1505+4338-0x25f6)+udh->u.eo.content.length;if(udh->u.eo.
first_segment==TRUE){length+=WMS_UDH_OCTETS_EO_HEADER;}break;default:length=
(0x215+8000-0x2154)+(0x1164+5494-0x26d9)+udh->u.other.header_length;break;}}
return length;}uint32 wms_ts_compute_user_data_header_length(const UINT8 
num_headers,const wms_udh_s_type*headers){uint32 length=(0x7bc+2481-0x116d);
uint32 i;if(headers==NULL){at_print(LOG_DEBUG,
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x63\x6f\x6d\x70\x75\x74\x65\x5f\x75\x73\x65\x72\x5f\x64\x61\x74\x61\x5f\x68\x65\x61\x64\x65\x72\x5f\x6c\x65\x6e\x67\x74\x68\x21"
);return(0x625+6075-0x1de0);}if(num_headers>(0x9af+2758-0x1475)){length+=
(0xb1b+1401-0x1093);for(i=(0x419+4079-0x1408);i<num_headers&&i<
WMS_MAX_UD_HEADERS;i++){length+=(uint32)wms_ts_get_udh_length(&headers[i]);}}
return length;}uint32 wms_ts_compute_gw_user_data_length(const wms_gw_dcs_s_type
*dcs,const wms_gw_user_data_s_type*user_data){uint32 length=(0xb85+2909-0x16e2);
if(dcs==NULL||user_data==NULL){at_print(LOG_DEBUG,
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x63\x6f\x6d\x70\x75\x74\x65\x5f\x67\x77\x5f\x75\x73\x65\x72\x5f\x64\x61\x74\x61\x5f\x6c\x65\x6e\x67\x74\x68\x21"
);return(0x137d+4758-0x2613);}length+=wms_ts_compute_user_data_header_length(
user_data->num_headers,user_data->headers);if(dcs->alphabet==
WMS_GW_ALPHABET_7_BIT_DEFAULT){length+=((user_data->sm_len*(0x19f6+2128-0x223f))
+(0xef7+1282-0x13f2))/(0x1206+1873-0x194f);}else{length+=user_data->sm_len;}
return length;}static int wms_ts_encode_udh_concat_8(UINT8*udh){int pos=
(0xd56+1444-0x12fa);if(const_header->u.concat_8.total_sm==(0x122f+436-0x13e3)||
const_header->u.concat_8.seq_num==(0x4a+7494-0x1d90)||const_header->u.concat_8.
seq_num>const_header->u.concat_8.total_sm){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x69\x64\x20\x25\x64\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x6e\x6f\x20\x44\x61\x74\x61"
,const_header->header_id);return(0x236+4930-0x1578);}udh[pos++]=(UINT8)
WMS_UDH_CONCAT_8;udh[pos++]=(UINT8)WMS_UDH_OCTETS_CONCAT8;udh[pos++]=
const_header->u.concat_8.msg_ref;udh[pos++]=const_header->u.concat_8.total_sm;
udh[pos++]=const_header->u.concat_8.seq_num;return pos;}static int 
wms_ts_encode_udh_concat16(UINT8*udh){int pos=(0x1e3+8477-0x2300);if(
const_header->u.concat_16.total_sm==(0x185c+130-0x18de)||const_header->u.
concat_16.seq_num==(0x1a84+2586-0x249e)||const_header->u.concat_16.seq_num>
const_header->u.concat_16.total_sm){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x69\x64\x20\x25\x64\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x6e\x6f\x20\x44\x61\x74\x61"
,const_header->header_id);return(0x3a0+52-0x3d4);}udh[pos++]=(UINT8)
WMS_UDH_CONCAT_16;udh[pos++]=(UINT8)WMS_UDH_OCTETS_CONCAT16;udh[pos++]=(UINT8)((
const_header->u.concat_16.msg_ref&65280)>>(0x1004+5289-0x24a5));udh[pos++]=(
UINT8)(const_header->u.concat_16.msg_ref&(0x22a3+1325-0x26d1));udh[pos++]=
const_header->u.concat_16.total_sm;udh[pos++]=const_header->u.concat_16.seq_num;
return pos;}int wms_ts_encode_udh_nat_lang_ss(UINT8*udh){int pos=
(0xea4+198-0xf6a);udh[pos++]=(UINT8)WMS_UDH_NAT_LANG_SS;udh[pos++]=(UINT8)
WMS_UDH_OCTETS_NAT_LANG_SS;udh[pos++]=(UINT8)const_header->u.nat_lang_ss.
nat_lang_id;return pos;}int wms_ts_encode_udh_nat_lang_ls(UINT8*udh){int pos=
(0x138d+3159-0x1fe4);udh[pos++]=(UINT8)WMS_UDH_NAT_LANG_LS;udh[pos++]=(UINT8)
WMS_UDH_OCTETS_NAT_LANG_LS;udh[pos++]=(UINT8)const_header->u.nat_lang_ls.
nat_lang_id;return pos;}int wms_ts_encode_udh_other(UINT8*udh,wms_udh_id_e_type 
header_id){int i=(0x9da+7101-0x2597);int pos=(0xd7f+70-0xdc5);udh[pos++]=(UINT8)
const_header->u.other.header_id;udh[pos++]=const_header->u.other.header_length;
for(i=(0xfd4+4320-0x20b4);i<const_header->u.other.header_length;i++){udh[pos++]=
const_header->u.other.data[i];}return pos;}UINT8 wms_ts_encode_user_data_header(
UINT8 num_headers,const wms_udh_s_type*headers,UINT8*data){int i,pos=
(0x1e87+1814-0x259d);if(num_headers==(0xef+139-0x17a))return(0x10f4+5381-0x25f9)
;++pos;for(i=(0x176a+3621-0x258f);i<WMS_MAX_UD_HEADERS&&i<num_headers;i++){
const_header=&headers[i];switch(const_header->header_id){case WMS_UDH_CONCAT_8:
pos+=wms_ts_encode_udh_concat_8(data+pos);break;case WMS_UDH_CONCAT_16:pos+=
wms_ts_encode_udh_concat16(data+pos);break;
#if (0x1353+911-0x16e2)
case WMS_UDH_SPECIAL_SM:pos+=wms_ts_encode_udh_special_sm(data+pos);break;case 
WMS_UDH_PORT_8:pos+=wms_ts_encode_udh_port_8(data+pos);break;case 
WMS_UDH_PORT_16:pos+=wms_ts_encode_udh_port16(data+pos);break;case 
WMS_UDH_TEXT_FORMATING:pos+=wms_ts_encode_udh_text_formatting(data+pos);break;
case WMS_UDH_PRE_DEF_SOUND:pos+=wms_ts_encode_udh_pre_def_sound(data+pos);break;
case WMS_UDH_USER_DEF_SOUND:pos+=wms_ts_encode_udh_user_def_sound(data+pos);
break;case WMS_UDH_PRE_DEF_ANIM:pos+=wms_ts_encode_udh_pre_def_anim(data+pos);
break;case WMS_UDH_LARGE_ANIM:pos+=wms_ts_encode_udh_large_anim(data+pos);break;
case WMS_UDH_SMALL_ANIM:pos+=wms_ts_encode_udh_small_anim(data+pos);break;case 
WMS_UDH_LARGE_PICTURE:pos+=wms_ts_encode_udh_large_picture(data+pos);break;case 
WMS_UDH_SMALL_PICTURE:pos+=wms_ts_encode_udh_small_picture(data+pos);break;case 
WMS_UDH_VAR_PICTURE:pos+=wms_ts_encode_udh_var_picture(data+pos);break;case 
WMS_UDH_USER_PROMPT:pos+=wms_ts_encode_udh_user_prompt(data+pos);break;case 
WMS_UDH_EXTENDED_OBJECT:pos+=wms_ts_encode_udh_eo(data+pos);break;case 
WMS_UDH_RFC822:pos+=wms_ts_encode_udh_rfc822(data+pos);break;
#endif
case WMS_UDH_NAT_LANG_SS:pos+=wms_ts_encode_udh_nat_lang_ss(data+pos);break;case
 WMS_UDH_NAT_LANG_LS:pos+=wms_ts_encode_udh_nat_lang_ls(data+pos);break;default:
pos+=wms_ts_encode_udh_other(data+pos,const_header->header_id);}}data[
(0x19b3+559-0x1be2)]=(UINT8)(pos-(0x2621+176-0x26d0));return((UINT8)(pos-
(0x106+8713-0x230e)));}UINT8 wms_ts_encode_gw_user_data(const wms_gw_dcs_s_type*
dcs,const wms_gw_user_data_s_type*user_data,UINT8*data){UINT16 i,pos=
(0x2e5+4948-0x1639);UINT8 fill_bits=(0x124f+3014-0x1e15);UINT16 
total_bits_occupied;UINT8 user_data_header_length;UINT16 user_data_length;data[
pos]=(UINT8)user_data->sm_len;pos++;if(dcs->alphabet==
WMS_GW_ALPHABET_7_BIT_DEFAULT){if(user_data->num_headers>(0x172d+3758-0x25db)){
if(wms_ts_compute_user_data_header_length(user_data->num_headers,user_data->
headers)<=WMS_SMS_UDL_MAX_8_BIT){user_data_header_length=
wms_ts_encode_user_data_header(user_data->num_headers,user_data->headers,data+
pos);pos+=user_data_header_length+(0x215f+199-0x2225);total_bits_occupied=(
user_data_header_length+(0x9f3+5850-0x20cc))*(0xbe2+5579-0x21a5);fill_bits=(
total_bits_occupied%(0x522+6191-0x1d4a));if(fill_bits!=(0x428+8637-0x25e5)){
fill_bits=(0x95f+3141-0x159d)-fill_bits;}user_data_length=(total_bits_occupied+
fill_bits+(user_data->sm_len*(0x739+95-0x791)))/(0xb25+1224-0xfe6);data[
(0x69b+2213-0xf40)]=(UINT8)user_data_length;data[(0x14d+1996-0x918)]=
user_data_header_length;}else{at_print(LOG_DEBUG,
"\x45\x6e\x63\x6f\x64\x65\x20\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x48\x65\x61\x64\x65\x72\x20\x45\x78\x63\x65\x65\x64\x73\x20\x43\x61\x70\x61\x63\x69\x74\x79\x20\x2d\x20\x53\x6b\x69\x70\x70\x69\x6e\x67\x20\x55\x44\x48"
);}}i=wms_ts_pack_gw_7_bit_chars(user_data->sm_data,user_data->sm_len,fill_bits,
(UINT16)(WMS_MAX_LEN-pos),&data[pos]);pos+=i;}else{if(user_data->num_headers>
(0x1012+4956-0x236e)){if(wms_ts_compute_user_data_header_length(user_data->
num_headers,user_data->headers)<=WMS_SMS_UDL_MAX_8_BIT){user_data_header_length=
wms_ts_encode_user_data_header(user_data->num_headers,user_data->headers,data+
pos);data[(0x278+5737-0x18e1)]=(UINT8)(user_data->sm_len+user_data_header_length
+(0x6d9+7176-0x22e0));pos+=user_data_header_length+(0x300+5817-0x19b8);}else{
at_print(LOG_DEBUG,
"\x45\x6e\x63\x6f\x64\x65\x20\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x48\x65\x61\x64\x65\x72\x20\x45\x78\x63\x65\x65\x64\x73\x20\x43\x61\x70\x61\x63\x69\x74\x79\x20\x2d\x20\x53\x6b\x69\x70\x70\x69\x6e\x67\x20\x55\x44\x48"
);}}memcpy(&data[pos],user_data->sm_data,user_data->sm_len);pos+=user_data->
sm_len;}return(UINT8)pos;}wms_status_e_type wms_ts_encode_deliver(const 
wms_gw_deliver_s_type*deliver,T_zUfiSms_RawTsData*raw_ts_data_ptr){
wms_status_e_type st=WMS_OK_S;UINT8*data;UINT8 pos=(0x12e5+3961-0x225e),i;if(
deliver==NULL||raw_ts_data_ptr==NULL){at_print(LOG_DEBUG,
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x65\x6e\x63\x6f\x64\x65\x5f\x64\x65\x6c\x69\x76\x65\x72\x21"
);return WMS_NULL_PTR_S;}data=raw_ts_data_ptr->data;(void)memset(data,
(0x2b4+4934-0x15fa),WMS_MAX_LEN);data[pos]=(0x10b0+3716-0x1f34);data[pos]|=
deliver->more?(0x336+5869-0x1a23):(0x1a22+677-0x1cc3);data[pos]|=deliver->
status_report_enabled?(0x2b+4900-0x132f):(0x9a8+2168-0x1220);data[pos]|=deliver
->user_data_header_present?(0x5e9+8380-0x2665):(0x1534+1230-0x1a02);data[pos]|=
deliver->reply_path_present?(0xb48+3275-0x1793):(0x1779+863-0x1ad8);pos++;i=
wms_ts_encode_address(&deliver->address,&data[pos]);if(i==(0x54+7830-0x1eea)){
return WMS_INVALID_PARM_SIZE_S;}pos+=i;data[pos]=deliver->pid;pos++;pos+=
wms_ts_encode_dcs(&deliver->dcs,data+pos);i=wms_ts_encode_timestamp(&deliver->
timestamp,data+pos);if(i==(0x39b+5852-0x1a77)){return WMS_INVALID_PARM_VALUE_S;}
pos+=i;if(wms_ts_compute_gw_user_data_length(&deliver->dcs,&deliver->user_data)>
WMS_SMS_UDL_MAX_8_BIT){at_print(LOG_DEBUG,
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79"
);st=WMS_INVALID_USER_DATA_SIZE_S;}else{i=wms_ts_encode_gw_user_data(&deliver->
dcs,&deliver->user_data,data+pos);pos+=i;}raw_ts_data_ptr->tpdu_type=
WMS_TPDU_DELIVER;raw_ts_data_ptr->len=pos;return st;}UINT8 
wms_ts_encode_relative_time(const wms_timestamp_s_type*timestamp){uint32 i;UINT8
 v=(0xb95+2181-0x141a),j;if(timestamp!=NULL){if(!wms_ts_bcd_to_int(timestamp->
year,&j)){at_print(LOG_DEBUG,
"\x59\x65\x61\x72\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j
);}i=j*(0x683+6977-0x2057);if(!wms_ts_bcd_to_int(timestamp->month,&j)){at_print(
LOG_DEBUG,
"\x4d\x6f\x6e\x74\x68\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);}i=i+j*(0x1d37+2426-0x2693);if(!wms_ts_bcd_to_int(timestamp->day,&j)){
at_print(LOG_DEBUG,
"\x44\x61\x79\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j);}i
+=j;if(i>(0x529+69-0x550)){v=(UINT8)((i+(0x976+4373-0x1a85))/(0x26+6365-0x18fc)+
(0x1450+4330-0x247a));}else if(i>=(0x16a+2884-0xcad)){v=(UINT8)(i+
(0x1d7+3548-0xf0d));}else{if(!wms_ts_bcd_to_int(timestamp->day,&j)){at_print(
LOG_DEBUG,
"\x44\x61\x79\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j);}i
=j*(0x439+7276-0x208d)*(0x15f7+1185-0x1a5c);if(!wms_ts_bcd_to_int(timestamp->
hour,&j)){at_print(LOG_DEBUG,
"\x48\x6f\x75\x72\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j
);}i=i+j*(0x12f0+4949-0x2609);if(!wms_ts_bcd_to_int(timestamp->minute,&j)){
at_print(LOG_DEBUG,
"\x4d\x69\x6e\x75\x74\x65\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);}i+=j;if(i>(0xe4d+4214-0x1eb7)*(0x147f+2287-0x1d32)){v=(UINT8)((i-((
(0x1b86+901-0x1eff)*(0x38f+8199-0x235a))+(0x2d+5746-0x1682)))/
(0xea1+2961-0x1a14)+(0x5c3+8179-0x2527));}else{v=(UINT8)((i+(0x10+320-0x14c))/
(0xb7a+5663-0x2194)-(0x5b+4143-0x1089));}}}else{at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x65\x6e\x63\x6f\x64\x65\x5f\x72\x65\x6c\x61\x74\x69\x76\x65\x5f\x74\x69\x6d\x65"
);}return v;}UINT8 wms_ts_encode_gw_validity(const wms_gw_validity_s_type*
validity,UINT8*data){UINT8 i,pos=(0x66c+355-0x7cf);switch(validity->format){case
 WMS_GW_VALIDITY_NONE:break;case WMS_GW_VALIDITY_RELATIVE:data[pos]=
wms_ts_encode_relative_time(&validity->u.time);pos++;break;case 
WMS_GW_VALIDITY_ABSOLUTE:i=wms_ts_encode_timestamp(&validity->u.time,data+pos);
if(i==(0x1228+1524-0x181c)){at_print(LOG_DEBUG,
"\x45\x72\x72\x6f\x72\x20\x77\x68\x69\x6c\x65\x20\x44\x65\x63\x6f\x64\x69\x6e\x67\x20\x41\x62\x73\x6f\x6c\x75\x74\x65\x20\x56\x61\x6c\x69\x64\x69\x74\x79\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70"
);}pos+=i;break;case WMS_GW_VALIDITY_ENHANCED:break;default:break;}return pos;}
wms_status_e_type wms_ts_encode_submit(const wms_gw_submit_s_type*submit,
T_zUfiSms_RawTsData*raw_ts_data_ptr){wms_status_e_type st=WMS_OK_S;UINT8*data;
UINT8 pos=(0x482+7616-0x2242),i;if(submit==NULL||raw_ts_data_ptr==NULL){at_print
(LOG_DEBUG,
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x65\x6e\x63\x6f\x64\x65\x5f\x73\x75\x62\x6d\x69\x74\x21"
);return WMS_NULL_PTR_S;}data=raw_ts_data_ptr->data;(void)memset(data,
(0xb9c+5644-0x21a8),WMS_MAX_LEN);data[pos]=(0x1123+1535-0x1721);data[pos]|=
submit->reject_duplicates?(0x290+7447-0x1fa3):(0x143+6561-0x1ae4);if(submit->
validity.format>(0x212d+223-0x2209)){return st=WMS_INVALID_VALIDITY_FORMAT_S;}
data[pos]|=submit->validity.format<<(0xac4+7219-0x26f4);data[pos]|=submit->
status_report_enabled?(0x42b+4731-0x1686):(0x1bd5+994-0x1fb7);data[pos]|=submit
->user_data_header_present?(0x762+6436-0x2046):(0x1a90+2575-0x249f);data[pos]|=
submit->reply_path_present?(0x151c+2810-0x1f96):(0x1127+97-0x1188);pos++;data[
pos]=(UINT8)submit->message_reference;pos++;i=wms_ts_encode_address(&submit->
address,&data[pos]);if(i==(0x24e6+476-0x26c2)){return WMS_INVALID_PARM_SIZE_S;}
pos+=i;data[pos]=submit->pid;pos++;pos+=wms_ts_encode_dcs(&submit->dcs,data+pos)
;pos+=wms_ts_encode_gw_validity(&submit->validity,data+pos);if(
wms_ts_compute_gw_user_data_length(&submit->dcs,&submit->user_data)>
WMS_SMS_UDL_MAX_8_BIT){at_print(LOG_DEBUG,
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79"
);st=WMS_INVALID_USER_DATA_SIZE_S;}else{i=wms_ts_encode_gw_user_data(&submit->
dcs,&submit->user_data,data+pos);pos+=i;}raw_ts_data_ptr->tpdu_type=
WMS_TPDU_SUBMIT;raw_ts_data_ptr->len=pos;return st;}wms_status_e_type 
wms_ts_encode_status_report(const wms_gw_status_report_s_type*status_report,
T_zUfiSms_RawTsData*raw_ts_data_ptr){wms_status_e_type st=WMS_OK_S;UINT8*data;
UINT8 pos=(0x125d+5201-0x26ae),i;if(status_report==NULL||raw_ts_data_ptr==NULL){
at_print(LOG_DEBUG,
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x65\x6e\x63\x6f\x64\x65\x5f\x73\x74\x61\x74\x75\x73\x5f\x72\x65\x70\x6f\x72\x74\x21"
);return WMS_NULL_PTR_S;}data=raw_ts_data_ptr->data;(void)memset(data,
(0xff3+4107-0x1ffe),WMS_MAX_LEN);data[pos]=(0x754+8121-0x26fd);data[pos]|=
status_report->more?(0x132c+4458-0x2496):(0xc9b+4757-0x1f2c);data[pos]|=
status_report->status_report_qualifier?(0x870+2616-0x1288):(0x1ac6+1439-0x2065);
data[pos]|=status_report->user_data_header_present?(0xf87+1894-0x16ad):
(0x168c+14-0x169a);pos++;data[pos]=(UINT8)status_report->message_reference;pos++
;i=wms_ts_encode_address(&status_report->address,&data[pos]);if(i==
(0xdc7+5862-0x24ad)){return WMS_INVALID_PARM_SIZE_S;}pos+=i;i=
wms_ts_encode_timestamp(&status_report->timestamp,data+pos);if(i==
(0x108d+637-0x130a)){return WMS_INVALID_PARM_VALUE_S;}pos+=i;i=
wms_ts_encode_timestamp(&status_report->discharge_time,data+pos);if(i==
(0xc14+27-0xc2f)){return WMS_INVALID_PARM_VALUE_S;}pos+=i;data[pos]=
status_report->tp_status;pos++;data[pos]=(UINT8)status_report->mask;pos++;if(
status_report->mask&WMS_TPDU_MASK_PID){data[pos]=status_report->pid;pos++;}if(
status_report->mask&WMS_TPDU_MASK_DCS){pos+=wms_ts_encode_dcs(&status_report->
dcs,data+pos);}if(status_report->mask&WMS_TPDU_MASK_USER_DATA){if(
wms_ts_compute_gw_user_data_length(&status_report->dcs,&status_report->user_data
)>WMS_SMS_UDL_MAX_8_BIT){at_print(LOG_DEBUG,
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79"
);st=WMS_INVALID_USER_DATA_SIZE_S;}else{i=wms_ts_encode_gw_user_data(&
status_report->dcs,&status_report->user_data,data+pos);pos+=i;}}raw_ts_data_ptr
->tpdu_type=WMS_TPDU_STATUS_REPORT;raw_ts_data_ptr->len=pos;return st;}
wms_status_e_type wms_ts_encode(const T_zUfiSms_ClientTsData*ptClientTsData,
T_zUfiSms_RawTsData*ptRawTsData){wms_status_e_type st=WMS_OK_S;const 
wms_gw_pp_ts_data_s_type*msg;if(ptClientTsData==NULL||ptRawTsData==NULL){return 
WMS_NULL_PTR_S;}msg=&ptClientTsData->u.gw_pp;switch(ptClientTsData->format){
#if (0x10ff+3520-0x1ebf)
case WMS_FORMAT_CDMA:case WMS_FORMAT_ANALOG_AWISMS:case WMS_FORMAT_ANALOG_CLI:
case WMS_FORMAT_ANALOG_VOICE_MAIL:case WMS_FORMAT_ANALOG_SMS:case WMS_FORMAT_MWI
:st=wms_ts_encode_bearer_data(&ptClientTsData->u.cdma,ptRawTsData);break;
#endif
case WMS_FORMAT_GW_PP:ptRawTsData->tpdu_type=msg->tpdu_type;switch(msg->
tpdu_type){case WMS_TPDU_DELIVER:st=wms_ts_encode_deliver(&msg->u.deliver,
ptRawTsData);break;case WMS_TPDU_SUBMIT:st=wms_ts_encode_submit(&msg->u.submit,
ptRawTsData);break;case WMS_TPDU_STATUS_REPORT:st=wms_ts_encode_status_report(&
msg->u.status_report,ptRawTsData);break;
#if (0xc79+3740-0x1b15)
case WMS_TPDU_SUBMIT_REPORT_ACK:st=wms_ts_encode_submit_report_ack(&msg->u.
submit_report_ack,ptRawTsData);break;case WMS_TPDU_SUBMIT_REPORT_ERROR:st=
wms_ts_encode_submit_report_error(&msg->u.submit_report_error,ptRawTsData);break
;case WMS_TPDU_COMMAND:st=wms_ts_encode_command(&msg->u.command,ptRawTsData);
break;case WMS_TPDU_DELIVER_REPORT_ACK:st=wms_ts_encode_deliver_report_ack(&msg
->u.deliver_report_ack,ptRawTsData);break;case WMS_TPDU_DELIVER_REPORT_ERROR:st=
wms_ts_encode_deliver_report_error(&msg->u.deliver_report_error,ptRawTsData);
break;
#endif
default:at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x54\x50\x44\x55\x20\x74\x79\x70\x65\x20\x25\x64"
,msg->tpdu_type);st=WMS_INVALID_TPDU_TYPE_S;break;}break;default:st=
WMS_INVALID_FORMAT_S;at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x66\x6f\x72\x6d\x61\x74\x3a\x20\x25\x64",
ptClientTsData->format);break;}ptRawTsData->format=ptClientTsData->format;return
 st;}UINT8 wms_ts_unpack_gw_7_bit_chars(const UINT8*in,UINT8 in_len,UINT8 
out_len_max,UINT16 shift,UINT8*out){int i=(0x69+8471-0x2180);UINT16 pos=
(0x228a+39-0x22b1);if(in==NULL||out==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x75\x6e\x70\x61\x63\x6b\x5f\x67\x77\x5f\x37\x5f\x62\x69\x74\x5f\x63\x68\x61\x72\x73"
);return(0xcef+2663-0x1756);}if(shift!=(0x17fc+2648-0x2254))pos=pos+
(0x5e8+4312-0x16bf);if(shift==(0xcd7+4685-0x1f1d)){out[(0x8e1+3006-0x149f)]=in[
(0xaea+6581-0x249f)]>>(0x127f+362-0x13e8);shift=(0x66f+703-0x92e);i=
(0xa5f+6451-0x2391);}for(i=i;i<out_len_max&&i<in_len;i++,pos++){out[i]=(in[pos]
<<shift)&(0x343+7651-0x20a7);if(pos!=(0xc3c+1738-0x1306)){
#if (0x49+5375-0x1547)       
if(shift==(0xfdf+1679-0x166e)){out[i]|=(0x12c0+3053-0x1ead);}else{out[i]|=in[pos
-(0x2dd+4508-0x1478)]>>((0x17f+2090-0x9a1)-shift);}
#else
out[i]|=in[pos-(0x358+3008-0xf17)]>>((0x13d7+4710-0x2635)-shift);
#endif
}shift++;if(shift==(0xf21+2676-0x198e)){shift=(0x175f+2107-0x1f9a);i++;if(i>=
out_len_max){at_print(LOG_DEBUG,
"\x4e\x6f\x74\x20\x65\x6e\x6f\x75\x67\x68\x20\x6f\x75\x74\x70\x75\x74\x20\x62\x75\x66\x66\x65\x72\x20\x66\x6f\x72\x20\x75\x6e\x70\x61\x63\x6b\x69\x6e\x67\x21"
);break;}out[i]=in[pos]>>(0x24b+1276-0x746);}}return(UINT8)(pos);}UINT8 
wms_ts_decode_address(const UINT8*data,wms_address_s_type*addr){UINT8 i,pos=
(0xe1+7336-0x1d89);i=data[pos];if(i>WMS_GW_ADDRESS_MAX){at_print(LOG_DEBUG,
"\x41\x64\x64\x72\x20\x6c\x65\x6e\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x20\x25\x64"
,i);return(0x2220+1236-0x26f4);}addr->number_of_digits=i;pos++;addr->digit_mode=
WMS_DIGIT_MODE_4_BIT;addr->number_type=(wms_number_type_e_type)((data[pos]&
(0x1a54+49-0x1a15))>>(0x1499+1550-0x1aa3));addr->number_plan=(
wms_number_plan_e_type)(data[pos]&(0xb8+189-0x166));pos++;if(addr->number_type==
WMS_NUMBER_ALPHANUMERIC){UINT8 bytes_increment=(0x14d+5240-0x15c5);addr->
digit_mode=WMS_DIGIT_MODE_8_BIT;bytes_increment=(addr->number_of_digits+
(0xf4c+354-0x10ad))/(0xf83+2373-0x18c6);addr->number_of_digits=(UINT8)(addr->
number_of_digits*(0x21db+44-0x2203)/(0xcbd+6162-0x24c8));(void)
wms_ts_unpack_gw_7_bit_chars(&data[pos],addr->number_of_digits,
WMS_GW_ADDRESS_MAX,(0xb97+1784-0x128f),addr->digits);pos+=bytes_increment;}else{
for(i=(0x4df+6193-0x1d10);i<addr->number_of_digits;i++){addr->digits[i++]=data[
pos]&(0x47b+3123-0x109f);addr->digits[i]=(data[pos]&(0x2d4+7787-0x204f))>>
(0x16d2+1953-0x1e6f);pos++;}}return pos;}UINT8 wms_ts_decode_dcs(const UINT8*
data,wms_gw_dcs_s_type*dcs){UINT8 pos=(0xecf+635-0x114a);UINT8 i;if(data==NULL||
dcs==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x64\x63\x73"
);return(0x540+7940-0x2444);}dcs->msg_class=WMS_MESSAGE_CLASS_NONE;dcs->
msg_waiting=WMS_GW_MSG_WAITING_NONE;dcs->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;
dcs->is_compressed=FALSE;i=(data[pos]&(0xb40+1779-0x1173))>>(0x11e9+1221-0x16a8)
;switch(i){case(0x1174+2501-0x1b39):dcs->is_compressed=data[pos]&
(0x42c+4813-0x16d9);if(data[pos]&(0x346+7823-0x21c5)){dcs->msg_class=(
wms_message_class_e_type)(data[pos]&(0x115b+4845-0x2445));}else{dcs->msg_class=
WMS_MESSAGE_CLASS_NONE;}dcs->alphabet=(wms_gw_alphabet_e_type)((data[pos]&
(0x1e02+1942-0x258c))>>(0x324+1235-0x7f5));break;case(0xa4f+1047-0xe63):if((data
[pos]&(0x446+4070-0x13fc))==(0x204b+837-0x2360)){dcs->alphabet=(data[pos]&
(0xea3+2807-0x1996))?WMS_GW_ALPHABET_8_BIT:WMS_GW_ALPHABET_7_BIT_DEFAULT;dcs->
msg_class=(wms_message_class_e_type)(data[pos]&(0x143a+3939-0x239a));dcs->
is_compressed=FALSE;dcs->msg_waiting=WMS_GW_MSG_WAITING_NONE_1111;}else{dcs->
is_compressed=FALSE;dcs->msg_class=WMS_MESSAGE_CLASS_NONE;if((data[pos]&
(0x1742+3592-0x251a))==(0x15a+8110-0x2108)){dcs->msg_waiting=
WMS_GW_MSG_WAITING_DISCARD;dcs->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;}else if(
(data[pos]&(0x16cb+345-0x17f4))==(0x3b5+7737-0x21de)){dcs->msg_waiting=
WMS_GW_MSG_WAITING_STORE;dcs->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;}else{dcs->
msg_waiting=WMS_GW_MSG_WAITING_STORE;dcs->alphabet=WMS_GW_ALPHABET_UCS2;}dcs->
msg_waiting_active=(data[pos]&(0x13f+1903-0x8a6))?TRUE:FALSE;dcs->
msg_waiting_kind=(wms_gw_msg_waiting_kind_e_type)(data[pos]&(0x1207+3912-0x214c)
);}break;default:at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x44\x43\x53\x3a\x20\x25\x78",data[pos]);dcs->
alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;dcs->is_compressed=FALSE;dcs->msg_waiting
=WMS_GW_MSG_WAITING_NONE;dcs->msg_class=WMS_MESSAGE_CLASS_NONE;break;}if(dcs->
alphabet>WMS_GW_ALPHABET_UCS2){dcs->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;}dcs
->raw_dcs_data=data[pos];pos++;return pos;}UINT8 wms_ts_decode_timestamp(const 
UINT8*data,wms_timestamp_s_type*timestamp){UINT8 pos=(0x1061+3795-0x1f34),i,j;if
(data==NULL||timestamp==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x74\x69\x6d\x65\x73\x74\x61\x6d\x70"
);return(0x6a2+4800-0x1962);}i=((data[pos]&(0xd40+1251-0x1214))<<
(0x6b9+5976-0x1e0d))+((data[pos]&(0xe24+3761-0x1be5))>>(0x1150+4544-0x230c));if(
!wms_ts_bcd_to_int(i,&j)){at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x59\x65\x61\x72\x20\x3a\x20\x25\x64"
,data[pos]);i=(0x3d3+1021-0x7d0);}timestamp->year=i;pos++;i=((data[pos]&
(0x1437+4393-0x2551))<<(0x1099+5726-0x26f3))+((data[pos]&(0xb93+6033-0x2234))>>
(0x6c3+6588-0x207b));if(wms_ts_bcd_to_int(i,&j)){if(j>(0x1a83+1151-0x1ef6)||j<
(0x217c+1077-0x25b0)){at_print(LOG_DEBUG,
"\x4d\x6f\x6e\x74\x68\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64"
,j);i=(0x17c4+588-0x1a0f);}}else{at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x4d\x6f\x6e\x74\x68\x20\x3a\x20\x25\x64"
,data[pos]);i=(0x10ed+3102-0x1d0a);}timestamp->month=i;pos++;i=((data[pos]&
(0x1432+2265-0x1cfc))<<(0x6df+2811-0x11d6))+((data[pos]&(0x615+7922-0x2417))>>
(0x9d0+5447-0x1f13));if(wms_ts_bcd_to_int(i,&j)){if(j>(0x23f+3617-0x1041)||j<
(0x11f7+2307-0x1af9)){at_print(LOG_DEBUG,
"\x44\x61\x79\x20\x69\x73\x20\x69\x6e\x76\x61\x6c\x69\x64\x3a\x20\x25\x64",j);i=
(0xdab+1377-0x130b);}}else{at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x44\x61\x79\x20\x3a\x20\x25\x64"
,data[pos]);i=(0x1088+333-0x11d4);}timestamp->day=i;pos++;i=((data[pos]&
(0x1599+668-0x1826))<<(0x84+1015-0x477))+((data[pos]&(0x1b14+2605-0x2451))>>
(0x42b+3154-0x1079));if(wms_ts_bcd_to_int(i,&j)){if(j>(0xf4c+2653-0x1992)){
at_print(LOG_DEBUG,
"\x48\x6f\x75\x72\x20\x69\x73\x20\x74\x6f\x6f\x20\x6c\x61\x72\x67\x65\x3a\x20\x25\x64"
,j);i=(0x3f2+3837-0x12ef);}}else{at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x48\x6f\x75\x72\x20\x3a\x20\x25\x64"
,data[pos]);i=(0x178b+2944-0x230b);}timestamp->hour=i;pos++;i=((data[pos]&
(0x158a+3323-0x2276))<<(0x65a+7859-0x2509))+((data[pos]&(0xb76+5292-0x1f32))>>
(0xa1f+3165-0x1678));if(wms_ts_bcd_to_int(i,&j)){if(j>(0x206+5167-0x15fa)){
at_print(LOG_DEBUG,
"\x4d\x69\x6e\x75\x74\x65\x20\x69\x73\x20\x74\x6f\x6f\x20\x6c\x61\x72\x67\x65\x3a\x20\x25\x64"
,j);i=(0x17b+8129-0x213c);}}else{at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x4d\x69\x6e\x75\x74\x65\x20\x3a\x20\x25\x64"
,data[pos]);i=(0x1394+360-0x14fc);}timestamp->minute=i;pos++;i=((data[pos]&
(0xedd+5996-0x263a))<<(0x1fa6+1037-0x23af))+((data[pos]&(0x3d1+32-0x301))>>
(0x5c5+6073-0x1d7a));if(wms_ts_bcd_to_int(i,&j)){if(j>(0x8bf+576-0xac4)){
at_print(LOG_DEBUG,
"\x53\x65\x63\x6f\x6e\x64\x20\x69\x73\x20\x74\x6f\x6f\x20\x6c\x61\x72\x67\x65\x3a\x20\x25\x64"
,i);i=(0x958+7271-0x25bf);}}else{at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x42\x43\x44\x20\x44\x69\x67\x69\x74\x73\x20\x69\x6e\x20\x45\x6e\x63\x6f\x64\x65\x64\x20\x54\x69\x6d\x65\x73\x74\x61\x6d\x70\x20\x53\x65\x63\x6f\x6e\x64\x20\x3a\x20\x25\x64"
,data[pos]);i=(0xa9+4197-0x110e);}timestamp->second=i;pos++;if(data[pos]&
(0x109a+1435-0x162d)){timestamp->timezone=(data[pos]&(0x1436+2624-0x1e6f))*
(0x2f0+2461-0xc83)+((data[pos]&(0x17f1+2159-0x1f70))>>(0x5b2+5054-0x196c));
timestamp->timezone*=(-(0xc4b+1190-0x10f0));}else{timestamp->timezone=(sint7)((
data[pos]&(0x1039+2216-0x18d2))*(0x596+6033-0x1d1d)+((data[pos]&
(0x1a82+2047-0x2191))>>(0x40a+8699-0x2601)));}if(timestamp->timezone>
(0x6cf+4283-0x175a)||timestamp->timezone<-(0xf15+1840-0x1615)){at_print(
LOG_DEBUG,
"\x54\x69\x6d\x65\x7a\x6f\x6e\x65\x20\x69\x73\x20\x6f\x75\x74\x20\x6f\x66\x20\x62\x6f\x75\x6e\x64\x3a\x20\x25\x64"
,timestamp->timezone);timestamp->timezone=(0xe3b+1557-0x1450);}pos++;return pos;
}static UINT8 wms_ts_decode_udh_concat_8(const UINT8*udh,wms_udh_s_type*
header_ptr){UINT8 pos=(0x1f4c+1495-0x2523);if(udh==NULL||header_ptr==NULL){
at_print(LOG_DEBUG,"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return
(0x597+5775-0x1c26);}if(udh[pos]<(0xa72+5114-0x1e69)){at_print(LOG_DEBUG,
"\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x43\x6f\x6e\x63\x61\x74\x20\x38\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x1919+2588-0x2335);}if(udh[pos+(0xeec+1847-0x1621)]==
(0x4a0+4961-0x1801)||udh[pos+(0xb66+1811-0x1276)]>udh[pos+(0xa4c+4007-0x19f1)]){
at_print(LOG_DEBUG,
"\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x43\x6f\x6e\x74\x61\x63\x74\x20\x38\x20\x77\x69\x74\x68\x20\x6f\x75\x74\x20\x6f\x66\x20\x62\x6f\x75\x6e\x64\x20\x6d\x61\x78\x20\x6d\x65\x73\x73\x61\x67\x65\x73"
);return(0x779+1564-0xd95);}pos++;header_ptr->header_id=WMS_UDH_CONCAT_8;
header_ptr->u.concat_8.msg_ref=udh[pos++];header_ptr->u.concat_8.total_sm=udh[
pos++];header_ptr->u.concat_8.seq_num=udh[pos++];return(udh[(0x5a2+5185-0x19e3)]
+(0x1c9+4933-0x150d));}static UINT8 wms_ts_decode_udh_concat16(const UINT8*udh,
wms_udh_s_type*header_ptr){UINT8 pos=(0x1868+32-0x1888);if(udh==NULL||header_ptr
==NULL){at_print(LOG_DEBUG,"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");
return(0x53d+57-0x576);}if(udh[pos]<(0xd29+206-0xdf3)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x43\x6f\x6e\x63\x61\x74\x31\x36\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x695+6494-0x1ff3);}if(udh[pos+(0xc8+6555-0x1a60)]==
(0x10d1+659-0x1364)||udh[pos+(0xc23+4214-0x1c95)]==(0x7e4+6095-0x1fb3)||udh[pos+
(0x232f+191-0x23ea)]>udh[pos+(0x1388+1184-0x1825)])return(0x18dc+1428-0x1e70);
header_ptr->header_id=WMS_UDH_CONCAT_16;pos++;header_ptr->u.concat_16.msg_ref=
udh[pos++];header_ptr->u.concat_16.msg_ref=(UINT16)(header_ptr->u.concat_16.
msg_ref<<(0x1a6c+1299-0x1f77))|udh[pos++];header_ptr->u.concat_16.total_sm=udh[
pos++];header_ptr->u.concat_16.seq_num=udh[pos++];return(udh[(0x56+4105-0x105f)]
+(0x988+3715-0x180a));}static UINT8 wms_ts_udh_decode_first_seg_check(const 
UINT8 len,const UINT8*data,UINT8*is_first_segment_ptr){UINT8 pos=
(0x562+2833-0x1073);UINT8 num_headers=(0x70a+7846-0x25b0);UINT8 udhl=
(0x6f7+1269-0xbec);UINT8 iedl=(0xb9a+3091-0x17ad);UINT8 iei=(0x464+7789-0x22d1);
*is_first_segment_ptr=TRUE;if(data==NULL||data[pos]==(0xc65+3757-0x1b12)||len==
(0x1516+4531-0x26c9)){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x75\x64\x68\x5f\x64\x65\x63\x6f\x64\x65\x5f\x66\x69\x72\x73\x74\x5f\x73\x65\x67\x5f\x63\x68\x65\x63\x6b"
);return FALSE;}udhl=data[pos];pos++;while((pos<udhl)&&(num_headers<
WMS_MAX_UD_HEADERS)){iei=data[pos];iedl=data[pos+(0xcdb+4716-0x1f46)];if(iei==
WMS_UDH_CONCAT_16){if(data[pos+(0x170+7676-0x1f67)]!=(0x13b+2836-0xc4e)){
at_print(LOG_DEBUG,
"\x57\x4d\x53\x5f\x55\x44\x48\x5f\x43\x4f\x4e\x43\x41\x54\x5f\x31\x36\x20\x6e\x6f\x74\x20\x66\x69\x72\x73\x74\x20\x73\x65\x67\x6d\x65\x6e\x74\x21"
);*is_first_segment_ptr=FALSE;return TRUE;}else{return TRUE;}}else{num_headers++
;pos+=((0xf45+4553-0x210c)+iedl);}}return TRUE;}static UINT8 
wms_ts_decode_udh_special_sm(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 
pos=(0x14a0+2707-0x1f33);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x76+9053-0x23d3);}if(udh
[pos]<(0xbe9+2119-0x142e)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x53\x70\x65\x63\x69\x61\x6c\x20\x53\x4d\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x1cb7+295-0x1dde);}pos++;header_ptr->header_id=
WMS_UDH_SPECIAL_SM;header_ptr->u.special_sm.msg_waiting=(
wms_gw_msg_waiting_e_type)((udh[pos]>>(0xf90+4718-0x21f7)==(0x147f+2863-0x1fae))
?WMS_GW_MSG_WAITING_DISCARD:WMS_GW_MSG_WAITING_STORE);header_ptr->u.special_sm.
msg_waiting_kind=(wms_gw_msg_waiting_kind_e_type)(udh[pos++]&
(0x1f1a+1138-0x230d));header_ptr->u.special_sm.message_count=udh[pos++];return(
udh[(0xddc+5457-0x232d)]+(0x14f2+8-0x14f9));}static UINT8 
wms_ts_decode_udh_port_8(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=
(0x2686+130-0x2708);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x1758+3935-0x26b7);}if(
udh[pos]<(0x277+1252-0x759)){at_print(LOG_DEBUG,
"\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x50\x6f\x72\x74\x20\x38\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xfb0+4320-0x2090);}pos++;header_ptr->header_id=WMS_UDH_PORT_8
;header_ptr->u.wap_8.dest_port=udh[pos++];header_ptr->u.wap_8.orig_port=udh[pos
++];return(udh[(0x153b+293-0x1660)]+(0x1961+632-0x1bd8));}static UINT8 
wms_ts_decode_udh_port16(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=
(0xcf8+6507-0x2663);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0xd1+9201-0x24c2);}if(udh
[pos]<(0x1daf+1448-0x2353)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x50\x6f\x72\x74\x31\x36\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xf30+1369-0x1489);}header_ptr->header_id=WMS_UDH_PORT_16;pos
++;header_ptr->u.wap_16.dest_port=udh[pos++];header_ptr->u.wap_16.dest_port=(
UINT16)(header_ptr->u.wap_16.dest_port<<(0x858+84-0x8a4))|udh[pos++];header_ptr
->u.wap_16.orig_port=udh[pos++];header_ptr->u.wap_16.orig_port=(UINT16)(
header_ptr->u.wap_16.orig_port<<(0x516+1948-0xcaa))|udh[pos++];return(udh[
(0x354+7066-0x1eee)]+(0x236+2811-0xd30));}static UINT8 
wms_ts_decode_udh_text_formatting(const UINT8*udh,wms_udh_s_type*header_ptr){
UINT8 pos=(0x2d0+6514-0x1c42);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG
,"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x378+8152-0x2350);}if(
udh[pos]<(0x1534+464-0x1701)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x54\x65\x78\x74\x20\x46\x6f\x72\x6d\x61\x74\x74\x69\x6e\x67\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x903+6176-0x2123);}if(udh[pos]>=(0xacb+7117-0x2694)){
header_ptr->u.text_formating.is_color_present=TRUE;}else{header_ptr->u.
text_formating.is_color_present=FALSE;}pos++;header_ptr->header_id=
WMS_UDH_TEXT_FORMATING;header_ptr->u.text_formating.start_position=udh[pos++];
header_ptr->u.text_formating.text_formatting_length=udh[pos++];header_ptr->u.
text_formating.alignment_type=(wms_udh_alignment_e_type)(udh[pos]&
(0x791+2809-0x1287));header_ptr->u.text_formating.font_size=(
wms_udh_font_size_e_type)((udh[pos]&(0xc9+7800-0x1f35))>>(0xc8+2189-0x953));
header_ptr->u.text_formating.style_bold=(udh[pos]&(0x15b8+984-0x1980))>>
(0xb3f+3537-0x190c);header_ptr->u.text_formating.style_italic=(udh[pos]&
(0x3e1+787-0x6d4))>>(0x97a+5124-0x1d79);header_ptr->u.text_formating.
style_underlined=(udh[pos]&(0x1f9+7922-0x20ab))>>(0x940+6414-0x2248);header_ptr
->u.text_formating.style_strikethrough=(udh[pos]&(0x527+1258-0x991))>>
(0xa15+6787-0x2491);pos++;if(header_ptr->u.text_formating.is_color_present){
header_ptr->u.text_formating.text_color_foreground=(wms_udh_text_color_e_type)(
udh[pos]&(0x194c+304-0x1a6d));header_ptr->u.text_formating.text_color_background
=(wms_udh_text_color_e_type)((udh[pos]&(0x1b40+845-0x1d9d))>>(0xcc4+925-0x105d))
;pos++;}return(udh[(0x333+9003-0x265e)]+(0x82d+7696-0x263c));}static UINT8 
wms_ts_decode_udh_pre_def_sound(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8
 pos=(0x1a55+1074-0x1e87);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x258+6067-0x1a0b);}if(
udh[pos]<(0xef5+2926-0x1a61)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x50\x72\x65\x20\x44\x65\x66\x69\x6e\x65\x64\x20\x53\x6f\x75\x6e\x64\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x112f+1607-0x1776);}pos++;header_ptr->header_id=
WMS_UDH_PRE_DEF_SOUND;header_ptr->u.pre_def_sound.position=udh[pos++];header_ptr
->u.pre_def_sound.snd_number=udh[pos++];return(udh[(0x196+8311-0x220d)]+
(0xae6+57-0xb1e));}static UINT8 wms_ts_decode_udh_user_def_sound(const UINT8*udh
,wms_udh_s_type*header_ptr){UINT8 pos=(0x14e1+2520-0x1eb9),j;if(udh==NULL||
header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0xf2+4241-0x1183);}if(udh
[pos]==(0x167d+3344-0x238d)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x55\x73\x65\x72\x20\x44\x65\x66\x69\x6e\x65\x64\x20\x53\x6f\x75\x6e\x64\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x6e\x6f\x20\x44\x61\x74\x61"
);return(0xc44+6456-0x257c);}header_ptr->header_id=WMS_UDH_USER_DEF_SOUND;
header_ptr->u.user_def_sound.data_length=udh[pos++]-(0xe25+3510-0x1bda);
header_ptr->u.user_def_sound.position=udh[pos++];if(header_ptr->u.user_def_sound
.data_length>WMS_UDH_MAX_SND_SIZE){at_print(LOG_DEBUG,
"\x4d\x61\x78\x20\x53\x69\x7a\x65\x20\x45\x78\x63\x65\x65\x64\x20\x48\x65\x61\x64\x65\x72\x20\x69\x64\x20\x25\x64\x20"
,header_ptr->header_id);return(0xa3f+3856-0x194f);}memset(header_ptr->u.
user_def_sound.user_def_sound,(0x677+1842-0xcaa),WMS_UDH_MAX_SND_SIZE);for(j=
(0x197+6692-0x1bbb);j<header_ptr->u.user_def_sound.data_length;j++)header_ptr->u
.user_def_sound.user_def_sound[j]=udh[pos++];return pos;}static UINT8 
wms_ts_decode_udh_pre_def_anim(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 
pos=(0x9ca+6615-0x23a1);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x15e4+2436-0x1f68);}if(
udh[pos]!=(0x43f+4520-0x15e5)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x50\x72\x65\x20\x44\x65\x66\x69\x6e\x65\x64\x20\x41\x6e\x69\x6d\x61\x74\x69\x6f\x6e\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x1141+4684-0x238d);}pos++;header_ptr->header_id=
WMS_UDH_PRE_DEF_ANIM;header_ptr->u.pre_def_anim.position=udh[pos++];header_ptr->
u.pre_def_anim.animation_number=udh[pos++];return pos;}static UINT8 
wms_ts_decode_udh_large_anim(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 
pos=(0x547+3794-0x1419),j,k;if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x11df+2457-0x1b78);}if(
udh[pos]!=(WMS_UDH_ANIM_NUM_BITMAPS*WMS_UDH_LARGE_BITMAP_SIZE+(0xec+3222-0xd81))
){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x4c\x61\x72\x67\x65\x20\x44\x65\x66\x69\x6e\x65\x64\x20\x41\x6e\x69\x6d\x61\x74\x69\x6f\x6e\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x97+7855-0x1f46);}header_ptr->header_id=WMS_UDH_LARGE_ANIM;
pos++;header_ptr->u.large_anim.position=udh[pos++];for(j=(0x14ff+3410-0x2251);j<
WMS_UDH_ANIM_NUM_BITMAPS;j++)for(k=(0x1003+4245-0x2098);k<
WMS_UDH_LARGE_BITMAP_SIZE;k++)header_ptr->u.large_anim.data[j][k]=udh[pos++];
return pos;}static UINT8 wms_ts_decode_udh_small_anim(const UINT8*udh,
wms_udh_s_type*header_ptr){UINT8 pos=(0x5b3+2838-0x10c9),j,k;if(udh==NULL||
header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x8c4+370-0xa36);}if(udh[
pos]!=(WMS_UDH_ANIM_NUM_BITMAPS*WMS_UDH_SMALL_BITMAP_SIZE+(0xb96+6453-0x24ca))){
at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x4c\x61\x72\x67\x65\x20\x44\x65\x66\x69\x6e\x65\x64\x20\x41\x6e\x69\x6d\x61\x74\x69\x6f\x6e\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xf14+4801-0x21d5);}header_ptr->header_id=WMS_UDH_SMALL_ANIM;
pos++;header_ptr->u.small_anim.position=udh[pos++];for(j=(0xbc9+3084-0x17d5);j<
WMS_UDH_ANIM_NUM_BITMAPS;j++)for(k=(0x867+5817-0x1f20);k<
WMS_UDH_SMALL_BITMAP_SIZE;k++)header_ptr->u.small_anim.data[j][k]=udh[pos++];
return pos;}static UINT8 wms_ts_decode_udh_large_picture(const UINT8*udh,
wms_udh_s_type*header_ptr){UINT8 pos=(0x16c0+3970-0x2642),j;if(udh==NULL||
header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x2a3+6478-0x1bf1);}if(
udh[pos]!=WMS_UDH_LARGE_PIC_SIZE+(0x1c25+2643-0x2677)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x4c\x61\x72\x67\x65\x20\x50\x69\x63\x74\x75\x72\x65\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xee6+4079-0x1ed5);}header_ptr->header_id=
WMS_UDH_LARGE_PICTURE;pos++;header_ptr->u.large_picture.position=udh[pos++];for(
j=(0xd71+1023-0x1170);j<WMS_UDH_LARGE_PIC_SIZE;j++)header_ptr->u.large_picture.
data[j]=udh[pos++];return pos;}static UINT8 wms_ts_decode_udh_small_picture(
const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=(0xa56+6169-0x226f),j;if(
udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0xd0+5062-0x1496);}if(udh
[pos]!=WMS_UDH_SMALL_PIC_SIZE+(0x65b+2322-0xf6c)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x53\x6d\x61\x6c\x6c\x20\x50\x69\x63\x74\x75\x72\x65\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x67\x6e\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x6c4+2533-0x10a9);}header_ptr->header_id=
WMS_UDH_SMALL_PICTURE;pos++;header_ptr->u.small_picture.position=udh[pos++];for(
j=(0x93b+7419-0x2636);j<WMS_UDH_SMALL_PIC_SIZE;j++)header_ptr->u.small_picture.
data[j]=udh[pos++];return pos;}static UINT8 wms_ts_decode_udh_var_picture(const 
UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=(0x9cd+1106-0xe1f),j,pic_size;if(
udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0xd58+4268-0x1e04);}if(
udh[pos]>WMS_UDH_VAR_PIC_SIZE+(0x56f+4522-0x1716)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x56\x61\x72\x20\x50\x69\x63\x74\x75\x72\x65\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xec7+974-0x1295);}if((udh[pos]-(0x23f5+587-0x263d))!=(udh[pos
+(0x6e3+46-0x70f)]*udh[pos+(0xa47+5768-0x20cc)])){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x56\x61\x72\x20\x50\x69\x63\x74\x75\x72\x65\x2c\x20\x70\x69\x63\x20\x73\x69\x7a\x65\x20\x76\x61\x6c\x75\x65\x20\x6d\x69\x73\x6d\x61\x74\x63\x68\x20\x77\x69\x74\x68\x20\x68\x65\x69\x67\x74\x20\x61\x6e\x64\x20\x77\x65\x69\x67\x68\x74"
);return(0x9f0+2507-0x13bb);}pic_size=udh[pos++]-(0x17c2+3141-0x2404);header_ptr
->header_id=WMS_UDH_VAR_PICTURE;header_ptr->u.var_picture.position=udh[pos++];
header_ptr->u.var_picture.width=(UINT8)(udh[pos++]*(0x188d+688-0x1b35));
header_ptr->u.var_picture.height=udh[pos++];for(j=(0xf6d+1663-0x15ec);j<pic_size
&&j<WMS_UDH_VAR_PIC_SIZE;j++)header_ptr->u.var_picture.data[j]=udh[pos++];return
 pos;}static UINT8 wms_ts_decode_udh_user_prompt(const UINT8*udh,wms_udh_s_type*
header_ptr){UINT8 pos=(0x20e2+663-0x2379);if(udh==NULL||header_ptr==NULL){
at_print(LOG_DEBUG,"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return
(0x1eda+1987-0x269d);}if(udh[pos]<(0x5fc+3315-0x12ee)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x55\x73\x65\x72\x20\x50\x72\x6f\x6d\x70\x74\x20\x70\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0x120c+2360-0x1b44);}pos++;header_ptr->header_id=
WMS_UDH_USER_PROMPT;header_ptr->u.user_prompt.number_of_objects=udh[pos++];
return(udh[(0x81d+1264-0xd0d)]+(0x9f+9604-0x2622));}static UINT8 
wms_ts_decode_udh_eo(const UINT8*udh,UINT8 first_segment,wms_udh_s_type*
header_ptr){UINT8 pos=(0x423+7341-0x20d0),udh_length;if(udh==NULL||header_ptr==
NULL){at_print(LOG_DEBUG,"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return
(0x13d4+2629-0x1e19);}if(udh[pos]==(0x5f2+15-0x601)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x45\x78\x74\x65\x6e\x64\x65\x64\x20\x4f\x62\x6a\x65\x63\x74\x20\x70\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x6e\x6f\x20\x44\x61\x74\x61"
);return(0x72a+4663-0x1961);}udh_length=udh[pos++];header_ptr->header_id=
WMS_UDH_EXTENDED_OBJECT;header_ptr->u.eo.first_segment=first_segment;if(
first_segment==TRUE){if(udh_length<WMS_UDH_OCTETS_EO_HEADER){return
(0x1217+317-0x1354);}header_ptr->u.eo.reference=udh[pos++];header_ptr->u.eo.
length=udh[pos++]<<(0x95c+4585-0x1b3d);header_ptr->u.eo.length|=udh[pos++];
header_ptr->u.eo.control=udh[pos++];header_ptr->u.eo.type=(wms_udh_eo_id_e_type)
udh[pos++];header_ptr->u.eo.position=udh[pos++]<<(0xfd+1078-0x52b);header_ptr->u
.eo.position|=udh[pos++];}header_ptr->u.eo.content.length=(udh_length-pos)+
(0x125+8075-0x20af);memcpy(header_ptr->u.eo.content.data,udh+pos,header_ptr->u.
eo.content.length);pos+=header_ptr->u.eo.content.length;return pos;}static UINT8
 wms_ts_decode_udh_rfc822(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=
(0x10f3+989-0x14d0);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x49d+6512-0x1e0d);}if(
udh[pos]<(0x124d+1940-0x19e0)){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x52\x66\x63\x38\x32\x32\x20\x50\x72\x65\x73\x65\x6e\x74\x20\x77\x69\x74\x68\x20\x69\x6e\x76\x61\x6c\x69\x64\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3d\x20\x25\x64"
,udh[pos]);return(0xdf9+5909-0x250e);}pos++;header_ptr->header_id=WMS_UDH_RFC822
;header_ptr->u.rfc822.header_length=udh[pos++];return(udh[(0xdc5+1272-0x12bd)]+
(0x1063+4359-0x2169));}static UINT8 wms_ts_decode_udh_nat_lang_ss(const UINT8*
udh,wms_udh_s_type*header_ptr){UINT8 pos=(0x190a+2433-0x228b);if(udh==NULL||
header_ptr==NULL){return(0x861+1157-0xce6);}if(udh[pos]!=
WMS_UDH_OCTETS_NAT_LANG_SS){return(0x9f+6934-0x1bb5);}pos++;header_ptr->
header_id=WMS_UDH_NAT_LANG_SS;if((WMS_UDH_NAT_LANG_TURKISH>udh[pos])||(
WMS_UDH_NAT_LANG_PORTUGUESE<udh[pos])){return(0x184b+3754-0x26f5);}header_ptr->u
.nat_lang_ss.nat_lang_id=(wms_udh_nat_lang_id_e_type)udh[pos++];return(udh[
(0xfb7+5682-0x25e9)]+(0x1c32+1882-0x238b));}static UINT8 
wms_ts_decode_udh_nat_lang_ls(const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 
pos=(0x11a6+3372-0x1ed2);if(udh==NULL||header_ptr==NULL){return(0x2fb+383-0x47a)
;}if(udh[pos]!=WMS_UDH_OCTETS_NAT_LANG_LS){return(0xc78+1692-0x1314);}pos++;
header_ptr->header_id=WMS_UDH_NAT_LANG_LS;if((WMS_UDH_NAT_LANG_TURKISH>udh[pos])
||(WMS_UDH_NAT_LANG_PORTUGUESE<udh[pos])){return(0x1fa1+68-0x1fe5);}header_ptr->
u.nat_lang_ls.nat_lang_id=(wms_udh_nat_lang_id_e_type)udh[pos++];return(udh[
(0x422+7639-0x21f9)]+(0x14db+257-0x15db));}static UINT8 wms_ts_decode_udh_other(
const UINT8*udh,wms_udh_s_type*header_ptr){UINT8 pos=(0x5a6+2995-0x1159),i=
(0x211a+1425-0x26ab);if(udh==NULL||header_ptr==NULL){at_print(LOG_DEBUG,
"\x75\x64\x68\x20\x69\x73\x20\x4e\x55\x4c\x4c");return(0x1533+2651-0x1f8e);}if(
udh[pos+(0xe16+3895-0x1d4c)]>WMS_UDH_OTHER_SIZE){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x48\x65\x61\x64\x65\x72\x20\x4f\x74\x68\x65\x72\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x65\x78\x63\x65\x65\x64\x69\x6e\x67\x20\x32\x32\x36"
);return(0x1af5+1187-0x1f98);}header_ptr->header_id=(wms_udh_id_e_type)udh[pos];
header_ptr->u.other.header_id=(wms_udh_id_e_type)udh[pos++];header_ptr->u.other.
header_length=udh[pos++];for(i=(0x75d+3057-0x134e);i<header_ptr->u.other.
header_length;i++){header_ptr->u.other.data[i]=udh[pos++];}return pos;}UINT8 
wms_ts_decode_user_data_header(const UINT8 len,const UINT8*data,UINT8*
num_headers_ptr,wms_udh_s_type*udh_ptr){UINT8 pos=(0x1aca+2193-0x235b);UINT8 
header_length=(0x5af+478-0x78d),num_headers=(0x46f+4776-0x1717);UINT8 udhl;UINT8
 first_segment=TRUE;if(data==NULL||len==(0x132d+2922-0x1e97)||data[pos]==
(0x14f9+323-0x163c)||num_headers_ptr==NULL||udh_ptr==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x75\x73\x65\x72\x5f\x64\x61\x74\x61\x5f\x68\x65\x61\x64\x65\x72"
);return(0x510+5766-0x1b96);}udhl=data[pos];pos++;while((pos<udhl)&&(num_headers
<WMS_MAX_UD_HEADERS)){switch(data[pos++]){case WMS_UDH_CONCAT_8:header_length=
wms_ts_decode_udh_concat_8(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_CONCAT_16:header_length=wms_ts_decode_udh_concat16(data+pos,&udh_ptr[
num_headers]);break;case WMS_UDH_SPECIAL_SM:header_length=
wms_ts_decode_udh_special_sm(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_PORT_8:header_length=wms_ts_decode_udh_port_8(data+pos,&udh_ptr[
num_headers]);break;case WMS_UDH_PORT_16:header_length=wms_ts_decode_udh_port16(
data+pos,&udh_ptr[num_headers]);break;case WMS_UDH_TEXT_FORMATING:header_length=
wms_ts_decode_udh_text_formatting(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_PRE_DEF_SOUND:header_length=wms_ts_decode_udh_pre_def_sound(data+pos,&
udh_ptr[num_headers]);break;case WMS_UDH_USER_DEF_SOUND:header_length=
wms_ts_decode_udh_user_def_sound(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_PRE_DEF_ANIM:header_length=wms_ts_decode_udh_pre_def_anim(data+pos,&
udh_ptr[num_headers]);break;case WMS_UDH_LARGE_ANIM:header_length=
wms_ts_decode_udh_large_anim(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_SMALL_ANIM:header_length=wms_ts_decode_udh_small_anim(data+pos,&udh_ptr[
num_headers]);break;case WMS_UDH_LARGE_PICTURE:header_length=
wms_ts_decode_udh_large_picture(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_SMALL_PICTURE:header_length=wms_ts_decode_udh_small_picture(data+pos,&
udh_ptr[num_headers]);break;case WMS_UDH_VAR_PICTURE:header_length=
wms_ts_decode_udh_var_picture(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_USER_PROMPT:header_length=wms_ts_decode_udh_user_prompt(data+pos,&
udh_ptr[num_headers]);break;case WMS_UDH_EXTENDED_OBJECT:
wms_ts_udh_decode_first_seg_check(len,data,&first_segment);header_length=
wms_ts_decode_udh_eo(data+pos,first_segment,&udh_ptr[num_headers]);break;case 
WMS_UDH_RFC822:header_length=wms_ts_decode_udh_rfc822(data+pos,&udh_ptr[
num_headers]);break;case WMS_UDH_NAT_LANG_SS:header_length=
wms_ts_decode_udh_nat_lang_ss(data+pos,&udh_ptr[num_headers]);break;case 
WMS_UDH_NAT_LANG_LS:header_length=wms_ts_decode_udh_nat_lang_ls(data+pos,&
udh_ptr[num_headers]);break;default:pos--;header_length=wms_ts_decode_udh_other(
data+pos,&udh_ptr[num_headers]);break;}if((UINT16)pos+(UINT16)header_length>
WMS_MAX_LEN){at_print(LOG_DEBUG,
"\x6e\x75\x6d\x62\x65\x72\x20\x6f\x66\x20\x62\x79\x74\x65\x73\x20\x64\x65\x63\x6f\x64\x65\x64\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x55\x44\x48\x4c\x20\x76\x61\x6c\x75\x65\x20\x6f\x66\x20\x25\x64"
,udhl);return(0x203+2623-0xc42);}else if(header_length!=(0xd51+1884-0x14ad)){pos
+=header_length;num_headers++;}else{at_print(LOG_DEBUG,
"\x42\x61\x64\x20\x55\x44\x48\x3a\x20\x70\x6f\x73\x3d\x25\x64\x2c\x20\x64\x61\x74\x61\x5b\x70\x6f\x73\x5d\x3d\x25\x64"
,pos,data[pos]);*num_headers_ptr=(0x580+2447-0xf0f);return(0xe1c+4118-0x1e32);}}
if(num_headers>=WMS_MAX_UD_HEADERS){at_print(LOG_DEBUG,
"\x64\x65\x63\x6f\x64\x65\x5f\x75\x64\x68\x3a\x20\x4e\x75\x6d\x20\x48\x65\x61\x64\x65\x72\x73\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x57\x4d\x53\x5f\x4d\x41\x58\x5f\x55\x44\x5f\x48\x45\x41\x44\x45\x52\x53"
);pos=udhl+(0x12c6+4249-0x235e);}if(pos!=(udhl+(0x365+1248-0x844))){at_print(
LOG_DEBUG,
"\x53\x4d\x53\x20\x55\x44\x48\x20\x63\x6f\x75\x6c\x64\x20\x6e\x6f\x74\x20\x62\x65\x20\x64\x65\x63\x6f\x64\x65\x64"
);num_headers=(0x510+103-0x577);udhl=(0x629+1687-0xcc0);}if(num_headers>
(0x186b+3383-0x25a2)){*num_headers_ptr=num_headers;}return udhl;}UINT8 
wms_ts_decode_gw_user_data(const wms_gw_dcs_s_type*dcs,const UINT8 len,const 
UINT8*data,const UINT8 user_data_header_present,wms_gw_user_data_s_type*
user_data){UINT8 i,pos=(0x1ab2+1055-0x1ed1);UINT8 fill_bits=(0x3e8+2026-0xbd2);
UINT8 user_data_length;UINT8 user_data_header_length=(0xf25+3907-0x1e68);if(dcs
==NULL||data==NULL||user_data==NULL){at_print(LOG_DEBUG,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x67\x77\x5f\x75\x73\x65\x72\x5f\x64\x61\x74\x61"
);return(0x5a+7419-0x1d55);}(void)memset(user_data,(0x11cc+4273-0x227d),sizeof(
wms_gw_user_data_s_type));if(len==(0xe12+6376-0x26fa)){return(0xd0+3315-0xdc3);}
if(dcs->alphabet==WMS_GW_ALPHABET_7_BIT_DEFAULT){if(len>WMS_SMS_UDL_MAX_7_BIT){
at_print(LOG_DEBUG,
"\x75\x73\x65\x72\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3e\x20\x6d\x61\x78\x20\x76\x61\x6c\x75\x65\x20\x66\x6f\x72\x20\x67\x77\x20\x37\x2d\x62\x69\x74\x20\x61\x6c\x70\x68\x61\x62\x65\x74"
);return(0x17a0+1563-0x1dbb);}user_data_length=len;if(user_data_header_present){
user_data_header_length=wms_ts_decode_user_data_header(data[pos],data+pos,&
user_data->num_headers,user_data->headers);}if(user_data_header_length>len){
at_print(LOG_DEBUG,
"\x75\x73\x65\x72\x20\x64\x61\x74\x61\x20\x68\x65\x61\x64\x65\x72\x20\x6c\x65\x6e\x67\x74\x68\x20\x3e\x20\x74\x6f\x74\x61\x6c\x20\x6c\x65\x6e\x67\x74\x68"
);return(0xc94+3365-0x19b9);}if(user_data_header_length>(0x41b+8505-0x2554)){
fill_bits=((len*(0xf39+4176-0x1f82))-((user_data_header_length+
(0xa8b+2722-0x152c))*(0x723+2714-0x11b5)))%(0x871+1180-0xd06);user_data_length=(
UINT8)(((len*(0x1250+940-0x15f5))-((user_data_header_length+(0x153a+19-0x154c))*
(0x916+4793-0x1bc7)))/(0x8bf+2636-0x1304));pos=user_data_header_length+
(0x9+1815-0x71f);if(fill_bits!=(0x10e2+5003-0x246d)){fill_bits=
(0x11e4+4857-0x24d5)-fill_bits;}}i=wms_ts_unpack_gw_7_bit_chars(&data[pos],
user_data_length,WMS_MAX_LEN,fill_bits,user_data->sm_data);user_data->sm_len=
user_data_length;}else{if(len>WMS_SMS_UDL_MAX_8_BIT){at_print(LOG_DEBUG,
"\x75\x73\x65\x72\x20\x64\x61\x74\x61\x20\x6c\x65\x6e\x67\x74\x68\x20\x3e\x20\x6d\x61\x78\x20\x76\x61\x6c\x75\x65\x20\x66\x6f\x72\x20\x38\x2d\x62\x69\x74\x20\x63\x68\x61\x72\x61\x72\x61\x63\x74\x65\x72\x73"
);return(0x1816+445-0x19d3);}user_data_length=len;if(user_data_header_present){
user_data_header_length=wms_ts_decode_user_data_header(data[pos],data+pos,&
user_data->num_headers,user_data->headers);if(user_data_header_length>len){
at_print(LOG_DEBUG,
"\x75\x73\x65\x72\x20\x64\x61\x74\x61\x20\x68\x65\x61\x64\x65\x72\x20\x6c\x65\x6e\x67\x74\x68\x20\x3e\x20\x74\x6f\x74\x61\x6c\x20\x6c\x65\x6e\x67\x74\x68"
);return(0xb68+5602-0x214a);}pos+=user_data_header_length+(0x215+5369-0x170d);
user_data_length=(len-user_data_header_length)-(0x1675+1753-0x1d4d);}memcpy(
user_data->sm_data,data+pos,user_data_length);user_data->sm_len=user_data_length
;i=(UINT8)user_data->sm_len;}pos+=i;return pos;}wms_status_e_type 
wms_ts_decode_deliver(const T_zUfiSms_RawTsData*ptRawTsData,
wms_gw_deliver_s_type*deliver){wms_status_e_type st=WMS_OK_S;uint32 pos=
(0x7e8+4902-0x1b0e),i;const UINT8*data=ptRawTsData->data;if(ptRawTsData==NULL||
deliver==NULL){printf(
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x64\x65\x6c\x69\x76\x65\x72"
);return WMS_NULL_PTR_S;}else if((data[pos]&(0x656+3234-0x12f5))!=
(0xc28+3575-0x1a1f)){printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x74\x70\x64\x75\x20\x74\x79\x70\x65\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x64\x65\x6c\x69\x76\x65\x72"
);return WMS_INVALID_TPDU_TYPE_S;}else{deliver->more=(data[pos]&
(0x1e39+674-0x20d7))?FALSE:TRUE;deliver->status_report_enabled=(data[pos]&
(0x18ad+781-0x1b9a))?TRUE:FALSE;deliver->user_data_header_present=(data[pos]&
(0x1e95+233-0x1f3e))?TRUE:FALSE;deliver->reply_path_present=(data[pos]&
(0x1f09+89-0x1ee2))?TRUE:FALSE;pos++;i=wms_ts_decode_address(&data[pos],&deliver
->address);if(i==(0x1190+3651-0x1fd3)){printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x70\x61\x72\x61\x6d\x20\x73\x69\x7a\x65\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x64\x65\x6c\x69\x76\x65\x72"
);return WMS_INVALID_PARM_SIZE_S;}pos+=i;deliver->pid=(wms_pid_e_type)data[pos];
pos++;pos+=wms_ts_decode_dcs(data+pos,&deliver->dcs);if(deliver->dcs.
msg_waiting_kind!=WMS_GW_MSG_WAITING_VOICEMAIL){if(deliver->pid==
WMS_PID_RETURN_CALL){deliver->dcs.msg_waiting=WMS_GW_MSG_WAITING_STORE;deliver->
dcs.msg_waiting_active=TRUE;deliver->dcs.msg_waiting_kind=
WMS_GW_MSG_WAITING_VOICEMAIL;}}i=wms_ts_decode_timestamp(data+pos,&deliver->
timestamp);if(i==(0x450+8648-0x2618)){printf(
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x70\x61\x72\x61\x6d\x20\x76\x61\x6c\x75\x65\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x64\x65\x6c\x69\x76\x65\x72"
);return WMS_INVALID_PARM_VALUE_S;}pos+=i;pos++;i=wms_ts_decode_gw_user_data(&
deliver->dcs,data[pos-(0x1187+3257-0x1e3f)],data+pos,deliver->
user_data_header_present,&deliver->user_data);if(i>WMS_SMS_UDL_MAX_8_BIT){printf
(
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79\x3a\x20\x55\x44\x4c\x20\x3d\x20\x25\x6c\x75"
,i);st=WMS_INVALID_USER_DATA_SIZE_S;}pos+=i;return st;}}UINT8 
wms_ts_decode_gw_validity(const UINT8*data,wms_gw_validity_s_type*validity){
UINT8 i,pos=(0x3c9+2042-0xbc3);if(data==NULL||validity==NULL){at_print(LOG_DEBUG
,
"\x6e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x67\x77\x5f\x76\x61\x6c\x69\x64\x69\x74\x79"
);return(0xc57+4643-0x1e7a);}else{switch(validity->format){case 
WMS_GW_VALIDITY_NONE:memset(validity,(0x8e2+7062-0x2478),sizeof(
wms_gw_validity_s_type));break;case WMS_GW_VALIDITY_RELATIVE:
zUfiSms_DecodeRelativeTime(data[pos],&validity->u.time);pos++;break;case 
WMS_GW_VALIDITY_ABSOLUTE:i=wms_ts_decode_timestamp(data+pos,&validity->u.time);
pos+=i;break;case WMS_GW_VALIDITY_ENHANCED:break;default:break;}return pos;}}
wms_status_e_type wms_ts_decode_submit(const T_zUfiSms_RawTsData*ptRawTsData,
wms_gw_submit_s_type*submit){wms_status_e_type st=WMS_OK_S;uint32 pos=
(0xd65+1327-0x1294),i;const UINT8*data;if(ptRawTsData==NULL||submit==NULL){
printf(
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x73\x75\x62\x6d\x69\x74\x21"
);return WMS_NULL_PTR_S;}data=ptRawTsData->data;submit->reject_duplicates=(data[
pos]&(0xc06+2515-0x15d5))?TRUE:FALSE;submit->validity.format=(
wms_gw_validity_format_e_type)((data[pos]&(0x1842+2457-0x21c3))>>
(0x2087+475-0x225f));submit->status_report_enabled=(data[pos]&
(0x1790+1888-0x1ed0))?TRUE:FALSE;submit->user_data_header_present=(data[pos]&
(0x1cfd+1960-0x2465))?TRUE:FALSE;submit->reply_path_present=(data[pos]&
(0x51b+7507-0x21ee))?TRUE:FALSE;pos++;submit->message_reference=data[pos];pos++;
i=wms_ts_decode_address(&data[pos],&submit->address);if(i==(0xedb+2641-0x192c)){
return WMS_INVALID_PARM_SIZE_S;}pos+=i;submit->pid=(wms_pid_e_type)data[pos];pos
++;pos+=wms_ts_decode_dcs(data+pos,&submit->dcs);i=wms_ts_decode_gw_validity(
data+pos,&submit->validity);if((submit->validity.format!=WMS_GW_VALIDITY_NONE)&&
(i==(0x13dc+561-0x160d))){return WMS_INVALID_PARM_VALUE_S;}pos+=i;pos++;i=
wms_ts_decode_gw_user_data(&submit->dcs,data[pos-(0x15ed+1762-0x1cce)],data+pos,
submit->user_data_header_present,&submit->user_data);if(i>WMS_SMS_UDL_MAX_8_BIT)
{printf(
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79\x3a\x20\x55\x44\x4c\x20\x3d\x20\x25\x6c\x75"
,i);st=WMS_INVALID_USER_DATA_SIZE_S;}pos+=i;return st;}wms_status_e_type 
wms_ts_decode_status_report(const T_zUfiSms_RawTsData*ptRawTsData,
wms_gw_status_report_s_type*status_report){wms_status_e_type st=WMS_OK_S;uint32 
pos=(0x25c+3689-0x10c5),i;const UINT8*data;if(ptRawTsData==NULL||status_report==
NULL){printf(
"\x4e\x75\x6c\x6c\x20\x70\x6f\x69\x6e\x74\x65\x72\x20\x69\x6e\x20\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x5f\x73\x74\x61\x74\x75\x73\x5f\x72\x65\x70\x6f\x72\x74\x21"
);return WMS_NULL_PTR_S;}data=ptRawTsData->data;status_report->more=data[pos]&
(0x704+4790-0x19b6)?FALSE:TRUE;status_report->status_report_qualifier=data[pos]&
(0x19ad+1777-0x207e)?TRUE:FALSE;status_report->user_data_header_present=(data[
pos]&(0x14d1+4198-0x24f7))?TRUE:FALSE;pos++;status_report->message_reference=
data[pos];pos++;i=wms_ts_decode_address(&data[pos],&status_report->address);if(i
==(0x1210+4634-0x242a)){return WMS_INVALID_PARM_SIZE_S;}pos+=i;i=
wms_ts_decode_timestamp(data+pos,&status_report->timestamp);if(i==
(0x5a6+2081-0xdc7)){return WMS_INVALID_PARM_VALUE_S;}pos+=i;i=
wms_ts_decode_timestamp(data+pos,&status_report->discharge_time);if(i==
(0x7f6+7844-0x269a)){return WMS_INVALID_PARM_VALUE_S;}pos+=i;status_report->
tp_status=(wms_tp_status_e_type)data[pos];pos++;status_report->mask=data[pos];
status_report->pid=(wms_pid_e_type)(0x3a4+6912-0x1ea4);status_report->user_data.
sm_len=(0x1c15+468-0x1de9);status_report->mask&=(0x17e7+119-0x175f);if((
status_report->mask!=(0x9ca+989-0xca8))&&(status_report->mask!=
(0x193c+639-0x1bbb))){pos++;if(status_report->mask&WMS_TPDU_MASK_PID){
status_report->pid=(wms_pid_e_type)data[pos];pos++;}if(status_report->mask&
WMS_TPDU_MASK_DCS){pos+=wms_ts_decode_dcs(data+pos,&status_report->dcs);}if(
status_report->mask&WMS_TPDU_MASK_USER_DATA){pos++;i=wms_ts_decode_gw_user_data(
&status_report->dcs,data[pos-(0x8+4739-0x128a)],data+pos,status_report->
user_data_header_present,&status_report->user_data);if(i>WMS_SMS_UDL_MAX_8_BIT){
printf(
"\x55\x73\x65\x72\x20\x44\x61\x74\x61\x20\x4c\x65\x6e\x67\x74\x68\x20\x68\x61\x73\x20\x65\x78\x63\x65\x65\x64\x65\x64\x20\x63\x61\x70\x61\x63\x69\x74\x79\x3a\x20\x55\x44\x4c\x20\x3d\x20\x25\x6c\x75"
,i);st=WMS_INVALID_USER_DATA_SIZE_S;}pos+=i;}}else{status_report->mask=
(0x1c69+1134-0x20d7);}return st;}wms_status_e_type wms_ts_decode(const 
T_zUfiSms_RawTsData*ptRawTsData,T_zUfiSms_ClientTsData*ptClientTsData){
wms_status_e_type st=WMS_OK_S;wms_gw_pp_ts_data_s_type*msg;if(ptRawTsData==NULL
||ptClientTsData==NULL){return WMS_NULL_PTR_S;}msg=&ptClientTsData->u.gw_pp;
switch(ptRawTsData->format){case WMS_FORMAT_CDMA:case WMS_FORMAT_ANALOG_AWISMS:
case WMS_FORMAT_ANALOG_CLI:case WMS_FORMAT_ANALOG_VOICE_MAIL:case 
WMS_FORMAT_ANALOG_SMS:case WMS_FORMAT_MWI:at_print(AT_DEBUG,
"\x21\x21\x21\x21\x21\x21\x21\x21\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x20\x45\x72\x72\x6f\x72\x3a\x20\x47\x65\x74\x20\x4f\x6d\x69\x74\x20\x66\x6f\x72\x6d\x61\x74\x21\x21\x21\x20\x46\x6f\x72\x6d\x61\x74\x5b\x25\x64\x5d" "\n"
,ptRawTsData->format);break;case WMS_FORMAT_GW_PP:msg->tpdu_type=ptRawTsData->
tpdu_type;switch(msg->tpdu_type){case WMS_TPDU_DELIVER:st=wms_ts_decode_deliver(
ptRawTsData,&msg->u.deliver);break;case WMS_TPDU_SUBMIT:st=wms_ts_decode_submit(
ptRawTsData,&msg->u.submit);break;case WMS_TPDU_STATUS_REPORT:st=
wms_ts_decode_status_report(ptRawTsData,&msg->u.status_report);break;case 
WMS_TPDU_DELIVER_REPORT_ACK:case WMS_TPDU_DELIVER_REPORT_ERROR:case 
WMS_TPDU_SUBMIT_REPORT_ACK:case WMS_TPDU_SUBMIT_REPORT_ERROR:case 
WMS_TPDU_COMMAND:at_print(AT_DEBUG,
"\x21\x21\x21\x21\x21\x21\x21\x21\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x20\x45\x72\x72\x6f\x72\x3a\x20\x47\x65\x74\x20\x4f\x6d\x69\x74\x20\x62\x72\x61\x6e\x63\x68\x21\x21\x21\x20\x74\x70\x64\x75\x5f\x74\x79\x70\x65\x5b\x25\x64\x5d" "\n"
,msg->tpdu_type);break;default:at_print(LOG_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x54\x50\x44\x55\x20\x74\x79\x70\x65\x20\x25\x64"
,msg->tpdu_type);st=WMS_INVALID_TPDU_TYPE_S;break;}break;case WMS_FORMAT_GW_CB:
at_print(AT_DEBUG,
"\x21\x21\x21\x21\x21\x21\x21\x21\x77\x6d\x73\x5f\x74\x73\x5f\x64\x65\x63\x6f\x64\x65\x20\x45\x72\x72\x6f\x72\x3a\x20\x47\x65\x74\x20\x4f\x6d\x69\x74\x20\x66\x6f\x72\x6d\x61\x74\x21\x21\x21\x20\x46\x6f\x72\x6d\x61\x74\x5b\x25\x64\x5d" "\n"
,ptRawTsData->format);break;default:st=WMS_INVALID_FORMAT_S;at_print(AT_DEBUG,
"\x49\x6e\x76\x61\x6c\x69\x64\x20\x66\x6f\x72\x6d\x61\x74\x3a\x20\x25\x64",
ptRawTsData->format);break;}ptClientTsData->format=ptRawTsData->format;return st
;}
