
#include <limits.h>
#include "sms_code.h"
#include <sys/msg.h>
T_zUfiSms_ConcatInfo g_zUfiSms_ConcatSms;T_zUfiSms_GroupInfo g_zUfiSms_GroupSms;
T_zUfiSms_DbStoreData g_zUfiSms_DbStoreData[ZTE_WMS_CONCAT_SMS_COUNT_MAX];UINT8 
g_zUfiSms_MemFullFlag[ZTE_WMS_MEMORY_MAX]={FALSE,FALSE};T_zUfiSms_DelSms 
g_zUfiSms_DelMsg;T_zUfiSms_DelIndexInfo g_deleteIndex={(0x148a+959-0x1849)};
T_zUfiSms_ModifySms g_zUfiSms_modifyMsg={(0x35f+6364-0x1c3b)};
T_zUfiSms_ModifyIndexInfo g_modifyIndex={(0x5a0+250-0x69a)};int 
g_zUfiSms_UnitLen=(0x1031+4186-0x208b);int g_zUfiSms_ConcatTotalNum=
(0xe41+5361-0x2332);int g_zUfiSms_CurConcatSegNo=(0xbd9+4007-0x1b80);UINT8 
g_zUfiSms_IsConcatSendSuc=TRUE;int g_zUfiSms_SendFailedCount=(0x464+1689-0xafd);
int g_zUfiSms_MsgRefer=(0x2109+1172-0x259d);int g_zUfiSms_SendFailedRetry=
(0x711+5222-0x1b77);char g_Zmena_value[(0xac6+4765-0x1d61)]={(0x892+6933-0x23a7)
};int g_displaymode=(0xb63+2204-0x13ff);extern SMS_LOCATION 
g_zUfiSms_CurLocation;extern T_zUfiSms_CmdStatus zUfiSms_HandleReport(unsigned 
char*ptPduData);extern void zUfiSms_GetReportStatus(char*pdu_tmp,int*stat);
extern VOID zUfiSms_ResendAtCmdZmena(int cid);extern int zUfiSms_DecodeSmsData(
T_zUfiSms_DbStoreData*pDb_Data,int msg_index,zUfiSms_StoreType iStorePos,
T_SmsStatus bSms_Status,wms_message_format_enum_v01 format,long iPdu_Len,
unsigned char*pPdu_Received);typedef struct{long mtype;char mtext[
(0x1ce5+1713-0x238a)];}FOTA_MSG_BUF;
#define WEBUI_NOTIFY_PUSH_MSG_  (0x1150+5251-0x25d0)
typedef struct{unsigned int isread_record;unsigned int inbox_full;}
T_zUfiMmi_SmsRecord;void zUfiMmi_SendSmsStatus(void){int iSmsNum=
(0x7d5+5519-0x1d64);T_zUfiMmi_SmsRecord tRecord={(0x1ba7+1102-0x1ff5)};CHAR 
smsNum[(0x7a6+4119-0x178b)]={(0xa5c+5372-0x1f58)};sc_cfg_get(NV_SMS_IN_NUM,
smsNum,sizeof(smsNum));iSmsNum=atoi(smsNum);tRecord.isread_record=
zUfiSms_IsUnreadSms(ZTE_WMS_MEMORY_MAX);if(iSmsNum>=ZSMS_NUM_MAX_CPE){tRecord.
inbox_full=(0xf95+4843-0x227f);}else{tRecord.inbox_full=(0x1049+2750-0x1b07);}}
VOID zUfiSms_Init(VOID){zUfiSms_CfgInit();zUfiSms_ChangeMainState(
SMS_STATE_INITING);}VOID zSvr_Zpbic_Sms_Init(VOID){int atRes=(0x21d1+837-0x2516)
;T_zUfiSms_StatusInfo tStatus={(0x1461+169-0x150a)};CHAR outDate[
(0x11a7+4508-0x2311)]={(0x2d4+211-0x3a7)};zUfiSms_Init();zUfiSms_InitCmdStatus(&
tStatus,WMS_SMS_CMD_INIT);zSms_SendSmsInitReq();at_print(LOG_DEBUG,
"corem zSvr_Zpbic_Sms_Init has send init req\n");zSms_SendZmenaReq(
(0xd49+886-0x10bf));at_print(LOG_DEBUG,
"\x63\x6f\x72\x65\x6d\x20\x7a\x53\x76\x72\x5f\x5a\x70\x62\x69\x63\x5f\x53\x6d\x73\x5f\x49\x6e\x69\x74\x20\x68\x61\x73\x20\x73\x65\x6e\x64\x20\x7a\x6d\x65\x6e\x61\x20\x72\x65\x71" "\n"
);sc_cfg_get(NV_OUTDATE_DELETE,outDate,sizeof(outDate));at_print(LOG_DEBUG,
"\x63\x6f\x72\x65\x6d\x20\x7a\x53\x76\x72\x5f\x5a\x70\x62\x69\x63\x5f\x53\x6d\x73\x5f\x49\x6e\x69\x74\x20\x6f\x75\x74\x64\x61\x74\x65\x20\x63\x68\x65\x63\x6b\x20\x25\x73" "\n"
,outDate);if((0x322+3677-0x117f)==strcmp(outDate,"\x31")){atWeb_OutdateSmsCheck(
ZUFI_NULL);}}VOID zUfiSms_DeleteAllSimSms(VOID){zUfiSms_DeleteAllSimSmsInDb();}
#if (0x19d7+69-0x1a1c)
VOID zUfiSms_ResendAtCmdZmena(int cid){CHAR netType[(0x932+4123-0x191b)]={
(0xd35+3582-0x1b33)};sc_cfg_get(NV_NETWORK_TYPE,netType,sizeof(netType));if(!
g_Zmena_rsp&&((0xfd0+1916-0x174c)!=strcmp(
"\x4e\x6f\x20\x53\x65\x72\x76\x69\x63\x65",netType)&&(0x100a+5709-0x2657)!=
strcmp("\x4c\x69\x6d\x69\x74\x65\x64\x20\x53\x65\x72\x76\x69\x63\x65",netType)))
{atUnsoli_Report_Zmena(NULL,cid);}}
#endif
void zUfiSms_Ack_new_msg(BOOL needAck){
#if (0x53d+2626-0xf7f)
CHAR ackPduStr[(0x16ad+2101-0x1d52)]={(0x397+254-0x495)};SMS_PARAM reportParam={
(0x162f+1355-0x1b7a)};int total_length=(0xef3+399-0x1082);UINT8 TP_FCS=
(0x56b+4881-0x187c);CHAR strValue[(0x1c34+1978-0x23ec)]={(0x6b8+5869-0x1da5)};if
(needAck){TP_FCS=(0x562+4299-0x162d);sprintf(strValue,"\x25\x64",
(0xd31+3826-0x1c22));}else{TP_FCS=(0x1071+2068-0x17b2);sprintf(strValue,
"\x25\x64",(0x85f+3894-0x1793));}sprintf(reportParam.SCA,"\x25\x73",cfg_get(
"\x73\x6d\x73\x5f\x63\x65\x6e\x74\x65\x72\x5f\x6e\x75\x6d"));total_length=
zUfiSms_EncodePdu_DeliverReport(&reportParam,ackPduStr,TP_FCS);memset(&
g_zUfiSms_ackPdu,(0x17+8922-0x22f1),sizeof(g_zUfiSms_ackPdu));g_zUfiSms_ackPdu.
length=String2Bytes(ackPduStr,g_zUfiSms_ackPdu.pdu,strlen(ackPduStr));memset(
g_zUfiSms_ackPdu.pdu,(0x1db5+537-0x1fce),sizeof(g_zUfiSms_ackPdu.pdu));memcpy(&
g_zUfiSms_ackPdu.pdu,&ackPduStr,sizeof(ackPduStr));atBase_SendMsgToSelf(
ZAT_CNMA_CMD,strValue,sizeof(strValue));
#endif
#if (0x892+772-0xb96)
CHAR strValue[(0x10dc+3874-0x1ffc)]={(0x3dc+6833-0x1e8d)};if(needAck){snprintf(
strValue,sizeof(strValue),"\x25\x64",(0xae0+1838-0x120d));}else{snprintf(
strValue,sizeof(strValue),"\x25\x64",(0xcdf+5245-0x215a));}zSvr_InnerSendMsg(
ZUFI_MODULE_ID_AT_LOCAL,ZUFI_MODULE_ID_AT_UNSOLI,MSG_CMD_AT_CNMA,strlen(strValue
),strValue);
#endif
if(needAck){zSms_SendCnmaReq((0x14f3+4122-0x250c));}else{zSms_SendCnmaReq(
(0x897+3738-0x172f));}}T_zUfiSms_CmdStatus zUfiSms_SendRawSms(T_zUfiSms_SendReq*
ptSendMsg){if(NULL==ptSendMsg||(0x1682+4074-0x266c)==ptSendMsg->receiver_count){
return WMS_CMD_FAILED;}at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x52\x61\x77\x53\x6d\x73\x20\x72\x65\x63\x65\x69\x76\x65\x72\x5f\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2f\x64\x65\x73\x74\x5f\x6e\x75\x6d\x3a\x25\x73\x2f\x6d\x73\x67\x5f\x6c\x65\x6e\x3a\x25\x64\x2f\x69\x64\x3a\x25\x64\x2e" "\n"
,ptSendMsg->receiver_count,ptSendMsg->dest_num[(0xe33+1835-0x155e)],ptSendMsg->
msg_len,ptSendMsg->id);
#if (0x1e3c+1250-0x231d)
at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x52\x61\x77\x53\x6d\x73\x20\x64\x63\x73\x3a\x25\x64" "\n"
,ptSendMsg->dcs);
#endif
sc_cfg_set(NV_SMS_SEND_RESULT,"");g_zUfiSms_SendFailedCount=(0x6cb+4674-0x190d);
if(ptSendMsg->isDelete==TRUE||-(0x4f9+6905-0x1ff1)!=ptSendMsg->id){(void)
zUfiSms_DeleteDraftSms(ptSendMsg->id);}zUfiSms_SetGlobalDcsLang(ptSendMsg->dcs);
memset(&g_zUfiSms_GroupSms,(0x2191+31-0x21b0),sizeof(g_zUfiSms_GroupSms));if(
ZUFI_FAIL==zUfiSms_FillGroupSms(ptSendMsg,&g_zUfiSms_GroupSms)){return 
WMS_CMD_FAILED;}memset(&g_zUfiSms_ConcatSms,(0x1a08+2790-0x24ee),sizeof(
g_zUfiSms_ConcatSms));g_zUfiSms_UnitLen=zUfiSms_FillConcatSms(ptSendMsg,&
g_zUfiSms_ConcatSms);g_zUfiSms_IsConcatSendSuc=TRUE;g_zUfiSms_CurConcatSegNo=
(0x50f+5370-0x1a09);memset(g_zUfiSms_DbStoreData,(0xe4b+4989-0x21c8),sizeof(
g_zUfiSms_DbStoreData));if(ptSendMsg->mem_store==(0x712+3792-0x15d8)){
g_displaymode=(0x39b+8601-0x2533);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x53\x6d\x73\x3a\x20\x6d\x73\x67\x5f\x64\x69\x73\x70\x6c\x61\x79\x6d\x6f\x64\x65\x20\x3d\x20\x31" "\n"
);}else{g_displaymode=(0x60b+3409-0x135c);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x53\x6d\x73\x3a\x20\x6d\x73\x67\x5f\x64\x69\x73\x70\x6c\x61\x79\x6d\x6f\x64\x65\x20\x3d\x20\x30" "\n"
);}return zUfiSms_SendSms();}T_zUfiSms_CmdStatus zUfiSms_WriteRawSms(
T_zUfiSms_SaveReq*pSaveBuff){T_zUfiSms_ConcatInfo tConcatSms;T_zUfiSms_GroupInfo
 tGroupSms;int iSmsLen=(0x697+1203-0xb4a);T_zUfiSms_CmdStatus result=
WMS_CMD_SUCCESS;if(NULL==pSaveBuff){return WMS_CMD_FAILED;}if(
g_zUfiSms_MemFullFlag[ZTE_WMS_MEMORY_NV]){at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x52\x61\x77\x53\x6d\x73\x20\x6e\x76\x20\x6d\x65\x6d\x6f\x72\x79\x20\x69\x73\x20\x66\x75\x6c\x6c\x2c\x72\x65\x74\x75\x72\x6e" "\n"
);return WMS_CMD_FAILED;}if(pSaveBuff->isDelete==TRUE){(void)
zUfiSms_DeleteDraftSms(pSaveBuff->id);}zUfiSms_SetGlobalDcsLang(pSaveBuff->dcs);
memset(&tConcatSms,(0x2ad+1869-0x9fa),sizeof(T_zUfiSms_ConcatInfo));memset(&
tGroupSms,(0x147d+1865-0x1bc6),sizeof(T_zUfiSms_GroupInfo));(void)
zUfiSms_FillGroupSms(pSaveBuff,&tGroupSms);iSmsLen=zUfiSms_FillConcatSms(
pSaveBuff,&tConcatSms);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x52\x61\x77\x53\x6d\x73\x20\x74\x6f\x74\x61\x6c\x5f\x72\x65\x63\x65\x69\x76\x65\x72\x3d\x25\x64\x2c\x69\x53\x6d\x73\x4c\x65\x6e\x3d\x25\x64" "\n"
,tGroupSms.total_receiver,iSmsLen);for(tGroupSms.current_receiver=
(0x9ac+4124-0x19c8);tGroupSms.current_receiver<tGroupSms.total_receiver;
tGroupSms.current_receiver++){tConcatSms.current_sending=(0x8c7+427-0xa72);
result=zUfiSms_SaveSmsToDb(pSaveBuff,&tConcatSms,&tGroupSms,iSmsLen);at_print(
LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x52\x61\x77\x53\x6d\x73\x20\x63\x75\x72\x72\x65\x6e\x74\x5f\x72\x65\x63\x65\x69\x76\x65\x72\x3d\x25\x64\x2c\x72\x65\x73\x75\x6c\x74\x3d\x25\x64" "\n"
,tGroupSms.current_receiver,result);}sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);if(g_zUfiSms_MemFullFlag[
WMS_STORAGE_TYPE_NV_V01]){zUfiSms_SendSmsStatusInfo(MSG_SMS_DEFAULT);}at_print(
LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x52\x61\x77\x53\x6d\x73\x20\x65\x6e\x64\x20\x61\x6e\x64\x20\x72\x65\x73\x75\x6c\x74\x3d\x25\x64" "\n"
,result);return result;}T_zUfiSms_CmdStatus zUfiSms_DeleteSms(T_zUfiSms_DelReq*
ptDelBuff){T_zUfiSms_CmdStatus result=WMS_CMD_SUCCESS;BOOL 
memoryFullbeforeDelete=FALSE;BOOL unreadBeforeDelete=FALSE;if(NULL==ptDelBuff){
return WMS_CMD_FAILED;}at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x21\x21\x21\x21\x21\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x6d\x73\x21\x21\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2f\x69\x64\x5b\x25\x64\x5d\x2e" "\n"
,ptDelBuff->all_or_count,ptDelBuff->id[(0x156f+3993-0x2508)]);(void)
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);zUfiSms_ChangeMainState(
SMS_STATE_DELING);memset(&g_zUfiSms_DelMsg,(0x1e78+1944-0x2610),sizeof(
T_zUfiSms_DelSms));if(ZUFI_FAIL==zUfiSms_SetDeleteInfo(ptDelBuff)){at_print(
LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x21\x21\x21\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x74\x44\x65\x6c\x65\x74\x65\x49\x6e\x66\x6f\x20\x66\x61\x69\x6c\x2e" "\n"
);zUfiSms_ChangeMainState(SMS_STATE_DELED);return WMS_CMD_FAILED;}at_print(
LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x21\x21\x21\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x74\x44\x65\x6c\x65\x74\x65\x49\x6e\x66\x6f\x20\x52\x65\x61\x64\x20\x74\x6f\x20\x44\x65\x6c\x65\x74\x65\x3a\x6e\x76\x5f\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2f\x73\x69\x6d\x5f\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2e" "\n"
,g_zUfiSms_DelMsg.nv_count,g_zUfiSms_DelMsg.sim_count);if((0xd91+4580-0x1f75)<
g_zUfiSms_DelMsg.nv_count){if(g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]){
memoryFullbeforeDelete=TRUE;}unreadBeforeDelete=zUfiSms_IsUnreadSms(
ZTE_WMS_MEMORY_NV);result=(T_zUfiSms_CmdStatus)zUfiSms_DeleteNvSms();(void)
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);zUfiSms_ChangeMainState(
SMS_STATE_DELED);sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x4e\x76\x53\x6d\x73\x20\x6d\x65\x6d\x6f\x72\x79\x46\x75\x6c\x6c\x62\x65\x66\x6f\x72\x65\x44\x65\x6c\x65\x74\x65\x3d\x25\x64\x2c\x4d\x65\x6d\x46\x75\x6c\x6c\x46\x6c\x61\x67\x28\x4e\x56\x29\x3d\x25\x64" "\n"
,memoryFullbeforeDelete,g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]);if(
memoryFullbeforeDelete&&!g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]){
at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x4e\x76\x53\x6d\x73\x3a\x20\x73\x65\x6e\x64\x20\x41\x54\x2b\x5a\x4d\x45\x4e\x41\x3d\x30" "\n"
);zSms_SendZmenaReq((0x314+1413-0x899));}if(memoryFullbeforeDelete&&!
g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]||unreadBeforeDelete&&!
zUfiSms_IsUnreadSms(ZTE_WMS_MEMORY_NV)){zUfiSms_SendSmsStatusInfo(
MSG_SMS_DEFAULT);}}if((0x1545+1982-0x1d03)<g_zUfiSms_DelMsg.sim_count){result=
zUfiSms_DeleteSimSms();(void)zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_SIM);}
at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x6d\x73\x20\x72\x65\x73\x75\x6c\x74\x3d\x25\x64" "\n"
,result);return result;}T_zUfiSms_CmdStatus zUfiSms_ModifySmsTag(
T_zUfiSms_ModifyFlag*ptModifyBuff){unsigned long i=(0x1231+1539-0x1834);
T_zUfiSms_CmdStatus result=WMS_CMD_SUCCESS;char acStorePos[(0xc2b+6631-0x25e0)]=
{(0xe4+3267-0xda7)};if(NULL==ptModifyBuff){at_print(LOG_ERR,
"\x69\x6e\x70\x75\x74\x73\x20\x69\x73\x20\x6e\x75\x6c\x6c\x2e");return 
WMS_CMD_FAILED;}for(i=(0xeaa+3267-0x1b6d);i<ptModifyBuff->total_id;i++){if(
ptModifyBuff->id[i]<(0x34f+279-0x465)||ZUFI_FAIL==zUfiSms_UpdateSmsTagInDb(
ptModifyBuff->id[i],ptModifyBuff->tags)){result=WMS_CMD_FAILED;}else{result=
WMS_CMD_SUCCESS;}}if(ZUFI_FAIL==zUfiSms_GetStorePosById(
"\x4d\x65\x6d\x5f\x53\x74\x6f\x72\x65",acStorePos,sizeof(acStorePos),
ptModifyBuff->id[(0xea4+5972-0x25f8)])){return ZUFI_FAIL;}if((0x721+7499-0x246c)
==strcmp(acStorePos,ZTE_WMS_DB_NV_TABLE)){zUfiSms_SendSmsStatusInfo(
MSG_SMS_READING);}if((0x1f3b+1860-0x267f)==strcmp(acStorePos,
ZTE_WMS_DB_SIM_TABLE)&&ptModifyBuff->total_id>(0x88d+6384-0x217d)){
zUfiSms_ModifyModemSms(ptModifyBuff);}return result;}T_zUfiSms_CmdStatus 
zUfiSms_SetSmsPara(T_zUfiSms_ParaInfo*ptParaBuff){int atRes=(0xfb5+1988-0x1779);
CHAR sca[ZTE_WMS_ADDRESS_DIGIT_MAX_V01+(0x1092+1107-0x14e4)]={
(0x4b2+3278-0x1180)};CHAR store[(0x981+416-0xb0d)]={(0x190d+1005-0x1cfa)};CHAR 
defaultStore[(0x5f7+8370-0x2677)]={(0xdb+1498-0x6b5)};if(ptParaBuff==ZUFI_NULL){
return WMS_CMD_FAILED;}if(strlen(ptParaBuff->sca)!=(0x87f+5237-0x1cf4)){strncpy(
sca,ptParaBuff->sca,sizeof(sca)-(0x476+1259-0x960));at_print(LOG_DEBUG,
"\x73\x65\x6e\x64\x20\x5a\x41\x54\x5f\x43\x53\x43\x41\x5f\x53\x45\x54\x5f\x43\x4d\x44\x20\x6d\x65\x73\x73\x61\x67\x65\x20\x63\x73\x63\x61\x20\x69\x73\x20\x25\x73\x2e" "\n"
,sca);atRes=zSms_SetCscaReq(ptParaBuff->sca);if(atRes!=ZSMS_RESULT_OK){return 
WMS_CMD_FAILED;}}sc_cfg_get(NV_DEFAULT_STORE,defaultStore,sizeof(defaultStore));
if((*(ptParaBuff->default_store)!='\0')&&((0xf3c+5219-0x239f)!=strcmp(
defaultStore,ptParaBuff->default_store))){{strncpy(store,ptParaBuff->
default_store,sizeof(store)-(0x1667+285-0x1783));}atRes=zSms_SendCnmiReq(store);
if(atRes!=ZSMS_RESULT_OK){return WMS_CMD_FAILED;}}if(-(0x60b+6472-0x1f52)==
zUfiSms_SetDbParameters(ptParaBuff)){at_print(LOG_ERR,
"\x73\x65\x74\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x73\x20\x74\x6f\x20\x74\x61\x62\x6c\x65\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return WMS_CMD_FAILED;}return WMS_CMD_SUCCESS;}void zUfiSms_CmgrNvSet(void){
char sms_rec_flag[(0x3da+1813-0xaea)]={(0x23bc+515-0x25bf)};char remind_flag[
(0x6c0+4057-0x1694)];int sms_count=(0x1c6f+1207-0x2126);int remind_count=
(0x69d+3174-0x1303);memset(sms_rec_flag,(0x1c5f+1217-0x2120),sizeof(sms_rec_flag
));sc_cfg_get(ZTE_WMS_NVCONFIG_RECEVIED,sms_rec_flag,sizeof(sms_rec_flag));
sms_count=atoi(sms_rec_flag);if(sms_count<(0x513+1050-0x92d)||sms_count>INT_MAX-
(0xa4a+3800-0x1921)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x73\x6d\x73\x5f\x63\x6f\x75\x6e\x74\x20\x65\x72\x72\x3a\x25\x64" "\n"
,sms_count);return;}memset(sms_rec_flag,(0x13fc+564-0x1630),sizeof(sms_rec_flag)
);snprintf(sms_rec_flag,sizeof(sms_rec_flag),"\x25\x64",sms_count+
(0x1eb1+551-0x20d7));sc_cfg_set(ZTE_WMS_NVCONFIG_RECEVIED,sms_rec_flag);
sc_cfg_set(ZTE_WMS_NVCONFIG_RECEVIED_LED,sms_rec_flag);memset(remind_flag,
(0x156c+2739-0x201f),sizeof(remind_flag));snprintf(remind_flag,sizeof(
remind_flag),"\x25\x64",remind_count+(0x3b5+7218-0x1fe6));sc_cfg_set(
ZTE_WMS_NVCONFIG_RECEVIED_REMIND,remind_flag);sc_cfg_set(NV_SMS_RECV_RESULT,
"\x6f\x6b");sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");}void zUfiSms_CdsRespProc(
T_zSms_SmsInd*ptRespData){unsigned char acFormatPdu[ZSMS_PDU_SIZE]={
(0x8fb+6340-0x21bf)};T_zUfiSms_DbStoreData tDbStoreData={(0x12e4+1367-0x183b)};
int isPushSms=(0x16b7+3620-0x24db);if(strcmp(ptRespData->pdu,"")==
(0x1ada+520-0x1ce2)){CHAR srState[(0x24d+6329-0x1ad4)]={(0x15c8+1002-0x19b2)};
sc_cfg_get(NV_SR_STATE,srState,sizeof(srState));if((0x8d0+5422-0x1dfe)!=strcmp(
srState,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x69\x6e\x67")){sc_cfg_set(
NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(SMS_STATE_RECVED)
;}else{sc_cfg_set(NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");}
return;}zUfiSms_GetReportStatus(ptRespData->pdu,&ptRespData->stat);(void)
String2Bytes(ptRespData->pdu,acFormatPdu,(int)strlen(ptRespData->pdu));if(
(0x13a5+4873-0x26a9)==ptRespData->stat){(void)zUfiSms_HandleReport(acFormatPdu);
sc_cfg_set(NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");
zUfiSms_Ack_new_msg(TRUE);return;}return;}int zUfiSms_CheckIfWholeSms(
T_zUfiSms_DbStoreData*data,SMS_MSG_INFO*pmsg){if(data->concat_sms!=
(0x208+1545-0x810)){return(0xec+6326-0x19a2);}zUfiSms_GetCurrentRecvTotalSeq(
data,pmsg);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x68\x65\x63\x6b\x49\x66\x57\x68\x6f\x6c\x65\x53\x6d\x73\x20\x69\x64\x20\x3d\x20\x25\x73\x2c\x20\x74\x6f\x74\x61\x6c\x53\x65\x71\x20\x3d\x20\x25\x64\x2c\x72\x65\x66\x20\x3d\x25\x64\x2c\x74\x6f\x74\x61\x6c\x20\x3d\x25\x64\x2c\x20\x73\x65\x71\x3d\x25\x64" "\n"
,pmsg->id,atoi(pmsg->total_seq),data->concat_info[(0x1153+1990-0x1919)],data->
concat_info[(0x18a2+3103-0x24c0)],data->concat_info[(0x155+3139-0xd96)]);if(data
->concat_info[(0xf5d+1363-0x14af)]==atoi(pmsg->total_seq)){return
(0x1539+339-0x168c);}return-(0x21bb+193-0x227b);}void 
zUfiSms_TrafficChangeSmsTag(T_zUfiSms_DbStoreData*data){CHAR smsNumber[
(0x3c3+5349-0x1876)]={(0x1c6c+216-0x1d44)};sc_cfg_get(NV_TRAFFIC_SMS_NUMBER,
smsNumber,sizeof(smsNumber));if((0x4c9+4080-0x14b9)==strcmp(smsNumber,data->
number)){data->tag=WMS_TAG_TYPE_MT_READ_V01;data->msg_displaymode=
(0xbd1+6702-0x25fe);}}void zUfiSms_HandleTrafficSms(T_zUfiSms_DbStoreData*data){
int iSmsId=(0x1092+2193-0x1923);SMS_MSG_INFO msg={(0xddd+1691-0x1478)};CHAR 
smsNumber[(0x278+2199-0xadd)]={(0x35+1273-0x52e)};sc_cfg_get(
NV_TRAFFIC_SMS_NUMBER,smsNumber,sizeof(smsNumber));at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x74\x44\x62\x53\x74\x6f\x72\x65\x44\x61\x74\x61\x2e\x6e\x75\x6d\x62\x65\x72\x20\x3d\x20\x25\x73\x2c\x20\x74\x72\x61\x66\x66\x69\x63\x5f\x73\x6d\x73\x5f\x6e\x75\x6d\x62\x65\x72\x20\x3d\x20\x25\x73" "\n"
,data->number,smsNumber);if((0x548+3885-0x1475)==strcmp(smsNumber,data->number))
{if((0x596+4506-0x1730)!=zUfiSms_CheckIfWholeSms(data,&msg)){at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x63\x6d\x74\x20\x69\x6e\x64\x2c\x20\x72\x65\x63\x76\x20\x73\x6d\x73\x2c\x20\x62\x75\x74\x20\x6e\x6f\x74\x20\x77\x68\x6f\x6c\x65\x20\x73\x6d\x73\x2c\x20\x77\x61\x69\x74\x20\x74\x6f\x20\x72\x65\x63\x76\x20\x6e\x65\x78\x74\x20\x73\x65\x67" "\n"
);return;}sc_cfg_set(NV_TRAFFIC_RECV_SMS_ID,msg.id);sc_cfg_set(
NV_TRAFFIC_SMS_NUMBER,"\x30");at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x5b\x74\x72\x61\x66\x66\x69\x63\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x48\x61\x6e\x64\x6c\x65\x54\x72\x61\x66\x66\x69\x63\x53\x6d\x73\x20\x20\x20\x74\x72\x61\x66\x66\x69\x63\x5f\x72\x65\x63\x76\x5f\x73\x6d\x73\x5f\x69\x64\x20\x3d\x20\x25\x73" "\n"
,msg.id);}}void zUfiSms_CmtRespProc(T_zSms_SmsInd*ptRespData){zUfiSms_StoreType 
iStorePos=WMS_STORAGE_TYPE_NV_V01;unsigned char acFormatPdu[ZSMS_PDU_SIZE]={
(0x21a+4062-0x11f8)};T_zUfiSms_DbStoreData tDbStoreData;int isPushSms=
(0x1340+1029-0x1745);SMS_PARAM one_sms={(0x26f+974-0x63d)};int 
unread_sms_before_recv_new_sms=(0xa6d+2262-0x1343);memset(&tDbStoreData,
(0x1816+2547-0x2209),sizeof(T_zUfiSms_DbStoreData));at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x45\x6e\x74\x65\x72\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2f\x70\x64\x75\x3a\x25\x73\x21" "\n"
,ptRespData->index,ptRespData->stat,ptRespData->length,ptRespData->pdu);if(
strcmp(ptRespData->pdu,"")==(0x816+763-0xb11)){CHAR srState[(0x700+4424-0x1816)]
={(0x1640+2873-0x2179)};sc_cfg_get(NV_SR_STATE,srState,sizeof(srState));if(
(0x1112+4942-0x2460)!=strcmp(srState,
"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x69\x6e\x67")){sc_cfg_set(
NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(SMS_STATE_RECVED)
;}else{sc_cfg_set(NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");}
return;}isPushSms=DecodePushPdu(ptRespData->pdu,&one_sms);at_print(LOG_DEBUG,
"\x5b\x73\x6d\x73\x5d\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x20\x69\x73\x50\x75\x73\x68\x53\x6d\x73\x20\x3d\x20\x25\x64" "\n"
,isPushSms);if(SMS_NOTIFICATION==isPushSms){BakNotificationSms(one_sms.TP_UD,
strlen(one_sms.TP_UD));zte_fota_notifyPushMsg((0xe38+46-0xe66));}if(SMS_NO_PUSH
!=isPushSms){at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x64" "\n"
,one_sms.index);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x52\x65\x66\x65\x72\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_ReferNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x41\x6c\x6c\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_AllPieceNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x43\x75\x72\x72\x65\x6e\x74\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_CurrentPieceNum);zUfiSms_ChangeMainState(SMS_STATE_RECVED);
zUfiSms_Ack_new_msg(TRUE);return;}zUfiSms_GetReportStatus(ptRespData->pdu,&
ptRespData->stat);(void)String2Bytes(ptRespData->pdu,acFormatPdu,(int)strlen(
ptRespData->pdu));if((0x891+3102-0x14aa)==ptRespData->stat){(void)
zUfiSms_HandleReport(acFormatPdu);sc_cfg_set(NV_SR_STATE,
"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");zUfiSms_Ack_new_msg(TRUE);return
;}(void)zUfiSms_DecodeSmsData(&tDbStoreData,ptRespData->index,iStorePos,(
T_SmsStatus)ptRespData->stat,WMS_MESSAGE_FORMAT_GW_PP_V01,ptRespData->length,
acFormatPdu);if(tDbStoreData.sms_class==WMS_MESSAGE_CLASS_2){iStorePos=
WMS_STORAGE_TYPE_UIM_V01;}if(zUfiSms_IsUnreadSms(ZTE_WMS_MEMORY_NV)){
unread_sms_before_recv_new_sms=(0xe3f+3513-0x1bf7);}else{
unread_sms_before_recv_new_sms=(0xaca+3025-0x169b);}zUfiSms_TrafficChangeSmsTag(
&tDbStoreData);if(ZTE_WMS_NV_MEMORY_FULL==zUfiSms_WriteSmsToDb(&tDbStoreData,
iStorePos,-(0xfdd+5728-0x263c))){zUfiSms_Ack_new_msg(FALSE);zSms_SendZmenaReq(
(0xa8c+3433-0x17f4));return;}if(tDbStoreData.sms_class!=WMS_MESSAGE_CLASS_2){
zUfiSms_Ack_new_msg(TRUE);}zUfiSms_CmgrNvSet();zUfiSms_CheckMemoryFull(
ZTE_WMS_MEMORY_NV);zUfiSms_ChangeMainState(SMS_STATE_RECVED);
zUfiSms_SendSmsStatusInfo(MSG_SMS_NEW);zUfiSms_HandleTrafficSms(&tDbStoreData);
return;}void zUfiSms_ZmgrRespProc(T_zSms_SmsInd*ptRespData){zUfiSms_StoreType 
iStorePos=WMS_STORAGE_TYPE_NV_V01;unsigned char acFormatPdu[ZSMS_PDU_SIZE]={
(0x14f8+4119-0x250f)};T_zUfiSms_DbStoreData tDbStoreData;int isPushSms=
(0x426+3393-0x1167);SMS_PARAM one_sms={(0x11c5+3220-0x1e59)};CHAR defaultStore[
(0x459+7842-0x22c9)]={(0x68c+3999-0x162b)};memset(&tDbStoreData,
(0x23f+1312-0x75f),sizeof(T_zUfiSms_DbStoreData));at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x45\x6e\x74\x65\x72\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2f\x70\x64\x75\x3a\x25\x73\x21" "\n"
,ptRespData->index,ptRespData->stat,ptRespData->length,ptRespData->pdu);if(
strcmp(ptRespData->pdu,"")==(0x1c81+627-0x1ef4)){CHAR srState[
(0x1ba6+1778-0x2266)]={(0xfc4+5158-0x23ea)};sc_cfg_get(NV_SR_STATE,srState,
sizeof(srState));if((0xbaa+1306-0x10c4)!=strcmp(srState,
"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x69\x6e\x67")){sc_cfg_set(
NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(SMS_STATE_RECVED)
;}else{sc_cfg_set(NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");}
return;}isPushSms=DecodePushPdu(ptRespData->pdu,&one_sms);at_print(LOG_DEBUG,
"\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x20\x69\x73\x50\x75\x73\x68\x53\x6d\x73\x20\x3d\x20\x25\x64" "\n"
,isPushSms);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x20\x69\x73\x50\x75\x73\x68\x53\x6d\x73\x20\x3d\x25\x64\x20" "\n"
,isPushSms);if(SMS_NOTIFICATION==isPushSms){BakNotificationSms(one_sms.TP_UD,
strlen(one_sms.TP_UD));zte_fota_notifyPushMsg((0x58+4816-0x1328));}if(
SMS_NO_PUSH!=isPushSms){at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x64" "\n"
,one_sms.index);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x52\x65\x66\x65\x72\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_ReferNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x41\x6c\x6c\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_AllPieceNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x43\x75\x72\x72\x65\x6e\x74\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_CurrentPieceNum);zUfiSms_ChangeMainState(SMS_STATE_RECVED);return;}
zUfiSms_GetReportStatus(ptRespData->pdu,&ptRespData->stat);(void)String2Bytes(
ptRespData->pdu,acFormatPdu,(int)strlen(ptRespData->pdu));if((0x5aa+5806-0x1c53)
==ptRespData->stat){(void)zUfiSms_HandleReport(acFormatPdu);sc_cfg_set(
NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");return;}sc_cfg_get(
NV_DEFAULT_STORE,defaultStore,sizeof(defaultStore));if((0x16f1+816-0x1a21)==
strcmp(defaultStore,"\x73\x69\x6d")){iStorePos=WMS_STORAGE_TYPE_UIM_V01;}(void)
zUfiSms_DecodeSmsData(&tDbStoreData,ptRespData->index,iStorePos,(T_SmsStatus)
ptRespData->stat,WMS_MESSAGE_FORMAT_GW_PP_V01,ptRespData->length,acFormatPdu);if
(tDbStoreData.sms_class==WMS_MESSAGE_CLASS_2){iStorePos=WMS_STORAGE_TYPE_UIM_V01
;}zUfiSms_TrafficChangeSmsTag(&tDbStoreData);if(ZTE_WMS_NV_MEMORY_FULL==
zUfiSms_WriteSmsToDb(&tDbStoreData,iStorePos,-(0x4d7+2848-0xff6))){return;}if(
tDbStoreData.sms_class!=WMS_MESSAGE_CLASS_2){}zUfiSms_CmgrNvSet();
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);zUfiSms_ChangeMainState(
SMS_STATE_RECVED);zUfiSms_HandleTrafficSms(&tDbStoreData);return;}void 
zUfiSms_CmgrRespProc(T_zSms_SmsInd*ptRespData){zUfiSms_StoreType iStorePos=
WMS_STORAGE_TYPE_NV_V01;unsigned char acFormatPdu[ZSMS_PDU_SIZE]={
(0xf8f+4150-0x1fc5)};T_zUfiSms_DbStoreData tDbStoreData;int isPushSms=
(0x884+4484-0x1a08);SMS_PARAM one_sms={(0x1556+2766-0x2024)};int 
unread_sms_before_recv_new_sms=(0x6f1+1516-0xcdd);memset(&tDbStoreData,
(0x841+2936-0x13b9),sizeof(T_zUfiSms_DbStoreData));at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x45\x6e\x74\x65\x72\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2f\x70\x64\x75\x3a\x25\x73\x21" "\n"
,ptRespData->index,ptRespData->stat,ptRespData->length,ptRespData->pdu);
zUfiSms_SendSmsStatusInfo(MSG_SMS_READING);if(strcmp(ptRespData->pdu,"")==
(0x1290+3918-0x21de)){CHAR srState[(0x5c5+1672-0xc1b)]={(0xe63+5453-0x23b0)};
sc_cfg_get(NV_SR_STATE,srState,sizeof(srState));if((0x12a7+1323-0x17d2)!=strcmp(
srState,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x69\x6e\x67")){sc_cfg_set(
NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(SMS_STATE_RECVED)
;}else{sc_cfg_set(NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");}
return;}isPushSms=DecodePushPdu(ptRespData->pdu,&one_sms);at_print(LOG_DEBUG,
"\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x20\x69\x73\x50\x75\x73\x68\x53\x6d\x73\x20\x3d\x20\x25\x64" "\n"
,isPushSms);at_print(LOG_DEBUG,
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x72\x52\x65\x73\x70\x50\x72\x6f\x63\x20\x69\x73\x50\x75\x73\x68\x53\x6d\x73\x20\x3d\x25\x64\x20" "\n"
,isPushSms);if(SMS_NOTIFICATION==isPushSms){BakNotificationSms(one_sms.TP_UD,
strlen(one_sms.TP_UD));zte_fota_notifyPushMsg((0xe9+8589-0x2276));}if(
SMS_NO_PUSH!=isPushSms){at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x64" "\n"
,one_sms.index);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x52\x65\x66\x65\x72\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_ReferNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x41\x6c\x6c\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_AllPieceNum);at_print(LOG_DEBUG,
"\x20\x6f\x6e\x65\x5f\x73\x6d\x73\x2e\x54\x50\x5f\x43\x75\x72\x72\x65\x6e\x74\x50\x69\x65\x63\x65\x4e\x75\x6d\x20\x3d\x20\x25\x64" "\n"
,one_sms.TP_CurrentPieceNum);zUfiSms_ChangeMainState(SMS_STATE_RECVED);return;}
zUfiSms_GetReportStatus(ptRespData->pdu,&ptRespData->stat);(void)String2Bytes(
ptRespData->pdu,acFormatPdu,(int)strlen(ptRespData->pdu));if((0x1ec+3646-0x1025)
==ptRespData->stat){(void)zUfiSms_HandleReport(acFormatPdu);sc_cfg_set(
NV_SR_STATE,"\x73\x72\x5f\x72\x65\x63\x65\x69\x76\x65\x64");return;}(void)
zUfiSms_DecodeSmsData(&tDbStoreData,ptRespData->index,iStorePos,(T_SmsStatus)
ptRespData->stat,WMS_MESSAGE_FORMAT_GW_PP_V01,ptRespData->length,acFormatPdu);if
(tDbStoreData.sms_class==WMS_MESSAGE_CLASS_2){iStorePos=WMS_STORAGE_TYPE_UIM_V01
;}if(zUfiSms_IsUnreadSms(ZTE_WMS_MEMORY_NV)){unread_sms_before_recv_new_sms=
(0x1bc9+1988-0x238c);}else{unread_sms_before_recv_new_sms=(0xb15+5720-0x216d);}
if(ZTE_WMS_NV_MEMORY_FULL==zUfiSms_WriteSmsToDb(&tDbStoreData,iStorePos,-
(0x709+5088-0x1ae8))){return;}if(tDbStoreData.sms_class!=WMS_MESSAGE_CLASS_2){}
zUfiSms_CmgrNvSet();zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);
zUfiSms_ChangeMainState(SMS_STATE_RECVED);return;}void zUfiSms_CmgsRespProc(VOID
){T_zUfiSms_StatusInfo tStatusInfo={(0x742+5259-0x1bcd)};g_zUfiSms_DbStoreData->
msg_displaymode=g_displaymode;if(g_zUfiSms_DbStoreData->msg_displaymode!=
(0x74a+8109-0x26f6)){if((0xb7+4094-0x10b5)==zUfiSms_WriteSmsToDb(&
g_zUfiSms_DbStoreData[g_zUfiSms_CurConcatSegNo-(0x158b+3652-0x23ce)],
WMS_STORAGE_TYPE_NV_V01,-(0x4fa+2208-0xd99))){g_zUfiSms_MsgRefer++;(void)
zUfiSms_SetMaxReference(g_zUfiSms_MsgRefer);}}printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x67\x73\x52\x73\x70\x20\x73\x65\x67\x4e\x6f\x3a\x25\x64\x2f\x54\x6f\x74\x61\x6c\x4e\x75\x6d\x3a\x25\x64\x2f\x46\x61\x69\x6c\x4e\x75\x6d\x3a\x25\x64\x2e" "\n"
,g_zUfiSms_CurConcatSegNo,g_zUfiSms_ConcatTotalNum,g_zUfiSms_SendFailedCount);if
(g_zUfiSms_CurConcatSegNo==g_zUfiSms_ConcatTotalNum){g_zUfiSms_CurConcatSegNo=
(0x36c+3178-0xfd6);memset((void*)&tStatusInfo,(0x403+8961-0x2704),sizeof(
T_zUfiSms_StatusInfo));tStatusInfo.err_code=ZTE_SMS_CMS_NONE;tStatusInfo.
send_failed_count=g_zUfiSms_SendFailedCount;tStatusInfo.delete_failed_count=
(0x895+4297-0x195e);if(g_zUfiSms_SendFailedCount==(0x386+2369-0xcc7)){
tStatusInfo.cmd_status=WMS_CMD_SUCCESS;sc_cfg_set(NV_SMS_SEND_RESULT,"\x6f\x6b")
;}else{tStatusInfo.cmd_status=WMS_CMD_FAILED;sc_cfg_set(NV_SMS_SEND_RESULT,
"\x66\x61\x69\x6c");}tStatusInfo.cmd=WMS_SMS_CMD_MSG_SEND;(void)
zUfiSms_SetCmdStatus(&tStatusInfo);sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);if(g_zUfiSms_MemFullFlag[
WMS_STORAGE_TYPE_NV_V01]){zUfiSms_SendSmsStatusInfo(MSG_SMS_DEFAULT);}}else{}}
int zte_fota_notifyPushMsg(int cmd){FOTA_MSG_BUF msg={(0x23f8+34-0x241a)};int 
errs=(0x205b+739-0x233e);key_t req_id=ftok(
"\x2f\x6d\x65\x64\x69\x61\x2f\x7a\x74\x65\x2f\x7a\x74\x65\x5f\x73\x6f\x63\x6b\x65\x74\x2f\x66\x6f\x74\x61\x5f\x64\x6d\x61\x70\x70\x5f\x6d\x73\x67"
,(0x1b4f+265-0x1c57));int msgid=msgget(req_id,(0x107c+2351-0x19ab));if(msgid!=-
(0x19e9+2042-0x21e2)){msg.mtype=(0x86f+512-0xa6e);msg.mtext[(0x483+7842-0x2325)]
=WEBUI_NOTIFY_PUSH_MSG_;errs=msgsnd(msgid,&msg,sizeof(msg)-sizeof(long),
(0x373+504-0x56b));}return(errs<(0x103d+2146-0x189f)?(0xda7+4845-0x2094):
(0xc55+5721-0x22ad));}
#if (0x9ad+5421-0x1eda)  
int atSms_SendCmglReq(PSTR pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){return 
zSvr_SendAtSyn(ZAT_CMGL_CMD,"\x41\x54\x2b\x43\x4d\x47\x4c\x3d\x30" "\r\n",cid,
pAtRst,atRstSize);}VOID atSms_RecvCmglRsp(T_zAt_AtRes*pResLine){return;}
#endif 
#if (0x10f0+1249-0x15d1) 
int atSms_SendZmglReq(PSTR pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){int res
=(0x1b91+154-0x1c2b);pthread_mutex_lock(&smsdb_mutex);res=zSvr_SendAtSyn(
ZAT_ZMGL_CMD,"\x41\x54\x2b\x5a\x4d\x47\x4c\x3d\x34" "\r\n",cid,pAtRst,atRstSize)
;pthread_mutex_unlock(&smsdb_mutex);return res;}VOID atSms_initAtOk(VOID){
T_zUfiSms_StatusInfo tStatus={(0x493+2900-0xfe7)};sc_cfg_set(NV_SMS_LOAD_RESULT,
"\x6f\x6b");tStatus.cmd_status=WMS_CMD_SUCCESS;tStatus.cmd=WMS_SMS_CMD_INIT;(
void)zUfiSms_SetCmdStatus(&tStatus);zUfiSms_ChangeMainState(SMS_STATE_LOADED);}
int atSms_initAtErr(UINT8*pErrCode){T_zUfiSms_StatusInfo tStatus={
(0x82a+4447-0x1989)};sc_cfg_set(NV_SMS_LOAD_RESULT,"\x66\x61\x69\x6c");
zUfiSms_ChangeMainState(SMS_STATE_LOADED);tStatus.cmd_status=WMS_CMD_FAILED;
tStatus.cmd=WMS_SMS_CMD_INIT;(void)zUfiSms_SetCmdStatus(&tStatus);return FALSE;}
VOID atSms_RecvZmglRsp(T_zAt_AtRes*pResLine){static T_zUfiSms_SmsItem tSmsPara={
(0x585+7599-0x2334)};printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x5a\x6d\x67\x6c\x52\x73\x70\x20\x45\x6e\x74\x65\x72\x20\x70\x64\x75\x46\x6c\x61\x67\x3a\x25\x64\x2f\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2e" "\n"
,pResLine->pduFlag,pResLine->result);if(pResLine->pduFlag==ZAT_ATRES_PDU_FLAG){
zUfiSms_CmglRespProc(pResLine,&tSmsPara);}else{memset(&tSmsPara,
(0xd2+1508-0x6b6),sizeof(T_zUfiSms_SmsItem));(void)zUfiSms_FormatSms(pResLine->
resParas,sizeof(pResLine->resParas),&tSmsPara,(0x1a5c+1799-0x2162));printf(
"\x5b\x53\x4d\x53\x5d\x20\x5a\x6d\x67\x6c\x20\x52\x65\x73\x70\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2e" "\n"
,tSmsPara.index,tSmsPara.stat,tSmsPara.length);}}int atSms_SendCmgrReq(PSTR 
pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){CHAR pAtcmdStr[(0x29b+8087-0x221e)
]={(0x4dc+4973-0x1849)};iSmsIndex=atoi(pAtCmdPara);printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6d\x67\x72\x52\x65\x71\x20\x47\x65\x74\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2e" "\n"
,iSmsIndex);snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x43\x4d\x47\x52\x3d\x25\x73" "\r\n",pAtCmdPara);return 
zSvr_SendAtSyn(ZAT_CMGR_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}VOID 
atSms_RecvCmgrRsp(T_zAt_AtRes*pResLine){
#if (0x852+1469-0xe0f)
static T_zUfiSms_SmsItem tSmsPara={(0x165a+273-0x176b)};T_zUfiSms_CmgrSetRsp 
tCmgrRsp={(0x547+2880-0x1087)};printf(
"\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x67\x72\x52\x73\x70\x20\x45\x6e\x74\x65\x72\x20\x70\x64\x75\x46\x6c\x61\x67\x3a\x25\x64\x2f\x50\x61\x72\x61\x73\x3a\x25\x73\x2f\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2e" "\n"
,pResLine->pduFlag,pResLine->resParas,pResLine->result);if(pResLine->pduFlag==
ZAT_ATRES_PDU_FLAG){if(ZAT_RESULT_AUTOREPORT!=pResLine->result){printf(
"\x21\x21\x21\x21\x21\x21\x21\x21\x21\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x67\x72\x52\x73\x70\x20\x67\x65\x74\x20\x72\x65\x73\x75\x6c\x74\x20\x45\x72\x72\x6f\x72\x2e" "\n"
);return;}tCmgrRsp.index=tSmsPara.index;tCmgrRsp.length=tSmsPara.length;tCmgrRsp
.stat=tSmsPara.stat;sscanf(pResLine->resParas,"\x25\x35\x30\x30\x73",tCmgrRsp.
pdu);zUfiSms_CmgrRespProc(&tCmgrRsp);zUfiMmi_SendSmsStatus();sc_cfg_set(
"\x73\x6d\x73\x5f\x72\x65\x63\x76\x5f\x72\x65\x73\x75\x6c\x74","\x6f\x6b");}else
{memset(&tSmsPara,(0xe67+2611-0x189a),sizeof(T_zUfiSms_SmsItem));(void)
zUfiSms_FormatSms(pResLine->resParas,&tSmsPara,(0x1506+2771-0x1fd7));tSmsPara.
index=iSmsIndex;printf(
"\x3d\x3d\x3d\x3d\x3d\x3d\x43\x6d\x67\x72\x20\x52\x65\x73\x70\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2e" "\n"
,tSmsPara.index,tSmsPara.stat,tSmsPara.length);}
#endif
}int atSms_SendZmgrReq(PSTR pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){CHAR 
pAtcmdStr[(0x21c0+576-0x23ec)]={(0xdc3+481-0xfa4)};iSmsIndex=atoi(pAtCmdPara);
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x5a\x6d\x67\x72\x52\x65\x71\x20\x47\x65\x74\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2e" "\n"
,iSmsIndex);snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x5a\x4d\x47\x52\x3d\x25\x73" "\r\n",pAtCmdPara);return 
zSvr_SendAtSyn(ZAT_ZMGR_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}VOID 
atSms_RecvZmgrOk(T_zAt_AtRes*pResLine,UINT8*nextAt){return;}VOID 
atSms_RecvZmgrErr(T_zAt_AtRes*pResLine,UINT8*nextAt){at_print(LOG_DEBUG,
"\x53\x4d\x53\x20\x7a\x6d\x67\x72\x20\x69\x73\x20\x66\x61\x69\x6c" "\n");
at_print(LOG_DEBUG
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x5a\x6d\x67\x72\x45\x72\x72\x20\x20\x53\x4d\x53\x20\x7a\x6d\x67\x72\x20\x69\x73\x20\x66\x61\x69\x6c" "\n"
);{sc_cfg_set(NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(
SMS_STATE_RECVED);}}VOID atSms_RecvZmgrRsp(T_zAt_AtRes*pResLine){static 
T_zUfiSms_SmsItem tSmsPara={(0x7c3+3745-0x1664)};T_zUfiSms_CmgrSetRsp tCmgrRsp={
(0x453+3569-0x1244)};printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x5a\x6d\x67\x72\x52\x73\x70\x20\x45\x6e\x74\x65\x72\x20\x70\x64\x75\x46\x6c\x61\x67\x3a\x25\x64\x2f\x50\x61\x72\x61\x73\x3a\x25\x73\x2f\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2e" "\n"
,pResLine->pduFlag,pResLine->resParas,pResLine->result);if(pResLine->pduFlag==
ZAT_ATRES_PDU_FLAG){if(ZAT_RESULT_AUTOREPORT!=pResLine->result){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x5a\x6d\x67\x72\x52\x73\x70\x20\x67\x65\x74\x20\x72\x65\x73\x75\x6c\x74\x20\x45\x72\x72\x6f\x72\x2e" "\n"
);return;}tCmgrRsp.index=tSmsPara.index;tCmgrRsp.length=tSmsPara.length;tCmgrRsp
.stat=tSmsPara.stat;sscanf(pResLine->resParas,"\x25\x35\x30\x30\x73",tCmgrRsp.
pdu);zUfiSms_ZmgrRespProc(&tCmgrRsp);zUfiMmi_SendSmsStatus();sc_cfg_set(
NV_SMS_RECV_RESULT,"\x6f\x6b");}else{memset(&tSmsPara,(0xa5d+7043-0x25e0),sizeof
(T_zUfiSms_SmsItem));(void)zUfiSms_FormatSms(pResLine->resParas,sizeof(pResLine
->resParas),&tSmsPara,(0xe2c+4902-0x2150));tSmsPara.index=iSmsIndex;printf(
"\x5b\x53\x4d\x53\x5d\x20\x5a\x6d\x67\x72\x20\x52\x65\x73\x70\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2e" "\n"
,tSmsPara.index,tSmsPara.stat,tSmsPara.length);}}int atSms_SendCmgdReq(PSTR 
pAtCmdPara,int cid,PSTR pAtRst,int atRstSize){CHAR pAtcmdStr[
(0x13d3+1244-0x189b)]={(0xe5b+339-0xfae)};snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x43\x4d\x47\x44\x3d\x25\x73" "\r\n",pAtCmdPara);return 
zSvr_SendAtSyn(ZAT_CMGD_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}VOID 
atSms_RecvCmgdOk(VOID){CHAR strUsed[(0xc24+3302-0x1900)]={(0x685+7953-0x2596)};
int used=(0x218+6238-0x1a76);sc_cfg_set(NV_SMS_DEL_RESULT,"\x6f\x6b");printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x74\x20\x73\x69\x6d\x5f\x64\x65\x6c\x5f\x72\x65\x73\x75\x6c\x74\x20\x74\x6f\x20\x4f\x4b\x2e\x20" "\n"
);sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed,sizeof(strUsed));used=atoi(
strUsed)-(0x84c+7806-0x26c9);if(used<(0x16f+5422-0x169d)){used=
(0x7e9+2805-0x12de);}memset(&strUsed,(0x2cc+4748-0x1558),(0xbf7+375-0xd64));
snprintf(strUsed,sizeof(strUsed),"\x25\x64",used);sc_cfg_set(
ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed);}VOID atSms_RecvCmgdErr(VOID){sc_cfg_set
(NV_SMS_DEL_RESULT,"\x66\x61\x69\x6c");printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x74\x20\x73\x69\x6d\x5f\x64\x65\x6c\x5f\x72\x65\x73\x75\x6c\x74\x20\x74\x6f\x20\x66\x61\x69\x6c\x2e\x20" "\n"
);}VOID atSms_RecvCmgdFinish(VOID){char StrValue[(0x316+6542-0x1c9a)]={
(0x5b5+1559-0xbcc)};CHAR strTotal[(0xced+842-0x102d)]={(0x543+3091-0x1156)};CHAR
 strUsed[(0x931+5528-0x1ebf)]={(0x145d+1911-0x1bd4)};int total=
(0x1b93+912-0x1f23);int used=(0x8fc+6905-0x23f5);int remain=(0x10b8+2237-0x1975)
;sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed,sizeof(strUsed));used=atoi(
strUsed);sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_TOTAL,strTotal,sizeof(strTotal));
total=atoi(strTotal);remain=total-used;if(remain<(0xf39+2687-0x19b8)){remain=
(0x1056+3094-0x1c6c);}memset(&StrValue,(0x2aa+6801-0x1d3b),(0x568+2599-0xf85));
snprintf(StrValue,sizeof(StrValue),"\x25\x64",remain);sc_cfg_set(
ZTE_WMS_NVCONFIG_SIM_CARD_REMAIN,StrValue);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x69\x6d\x53\x6d\x73\x20\x75\x73\x65\x64\x3d\x25\x64\x2c\x72\x65\x6d\x61\x69\x6e\x3d\x25\x64\x2c\x74\x6f\x74\x61\x6c\x3d\x25\x64" "\n"
,used,remain,total);zUfiSms_ChangeMainState(SMS_STATE_DELED);sc_cfg_set(
NV_SMS_DB_CHANGE,"\x31");}int atSms_SendCmgsReq(PSTR pAtCmdPara,int cid,PSTR 
pAtRst,int atRstSize){int atRes=(0x1b74+2076-0x2390);CHAR pAtcmdStr[
ZSMS_PDU_SIZE]={(0x621+5923-0x1d44)};snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x43\x4d\x47\x53\x3d\x25\x64" "\r\n",g_zUfiSms_FinalCmgsBuf.length)
;atRes=zSvr_SendAtSyn(ZAT_CMGS_CMD,pAtcmdStr,cid,pAtRst,atRstSize);if(atRes!=
ZAT_RESULT_SMS){return atRes;}memset(pAtcmdStr,(0x26a+5099-0x1655),ZSMS_PDU_SIZE
);if(strlen(g_zUfiSms_FinalCmgsBuf.pdu)<ZSMS_PDU_SIZE-(0xc97+541-0xeb3)){memcpy(
pAtcmdStr,g_zUfiSms_FinalCmgsBuf.pdu,strlen(g_zUfiSms_FinalCmgsBuf.pdu));}else{
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6d\x67\x73\x52\x65\x71\x20\x70\x64\x75\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x25\x73" "\n"
,g_zUfiSms_FinalCmgsBuf.pdu);memcpy(pAtcmdStr,g_zUfiSms_FinalCmgsBuf.pdu,
ZSMS_PDU_SIZE-(0x733+5849-0x1e0a));}*(pAtcmdStr+strlen(g_zUfiSms_FinalCmgsBuf.
pdu))=ZSMS_CTRL_Z_CHAR;memset(pAtRst,(0x14fc+3993-0x2495),atRstSize);return 
zSvr_SendAtSyn(ZAT_CMGS_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}VOID 
atSms_RecvCmgsOk(UINT8*pResLine,int cid){at_print(LOG_DEBUG,
"\x73\x6d\x73\x20\x73\x65\x6e\x64\x65\x64\x20\x73\x75\x63\x63\x65\x73\x73\x2e\x20" "\n"
);g_zUfiSms_CurConcatSegNo++;if(g_zUfiSms_CurConcatSegNo>
ZTE_WMS_CONCAT_SMS_COUNT_MAX){return;}g_zUfiSms_DbStoreData[
g_zUfiSms_CurConcatSegNo-(0x126a+3901-0x21a6)].tag=WMS_TAG_TYPE_MO_SENT_V01;
zUfiSms_CmgsRespProc(cid);}VOID atSms_RecvCmgsErr(UINT8*pResLine,int cid){
at_print(LOG_DEBUG,
"\x73\x6d\x73\x20\x73\x65\x6e\x64\x65\x64\x20\x66\x61\x69\x6c\x2e\x20" "\n");
g_zUfiSms_CurConcatSegNo++;if(g_zUfiSms_CurConcatSegNo>
ZTE_WMS_CONCAT_SMS_COUNT_MAX){return;}g_zUfiSms_SendFailedCount++;at_print(
LOG_DEBUG,
"\x73\x65\x6e\x64\x20\x73\x6d\x73\x20\x66\x61\x69\x6c\x65\x64\x2c\x73\x6f\x20\x77\x72\x69\x74\x65\x20\x73\x6d\x73\x20\x74\x6f\x20\x64\x72\x61\x66\x74\x62\x6f\x78\x2e" "\n"
);g_zUfiSms_DbStoreData[g_zUfiSms_CurConcatSegNo-(0x1127+5123-0x2529)].tag=
WMS_TAG_TYPE_MO_NOT_SENT_V01;if(g_zUfiSms_ConcatTotalNum>(0x68+1979-0x822)){
g_zUfiSms_IsConcatSendSuc=FALSE;}zUfiSms_CmgsRespProc(cid);}VOID 
atSms_RecvCmgsRsp(T_zAt_AtRes*pResLine){return;}VOID atSms_RecvCmtRsp(
T_zAt_AtRes*pResLine){CHAR needSMS[(0x18b6+2417-0x21f5)]={(0x55d+5001-0x18e6)};
sc_cfg_get(NV_NEED_SUPPORT_SMS,needSMS,sizeof(needSMS));if((0x2c2+8167-0x22a9)==
strcmp(needSMS,"\x6e\x6f")){printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}static T_zUfiSms_SmsItem tSmsPara={(0xf70+5617-0x2561)};
T_zUfiSms_CmtSetRsp tCmtRsp={(0x612+4973-0x197f)};if(NULL==pResLine){return;}
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x45\x6e\x74\x65\x72\x20\x70\x64\x75\x46\x6c\x61\x67\x3a\x25\x64\x2f\x50\x61\x72\x61\x73\x3a\x25\x73\x2f\x72\x65\x73\x75\x6c\x74\x3a\x25\x64\x2e" "\n"
,pResLine->pduFlag,pResLine->resParas,pResLine->result);if(pResLine->pduFlag==
ZAT_ATRES_PDU_FLAG){if(ZAT_RESULT_AUTOREPORT!=pResLine->result){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x67\x65\x74\x20\x72\x65\x73\x75\x6c\x74\x20\x45\x72\x72\x6f\x72\x2e" "\n"
);return;}tCmtRsp.length=tSmsPara.length;sscanf(pResLine->resParas,
"\x25\x35\x30\x30\x73",tCmtRsp.pdu);pthread_mutex_lock(&smsdb_mutex);
zUfiSms_CmtRespProc(&tCmtRsp);zUfiMmi_SendSmsStatus();pthread_mutex_unlock(&
smsdb_mutex);sc_cfg_set(NV_SMS_RECV_RESULT,"\x6f\x6b");}else{memset(&tSmsPara,
(0x1ca3+466-0x1e75),sizeof(T_zUfiSms_SmsItem));atBase_PreProcRes(pResLine->
resParas,sizeof(pResLine->resParas));printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x6d\x74\x20\x69\x6e\x64\x21\x20\x70\x52\x65\x73\x4c\x69\x6e\x65\x2d\x3e\x72\x65\x73\x50\x61\x72\x61\x73\x3a\x25\x73\x2e" "\n"
,pResLine->resParas);sscanf(pResLine->resParas,"\x25\x73\x20\x25\x64",tSmsPara.
alpha,&tSmsPara.length);printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x6d\x74\x20\x69\x6e\x64\x21\x20\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2e" "\n"
,tSmsPara.length);}}VOID atSms_RecvCmtiRsp(T_zAt_AtRes*pResLine){CHAR needSMS[
(0xc2d+615-0xe62)]={(0x69+4992-0x13e9)};sc_cfg_get(NV_NEED_SUPPORT_SMS,needSMS,
sizeof(needSMS));if((0x18a6+2347-0x21d1)==strcmp(needSMS,"\x6e\x6f")){printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}char sms_Main_state[(0x1492+2830-0x1f82)]={(0x740+7519-0x249f)};char*
memory=NULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x69\x52\x73\x70\x20\x65\x6e\x74\x65\x72\x20\x25\x73\x2e" "\n"
,pResLine->resParas);if(NULL==pResLine){return;}if(ZAT_CMTI_CMD!=pResLine->
atCmdId){return;}sc_cfg_get(NV_SMS_STATE,sms_Main_state,sizeof(sms_Main_state));
if(strcmp(sms_Main_state,"\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67")==
(0x850+2780-0x132c)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x69\x52\x73\x70\x3a\x20\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67" "\n"
);return;}memory=strstr(pResLine->resParas,"\"");if(NULL!=memory){memory++;}if(
(0xc25+2751-0x16e4)==strncmp("\x53\x4d",memory,(0x997+651-0xc20))){
zUfiSms_SetSmsLocation(SMS_LOCATION_SIM);zUfiSms_ChangeMainState(
SMS_STATE_RECVING);memory+=(0x5d1+7951-0x24dc);printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x6e\x64\x20\x63\x6d\x67\x72\x3a\x20\x25\x73" "\n"
,memory);zSvr_InnerSendMsg(ZUFI_MODULE_ID_AT_LOCAL,ZUFI_MODULE_ID_AT_UNSOLI,
MSG_CMD_AT_ZMGR,strlen(memory),memory);}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x69\x52\x73\x70\x20\x3a\x73\x74\x6f\x72\x65\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x6e\x6f\x74\x20\x53\x4d\x2e" "\n"
);}sc_cfg_set(NV_SMS_RECV_RESULT,"");}VOID atSms_RecvCdsRsp(T_zAt_AtRes*pResLine
){CHAR needSMS[(0x131d+2274-0x1bcd)]={(0x18a3+940-0x1c4f)};sc_cfg_get(
NV_NEED_SUPPORT_SMS,needSMS,sizeof(needSMS));if((0x12f4+2726-0x1d9a)==strcmp(
needSMS,"\x6e\x6f")){printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}static T_zUfiSms_SmsItem tSmsPara={(0x1070+5091-0x2453)};
T_zUfiSms_CmgrSetRsp tCmgrRsp={(0xbe9+5853-0x22c6)};if(NULL==pResLine){return;}
if(pResLine->pduFlag==ZAT_ATRES_PDU_FLAG){if(ZAT_RESULT_AUTOREPORT!=pResLine->
result){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x52\x73\x70\x20\x67\x65\x74\x20\x72\x65\x73\x75\x6c\x74\x20\x45\x72\x72\x6f\x72\x2e" "\n"
);return;}tCmgrRsp.length=tSmsPara.length;sscanf(pResLine->resParas,
"\x25\x35\x30\x30\x73",tCmgrRsp.pdu);pthread_mutex_lock(&smsdb_mutex);
zUfiSms_CdsRespProc(&tCmgrRsp);zUfiMmi_SendSmsStatus();pthread_mutex_unlock(&
smsdb_mutex);sc_cfg_set(NV_SMS_RECV_RESULT,"\x6f\x6b");}else{memset(&tSmsPara,
(0x25d+5284-0x1701),sizeof(T_zUfiSms_SmsItem));atBase_PreProcRes(pResLine->
resParas,sizeof(pResLine->resParas));printf(
"\x5b\x53\x4d\x53\x5d\x20\x63\x64\x73\x20\x69\x6e\x64\x21\x20\x70\x52\x65\x73\x4c\x69\x6e\x65\x2d\x3e\x72\x65\x73\x50\x61\x72\x61\x73\x3a\x25\x73\x2e" "\n"
,pResLine->resParas);sscanf(pResLine->resParas,"\x25\x73\x20\x25\x64",tSmsPara.
alpha,&tSmsPara.length);printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x52\x73\x70\x20\x63\x64\x73\x20\x69\x6e\x64\x21\x20\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x2e" "\n"
,tSmsPara.length);}}VOID atSms_RecvCdsiRsp(T_zAt_AtRes*pResLine){CHAR needSMS[
(0x442+6477-0x1d5d)]={(0x4f6+5002-0x1880)};sc_cfg_get(NV_NEED_SUPPORT_SMS,
needSMS,sizeof(needSMS));if((0x209+5686-0x183f)==strcmp(needSMS,"\x6e\x6f")){
printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}char sms_Main_state[(0xaaf+786-0xda3)]={(0xe4a+274-0xf5c)};char*memory
=NULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x20\x65\x6e\x74\x65\x72\x20\x25\x73\x2e" "\n"
,pResLine->resParas);if(NULL==pResLine){return;}if(ZAT_CDSI_CMD!=pResLine->
atCmdId){return;}sc_cfg_get(NV_SMS_STATE,sms_Main_state,sizeof(sms_Main_state));
if(strcmp(sms_Main_state,"\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67")==
(0x858+7237-0x249d)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x3a\x20\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67" "\n"
);return;}memory=strstr(pResLine->resParas,"\"");if(NULL!=memory){memory++;}
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x3a\x20\x6d\x65\x6d\x6f\x72\x79\x20\x3d\x20\x25\x73" "\n"
,memory);if((0x2245+622-0x24b3)==strncmp("\x53\x4d",memory,(0x9ec+5020-0x1d86)))
{zUfiSms_SetSmsLocation(SMS_LOCATION_SIM);zUfiSms_ChangeMainState(
SMS_STATE_RECVING);memory+=(0xa1b+4120-0x1a2f);printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x6e\x64\x20\x63\x6d\x67\x72\x3a\x20\x25\x73" "\n"
,memory);zSvr_InnerSendMsg(ZUFI_MODULE_ID_AT_LOCAL,ZUFI_MODULE_ID_AT_UNSOLI,
MSG_CMD_AT_ZMGR,strlen(memory),memory);}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x20\x3a\x73\x74\x6f\x72\x65\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x6e\x6f\x74\x20\x53\x4d\x2e" "\n"
);}sc_cfg_set(NV_SMS_RECV_RESULT,"");}int atSms_SendZmenaReq(PSTR pAtCmdPara,int
 cid,PSTR pAtRst,int atRstSize){CHAR pAtcmdStr[(0x465+3820-0x133d)]={
(0x19a2+2902-0x24f8)};snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x5a\x4d\x45\x4e\x41\x3d\x25\x73" "\r\n",pAtCmdPara);return 
zSvr_SendAtSyn(ZAT_ZMENA_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}VOID 
atSms_RecvZmenaOk(VOID){g_Zmena_rsp=TRUE;return;}VOID atSms_RecvZmenaErr(VOID){
g_Zmena_rsp=FALSE;return;}int atSms_SendCnmaReq(PSTR pAtCmdPara,int cid,PSTR 
pAtRst,int atRstSize){int atRes=(0x11cc+3874-0x20ee);CHAR pAtcmdStr[
ZSMS_PDU_SIZE]={(0x1df0+1236-0x22c4)};CHAR ackPduStr[(0x3e9+4537-0x1570)]={
(0x13d1+2971-0x1f6c)};if(atoi(pAtCmdPara)==(0x1bfa+1754-0x22d3)){snprintf(
pAtcmdStr,sizeof(pAtcmdStr),"\x41\x54\x2b\x43\x4e\x4d\x41\x3d\x25\x73" "\r\n",
pAtCmdPara);atRes=zSvr_SendAtSyn(ZAT_CNMA_CMD,pAtcmdStr,cid,pAtRst,atRstSize);
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6e\x6d\x61\x52\x65\x71\x20\x31\x31\x31\x31\x31\x31\x20\x61\x63\x6b\x20\x6f\x6b\x20\x3d\x20\x25\x73\x2e" "\n"
,pAtcmdStr);return(0x865+4554-0x1a2f);}else{zUfiSms_EncodePdu_DeliverReport(
ackPduStr,(0x935+7841-0x2703));snprintf(pAtcmdStr,sizeof(pAtcmdStr),
"\x41\x54\x2b\x43\x4e\x4d\x41\x3d\x25\x73\x2c\x25\x64" "\r\n",pAtCmdPara,strlen(
ackPduStr)/(0x165c+3718-0x24e0));atRes=zSvr_SendAtSyn(ZAT_CNMA_CMD,pAtcmdStr,cid
,pAtRst,atRstSize);if(atRes!=ZAT_RESULT_SMS){return atRes;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6e\x6d\x61\x52\x65\x71\x20\x3d\x20\x25\x73\x2e" "\n"
,pAtcmdStr);memset(pAtcmdStr,(0xc10+3553-0x19f1),ZSMS_PDU_SIZE);if(strlen(
ackPduStr)<ZSMS_PDU_SIZE-(0x722+791-0xa38)){memcpy(pAtcmdStr,ackPduStr,strlen(
ackPduStr));}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6d\x67\x73\x52\x65\x71\x20\x70\x64\x75\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x25\x73" "\n"
,ackPduStr);memcpy(pAtcmdStr,ackPduStr,ZSMS_PDU_SIZE-(0x7db+5901-0x1ee6));}*(
pAtcmdStr+strlen(ackPduStr))=ZSMS_CTRL_Z_CHAR;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6e\x6d\x61\x52\x65\x71\x2e\x20\x70\x64\x75\x3d\x20\x25\x73" "\n"
,pAtcmdStr);memset(pAtRst,(0x11b3+3142-0x1df9),atRstSize);return zSvr_SendAtSyn(
ZAT_CNMA_CMD,pAtcmdStr,cid,pAtRst,atRstSize);}}VOID atSms_RecvCnmaRsp(
T_zAt_AtRes*pResLine){return;}VOID atUnsoli_Delete_Sim_Sms(UINT8*pDatabuf,int 
cid){CHAR errCode[ZSVR_AT_RES_CODE_LEN]={(0x3c5+8944-0x26b5)};int atRes=
(0x78f+3120-0x13bf);if(pDatabuf==NULL){return;}atRes=atSms_SendCmgdReq(pDatabuf,
cid,errCode,ZSVR_AT_RES_CODE_LEN);if(atRes==ZSMS_RESULT_OK){atSms_RecvCmgdOk();}
else{atSms_RecvCmgdErr();}atSms_RecvCmgdFinish();}VOID atUnsoli_Report_Cnma(
UINT8*pDatabuf,int cid){CHAR errCode[ZSVR_AT_RES_CODE_LEN]={(0x1d6b+283-0x1e86)}
;if(pDatabuf==NULL){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x55\x6e\x73\x6f\x6c\x69\x5f\x52\x65\x70\x6f\x72\x74\x5f\x43\x6e\x6d\x61\x20\x6e\x75\x6c\x6c"
);return;}atSms_SendCnmaReq(pDatabuf,cid,errCode,ZSVR_AT_RES_CODE_LEN);}VOID 
atUnsoli_Report_Zmena(UINT8*pDatabuf,int cid){CHAR errCode[ZSVR_AT_RES_CODE_LEN]
={(0x17c2+965-0x1b87)};int atRes=(0xae9+5586-0x20bb);if(pDatabuf==NULL){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x55\x6e\x73\x6f\x6c\x69\x5f\x52\x65\x70\x6f\x72\x74\x5f\x5a\x6d\x65\x6e\x61\x20\x6e\x75\x6c\x6c"
);return;}atRes=atSms_SendZmenaReq(pDatabuf,cid,errCode,ZSVR_AT_RES_CODE_LEN);if
(atRes==ZSMS_RESULT_OK){atSms_RecvZmenaOk();}else{atSms_RecvZmenaErr();}}VOID 
atUnsoli_Report_Zmgr(UINT8*pDatabuf,int cid){CHAR errCode[ZSVR_AT_RES_CODE_LEN]=
{(0x1146+1887-0x18a5)};if(pDatabuf==NULL){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x55\x6e\x73\x6f\x6c\x69\x5f\x52\x65\x70\x6f\x72\x74\x5f\x5a\x6d\x67\x72\x20\x6e\x75\x6c\x6c"
);return;}atSms_SendZmgrReq(pDatabuf,cid,errCode,ZSVR_AT_RES_CODE_LEN);}
#endif

