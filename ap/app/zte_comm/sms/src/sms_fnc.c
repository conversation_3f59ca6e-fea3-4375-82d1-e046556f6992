
#include <fcntl.h>
#include <stdio.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/time.h>
#include <limits.h>
#include "sms_fnc.h"
#include "sms_db.h"
#include "sms_code.h"
#define SMS_RETRY_COUNT (0x226+2587-0xc3e)
#define SHORT_INT_LEN (0x21a+1379-0x777)
SMS_LOCATION g_zUfiSms_CurLocation=SMS_LOCATION_SIM;int 
g_zUfiSms_ConcatSmsReference=(0xfc1+5359-0x24b0);T_zUfiSms_ParaInfo 
g_zUfiSms_CurSmsPara={"",WMS_STORAGE_TYPE_UIM_V01,
ZTE_WMS_SMS_DEFAULT_TP_VALIDITY_PERIOD_GW,(0xa5+4566-0x127b),(0x4e7+3966-0x1465)
,(0x1806+518-0x1a0c),"\x6e\x76"};int g_zUfiSms_Language=NOT_DEFINE_LANGUAGE;int 
g_zUfiSms_Dcs=(0xa4f+6585-0x2408);unsigned long g_zUfiSms_StoreCapablity[
ZTE_WMS_MEMORY_MAX]={(0x182a+730-0x1aa0),ZTE_WMS_DB_MSG_COUNT_MAX};
T_zSms_SendSmsReq g_zUfiSms_FinalCmgsBuf;SMS_PARAM g_zUfiSms_SendingSms;UINT16 
g_zUfiSms_IsLanguageShift=(0xd63+1073-0x1194);extern int g_zUfiSms_MsgRefer;
extern T_zUfiSms_DelSms g_zUfiSms_DelMsg;extern T_zUfiSms_DelIndexInfo 
g_deleteIndex;extern T_zUfiSms_ModifyIndexInfo g_modifyIndex;extern 
T_zUfiSms_ModifySms g_zUfiSms_modifyMsg;extern UINT8 g_zUfiSms_MemFullFlag[
ZTE_WMS_MEMORY_MAX];extern int g_zUfiSms_ConcatTotalNum;extern 
T_zUfiSms_ConcatInfo g_zUfiSms_ConcatSms;extern T_zUfiSms_GroupInfo 
g_zUfiSms_GroupSms;extern int g_zUfiSms_UnitLen;extern UINT8 
g_zUfiSms_IsConcatSendSuc;extern int g_zUfiSms_SendFailedCount;extern 
T_zUfiSms_DbStoreData g_zUfiSms_DbStoreData[ZTE_WMS_CONCAT_SMS_COUNT_MAX];extern
 int g_zUfiSms_SendFailedRetry;void zUfiSms_SetSmsLocation(SMS_LOCATION 
eLocation){switch(eLocation){case SMS_LOCATION_SIM:{g_zUfiSms_CurLocation=
SMS_LOCATION_SIM;break;}case SMS_LOCATION_ME:{g_zUfiSms_CurLocation=
SMS_LOCATION_ME;break;}default:{return;}}}int zUfiSms_SetDeleteInfo(
T_zUfiSms_DelReq*ptDelMsg){char acStorePos[(0x12c7+2461-0x1c32)];int i=
(0x4cc+3615-0x12eb);g_zUfiSms_DelMsg.nv_count=(0x155+1560-0x76d);
g_zUfiSms_DelMsg.nv_index=(0x17ba+3535-0x2589);g_zUfiSms_DelMsg.sim_count=
(0x1de4+1940-0x2578);g_zUfiSms_DelMsg.sim_index=(0x14b+3214-0xdd9);for(i=
(0x29+3435-0xd94);i<ptDelMsg->all_or_count;i++){memset(acStorePos,
(0x183+2195-0xa16),sizeof(acStorePos));if(ZUFI_FAIL==zUfiSms_GetStorePosById(
"Mem_Store",acStorePos,sizeof(acStorePos),ptDelMsg->id[i])){return ZUFI_FAIL;}if
((0xacd+5316-0x1f91)==strcmp(acStorePos,ZTE_WMS_DB_NV_TABLE)){g_zUfiSms_DelMsg.
nv_id[g_zUfiSms_DelMsg.nv_count]=ptDelMsg->id[i];g_zUfiSms_DelMsg.nv_count++;
g_zUfiSms_DelMsg.nv_index_count++;}else if((0x1108+5429-0x263d)==strcmp(
acStorePos,ZTE_WMS_DB_SIM_TABLE)){g_zUfiSms_DelMsg.sim_id[g_zUfiSms_DelMsg.
sim_count]=ptDelMsg->id[i];g_zUfiSms_DelMsg.sim_count++;g_zUfiSms_DelMsg.
sim_index_count++;}}return ZUFI_SUCC;}void zUfiSms_ChangeMainState(
T_zUfiSms_MainState iNewState){char*ptStrSmsState[]={
"\x73\x6d\x73\x5f\x69\x6e\x69\x74\x69\x6e\x67",
"\x73\x6d\x73\x5f\x69\x6e\x69\x74\x65\x64",
"\x73\x6d\x73\x5f\x6c\x6f\x61\x64\x69\x6e\x67",
"\x73\x6d\x73\x5f\x6c\x6f\x61\x64\x65\x64",
"\x73\x6d\x73\x5f\x73\x65\x6e\x64\x69\x6e\x67",
"\x73\x6d\x73\x5f\x73\x65\x6e\x64\x65\x64",
"\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67",
"\x73\x6d\x73\x5f\x64\x65\x6c\x65\x64",
"\x73\x6d\x73\x5f\x72\x65\x63\x76\x69\x6e\x67",
"\x73\x6d\x73\x5f\x72\x65\x63\x76\x65\x64",
"\x73\x6d\x73\x5f\x73\x61\x76\x69\x6e\x67",
"\x73\x6d\x73\x5f\x73\x61\x76\x65\x64",
"\x73\x6d\x73\x5f\x64\x65\x6c\x73\x61\x76\x69\x6e\x67",
"\x73\x6d\x73\x5f\x64\x65\x6c\x73\x61\x76\x65\x64",
"\x73\x6d\x73\x5f\x73\x69\x6d\x5f\x6c\x6f\x61\x64\x65\x64",
"\x73\x6d\x73\x5f\x73\x65\x74\x74\x69\x6e\x67",
"\x73\x6d\x73\x5f\x68\x61\x76\x65\x73\x65\x74",
"\x73\x6d\x73\x5f\x63\x6f\x6e\x63\x61\x74\x73\x65\x6e\x64\x69\x6e\x67",};switch(
iNewState){case SMS_STATE_INITING:case SMS_STATE_INITED:case SMS_STATE_LOADING:
case SMS_STATE_LOADED:case SMS_STATE_SIM_LOADED:case SMS_STATE_SENDING:case 
SMS_STATE_SENDED:case SMS_STATE_DELING:case SMS_STATE_DELED:case 
SMS_STATE_RECVING:case SMS_STATE_RECVED:case SMS_STATE_DELSAVING:case 
SMS_STATE_DELSAVED:{break;}default:{return;}}sc_cfg_set(NV_SMS_STATE,
ptStrSmsState[iNewState]);}int zUfiSms_CheckStoreDir(void){if(-
(0x34f+1838-0xa7c)==access(ZTE_WMS_DB_DIR,F_OK)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x25\x73\x20\x64\x6f\x65\x73\x20\x6e\x6f\x74\x20\x65\x78\x69\x73\x74\x2c\x73\x6f\x63\x72\x65\x61\x74\x65\x20\x69\x74\x2e" "\n"
,ZTE_WMS_DB_DIR);if(-(0x985+4338-0x1a76)==mkdir(ZTE_WMS_DB_DIR,
(0xadc+4720-0x1b4d))){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x66\x61\x69\x6c\x65\x64\x20\x74\x6f\x20\x63\x72\x65\x61\x74\x65\x20\x64\x62\x20\x64\x69\x72\x2e" "\n"
);return ZUFI_FAIL;}}return ZUFI_SUCC;}int zUfiSms_CheckSmsDb(void){if(-
(0xe8b+4225-0x1f0b)==access(ZTE_WMS_DB_PATH,F_OK)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x25\x73\x20\x64\x6f\x65\x73\x20\x6e\x6f\x74\x20\x65\x78\x69\x73\x74\x2c\x73\x6f\x20\x67\x65\x74\x20\x64\x65\x66\x61\x75\x6c\x74\x20\x63\x6f\x6e\x66\x69\x67\x2e" "\n"
,ZTE_WMS_DB_PATH);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x3d\x3d\x3d\x3d\x3d\x3d\x25\x73\x20\x65\x78\x69\x73\x74\x2e" "\n"
,ZTE_WMS_DB_PATH);return ZUFI_SUCC;}void zUfiSms_GetDefaultCfgPara(void){
unsigned char sts_flag=(0xe1b+360-0xf83);unsigned char mem_store_flag=
(0x49+2627-0xa8c);unsigned int tp_validity_period=(0x1189+2366-0x1ac7);char 
Temp_sms_vp[(0xed7+3893-0x1e04)]={(0xec8+2653-0x1925)};CHAR reportEnable[
(0xa03+1257-0xeba)]={(0x17f6+230-0x18dc)};CHAR smsLocation[(0x91b+4969-0x1c52)]=
{(0xf37+4878-0x2245)};CHAR sendfailRetry[(0x6df+2273-0xf8e)]={
(0x10cd+4461-0x223a)};CHAR outdateDelete[(0x20e0+402-0x2240)]={
(0x48f+2077-0xcac)};CHAR defaultStore[(0x7b4+1984-0xf42)]={(0x50c+996-0x8f0)};
sc_cfg_get(NV_REPORT_ENABLE,reportEnable,sizeof(reportEnable));sc_cfg_get(
NV_SMS_LOCATION_SET,smsLocation,sizeof(smsLocation));sc_cfg_get(
NV_SENDFAIL_RETRY,sendfailRetry,sizeof(sendfailRetry));sc_cfg_get(
NV_OUTDATE_DELETE,outdateDelete,sizeof(outdateDelete));sc_cfg_get(
NV_DEFAULT_STORE,defaultStore,sizeof(defaultStore));if((0x1519+4232-0x25a1)==
strcmp(reportEnable,"\x31")){sts_flag=(0x11aa+774-0x14af);}g_zUfiSms_CurSmsPara.
status_report_on=sts_flag;if((0x168c+2771-0x215f)==strcmp(smsLocation,"\x4d\x45"
)){mem_store_flag=(0x84d+7023-0x23bc);}else{mem_store_flag=(0xabb+2872-0x15f2);}
g_zUfiSms_CurSmsPara.mem_store=(unsigned int)((0x213+4470-0x1389)==
mem_store_flag?WMS_STORAGE_TYPE_NV_V01:WMS_STORAGE_TYPE_UIM_V01);sc_cfg_get(
NV_SMS_VP,Temp_sms_vp,sizeof(Temp_sms_vp));tp_validity_period=
(0x1e5d+1064-0x2186);if((0xb4a+3320-0x1842)==strncmp(Temp_sms_vp,
"\x6c\x6f\x6e\x67\x65\x73\x74",(0x2d5+705-0x58f))){tp_validity_period=
(0x1ac6+381-0x1b44);}if((0xa7f+2736-0x152f)==strncmp(Temp_sms_vp,
"\x6f\x6e\x65\x5f\x64\x61\x79",(0xb6b+3090-0x1776))){tp_validity_period=
(0xeb+2272-0x924);}if((0x758+5997-0x1ec5)==strncmp(Temp_sms_vp,
"\x6f\x6e\x65\x77\x65\x65\x6b",(0x1e2+6204-0x1a17))){tp_validity_period=
(0xc93+2702-0x1674);}if((0xaa2+3643-0x18dd)==strncmp(Temp_sms_vp,
"\x74\x77\x65\x6c\x76\x65\x68",(0xbe3+3681-0x1a3d))){tp_validity_period=
(0x60c+4071-0x1564);}g_zUfiSms_CurSmsPara.tp_validity_period=tp_validity_period;
if((0x6c0+4052-0x1694)==strcmp(sendfailRetry,"\x31")){g_zUfiSms_CurSmsPara.
sendfail_retry_on=(0x915+2017-0x10f5);}else{g_zUfiSms_CurSmsPara.
sendfail_retry_on=(0xc3f+823-0xf76);}if((0x878+274-0x98a)==strcmp(outdateDelete,
"\x31")){g_zUfiSms_CurSmsPara.outdate_delete_on=(0x148+1908-0x8bb);}else{
g_zUfiSms_CurSmsPara.outdate_delete_on=(0xe8a+2731-0x1935);}if(
(0x991+6922-0x249b)==strcmp(defaultStore,"\x73\x69\x6d")){strncpy(
g_zUfiSms_CurSmsPara.default_store,"\x73\x69\x6d",sizeof(g_zUfiSms_CurSmsPara.
default_store)-(0x1188+4731-0x2402));}else{strncpy(g_zUfiSms_CurSmsPara.
default_store,"\x6e\x76",sizeof(g_zUfiSms_CurSmsPara.default_store)-
(0x15a+2774-0xc2f));}}void zUfiSms_GetDefaultPara(void){memset(&
g_zUfiSms_CurSmsPara,(0x41c+779-0x727),sizeof(T_zUfiSms_ParaInfo));
g_zUfiSms_CurSmsPara.status_report_on=(0xbe5+4091-0x1be0);g_zUfiSms_CurSmsPara.
mem_store=WMS_STORAGE_TYPE_NV_V01;g_zUfiSms_CurSmsPara.tp_validity_period=
ZTE_WMS_SMS_DEFAULT_TP_VALIDITY_PERIOD_GW;g_zUfiSms_CurSmsPara.sendfail_retry_on
=(0x37+9321-0x24a0);g_zUfiSms_CurSmsPara.outdate_delete_on=(0xe46+4089-0x1e3f);
strncpy(g_zUfiSms_CurSmsPara.default_store,"\x6e\x76",sizeof(
g_zUfiSms_CurSmsPara.default_store)-(0x1257+1749-0x192b));}
#if (0x976+1463-0xf2d)
T_zUfiSms_CmdStatus zUfiSms_SetParameters(T_zUfiSms_CmdMsgBuff*ptSmsBuffer){
T_zUfiSms_ParaInfo*ptSmsParameter=NULL;T_zUfiSms_ParaInfo tNewSmsParameter={
(0x1211+5015-0x25a8)};if(NULL==ptSmsBuffer){return WMS_CMD_FAILED;}
ptSmsParameter=(T_zUfiSms_ParaInfo*)(&(ptSmsBuffer->cmd_info.set_sms_para));
memcpy((void*)&tNewSmsParameter,(void*)ptSmsParameter,sizeof(T_zUfiSms_ParaInfo)
);
#ifndef TSP_MODEL
if((g_zUfiSms_CurSmsPara.mem_store!=ptSmsParameter->mem_store)){if(
ZUFI_SMS_FAILURE==zUfiSms_SetCpms(ptSmsParameter)){at_print(LOG_ERR,
"\x73\x65\x74\x20\x63\x66\x67\x20\x72\x6f\x75\x74\x65\x73\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return WMS_CMD_FAILED;}}
#endif
if(strlen(ptSmsParameter->sca)!=(0x101+4016-0x10b1)){if(ZUFI_SMS_FAILURE==
zUfiSms_SetCsca(ptSmsParameter)){return WMS_CMD_FAILED;}}if(ZUFI_SMS_FAILURE==
zUfiSms_SetDbParameters(ptSmsParameter)){return WMS_CMD_FAILED;}else{sc_cfg_set(
"\x73\x6d\x73\x5f\x63\x65\x6e\x74\x65\x72\x5f\x6e\x75\x6d",ptSmsParameter->sca);
}memcpy((void*)&g_zUfiSms_CurSmsPara,(void*)&tNewSmsParameter,sizeof(
T_zUfiSms_ParaInfo));return WMS_CMD_SUCCESS;}
#endif
int zUfiSms_LoadSmsPara(){int count=(0xe81+976-0x1251);if(ZUFI_SUCC!=
zUfiSms_IsDbEmpty(ZTE_WMS_DB_PARAMETER_TABLE,&count)){return ZUFI_FAIL;}if(count
==(0x168d+2188-0x1f19)){zUfiSms_GetDefaultCfgPara();if((0x10d0+494-0x12be)!=
zUfiSms_SetDbParameters(&g_zUfiSms_CurSmsPara)){return ZUFI_FAIL;}}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x68\x61\x76\x65\x20\x64\x65\x66\x61\x75\x6c\x74\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x20\x69\x6e\x20\x64\x61\x74\x61\x62\x61\x73\x65\x2e" "\n"
);if(ZUFI_SUCC!=zUfiSms_GetDbParameters()){return ZUFI_FAIL;}if(
WMS_STORAGE_TYPE_NV_V01!=g_zUfiSms_CurSmsPara.mem_store&&
WMS_STORAGE_TYPE_UIM_V01!=g_zUfiSms_CurSmsPara.mem_store){zUfiSms_GetDefaultPara
();if((0x834+3532-0x1600)!=zUfiSms_SetDbParameters(&g_zUfiSms_CurSmsPara)){
printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x74\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x73\x20\x66\x61\x69\x6c\x73\x20\x32\x2e" "\n"
);return ZUFI_FAIL;}}}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4c\x6f\x61\x64\x53\x6d\x73\x50\x61\x72\x61\x20\x73\x75\x63\x63\x2e" "\n"
);return ZUFI_SUCC;}int zUfiSms_CheckMemoryFull(T_zUfiSms_MemoryType mem_store){
int total_count=(0x1919+3299-0x25fc);if((ZTE_WMS_MEMORY_SIM==mem_store)||(
ZTE_WMS_MEMORY_MAX==mem_store)){if(ZUFI_FAIL==zUfiSms_GetTotalCount(
ZTE_WMS_DB_SIM_TABLE,&total_count)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x67\x65\x74\x20\x74\x61\x62\x6c\x65\x20\x74\x6f\x74\x61\x6c\x20\x63\x6f\x75\x6e\x74\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x68\x65\x63\x6b\x4d\x65\x6d\x6f\x72\x79\x46\x75\x6c\x6c\x20\x67\x65\x74\x20\x73\x69\x6d\x20\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2e" "\n"
,total_count);if(total_count>=(int)g_zUfiSms_StoreCapablity[
WMS_STORAGE_TYPE_UIM_V01]){g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_UIM_V01]=TRUE;
}else{g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_UIM_V01]=FALSE;}}if((
ZTE_WMS_MEMORY_NV==mem_store)||(ZTE_WMS_MEMORY_MAX==mem_store)){if(ZUFI_FAIL==
zUfiSms_GetTotalCount(ZTE_WMS_DB_NV_TABLE,&total_count)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x3a\x67\x65\x74\x20\x74\x61\x62\x6c\x65\x20\x74\x6f\x74\x61\x6c\x20\x63\x6f\x75\x6e\x74\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x68\x65\x63\x6b\x4d\x65\x6d\x6f\x72\x79\x46\x75\x6c\x6c\x20\x67\x65\x74\x20\x4d\x45\x20\x63\x6f\x75\x6e\x74\x3a\x25\x64\x2e" "\n"
,total_count);if(total_count>=(int)g_zUfiSms_StoreCapablity[
WMS_STORAGE_TYPE_NV_V01]){g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]=TRUE;}
else{g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_NV_V01]=FALSE;}}return ZUFI_SUCC;}
int zUfiSms_SetStorePara(char*mem_store){if((0x699+2274-0xf7b)==strcmp(mem_store
,"\x53\x4d")){g_zUfiSms_CurSmsPara.mem_store=(unsigned int)
WMS_STORAGE_TYPE_UIM_V01;(void)sc_cfg_set(NV_SMS_LOCATION_SET,"\x30");}else if(
(0xad1+5528-0x2069)==strcmp(mem_store,"\x4d\x45")){g_zUfiSms_CurSmsPara.
mem_store=(unsigned int)WMS_STORAGE_TYPE_NV_V01;(void)sc_cfg_set(
NV_SMS_LOCATION_SET,"\x31");}else if((0x701+1291-0xc0c)==strcmp(mem_store,
"\x53\x52")){g_zUfiSms_CurSmsPara.mem_store=(unsigned int)(0xb7+9635-0x2658);(
void)sc_cfg_set(NV_SMS_LOCATION_SET,"\x32");}else{g_zUfiSms_CurSmsPara.mem_store
=(unsigned int)WMS_STORAGE_TYPE_NONE_V01;(void)sc_cfg_set(NV_SMS_LOCATION_SET,
"\x2d\x31");}if(ZUFI_FAIL==zUfiSms_SetDbParameters(&g_zUfiSms_CurSmsPara)){
at_print(LOG_ERR,
"\x63\x61\x6e\x20\x6e\x6f\x74\x20\x73\x65\x74\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x73\x2e" "\n"
);return ZUFI_FAIL;}return ZUFI_SUCC;}int zUfiSms_SetScaPara(char*sca){strncpy(
g_zUfiSms_CurSmsPara.sca,sca,sizeof(g_zUfiSms_CurSmsPara.sca)-(0x87b+926-0xc18))
;if(ZUFI_FAIL==zUfiSms_SetDbParameters(&g_zUfiSms_CurSmsPara)){at_print(LOG_ERR,
"\x63\x61\x6e\x20\x6e\x6f\x74\x20\x73\x65\x74\x20\x70\x61\x72\x61\x6d\x65\x74\x65\x72\x73\x2e" "\n"
);return ZUFI_FAIL;}return ZUFI_SUCC;}void zUfiSms_SetGlobalDcsLang(unsigned 
char cDcs){if(cDcs==(0x109a+5067-0x2464)){g_zUfiSms_Dcs=DCS_ASC;
g_zUfiSms_Language=NOT_DEFINE_LANGUAGE;}else if(cDcs==(0x6b8+5235-0x1b29)){
g_zUfiSms_Dcs=DCS_USC;g_zUfiSms_Language=NOT_DEFINE_LANGUAGE;}else if(cDcs==
(0x3f2+8284-0x244b)){g_zUfiSms_Dcs=DCS_ASC;g_zUfiSms_Language=DCS_GSM7_SPANISH;}
else if(cDcs==(0x6a2+3460-0x1421)){g_zUfiSms_Dcs=DCS_ASC;g_zUfiSms_Language=
DCS_GSM7_PORTUGUESE;}else{g_zUfiSms_Dcs=DCS_ASC;g_zUfiSms_Language=
DCS_GSM7_DEFAULT;}}int zUfiSms_FillGroupSms(T_zUfiSms_SendReq*ptSendMsg,
T_zUfiSms_GroupInfo*ptGroupSms){int i;if(NULL==ptSendMsg||NULL==ptGroupSms||
ZTE_WMS_SEND_NUM_MAX<ptSendMsg->receiver_count){return ZUFI_FAIL;}ptGroupSms->
total_receiver=ptSendMsg->receiver_count;for(i=(0x167+3545-0xf40);i<ptGroupSms->
total_receiver;i++){strncpy(ptGroupSms->receivers[i],ptSendMsg->dest_num[i],
ZTE_WMS_ADDRESS_LEN_MAX);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x46\x69\x6c\x6c\x47\x72\x6f\x75\x70\x53\x6d\x73\x20\x72\x65\x63\x65\x69\x76\x65\x72\x73\x5b\x25\x64\x5d\x3d\x25\x73" "\n"
,i,ptGroupSms->receivers[i]);}ptGroupSms->current_receiver=(0x26d+5298-0x171f);
return ZUFI_SUCC;}int zUfiSms_FillConcatSms(T_zUfiSms_SendReq*pSendSrcMsg,
T_zUfiSms_ConcatInfo*pDestConcatMsg){int iTotalLen=(0x1639+876-0x19a5);int 
iUnitlen=(0x10ec+3917-0x2039);int iSegNo=(0x104f+3006-0x1c0d);unsigned char*
pSmsConverted=NULL;unsigned char acConvertContent[
ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX*ZTE_WMS_CONCAT_SMS_COUNT_MAX*
(0x18e8+1823-0x2003)+(0x59f+261-0x6a0)]={(0x1346+2203-0x1be1)};unsigned char 
acTmpContent[ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX*ZTE_WMS_CONCAT_SMS_COUNT_MAX*
(0x6c0+2120-0xf04)+(0x188d+1587-0x1ebc)]={(0x1d92+403-0x1f25)};int iTimeZone=
(0x1122+4253-0x21bf);int tmp_i=(0x374+6296-0x1c0c);if(NULL==pSendSrcMsg||NULL==
pDestConcatMsg){return-(0xb37+723-0xe09);}iTotalLen=pSendSrcMsg->msg_len;if(
ZUFI_FAIL==zUfiSms_GetSmsContent(acTmpContent,sizeof(acTmpContent))){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x53\x6d\x73\x43\x6f\x6e\x74\x65\x6e\x74\x20\x46\x61\x69\x6c\x2e" "\n"
);return-(0x13+9938-0x26e4);}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x53\x6d\x73\x43\x6f\x6e\x74\x65\x6e\x74\x3a\x25\x73\x2e" "\n"
,acTmpContent);(void)String2Bytes(acTmpContent,acConvertContent,(int)strlen(
acTmpContent));memset(acTmpContent,(0xfd3+3104-0x1bf3),sizeof(acTmpContent));if(
DCS_USC==g_zUfiSms_Dcs){pSmsConverted=acConvertContent;}else{if(
NOT_DEFINE_LANGUAGE==g_zUfiSms_Language){iTotalLen=
zUfiSms_ConvertAsciiToGsmDefault(acConvertContent,acTmpContent,pSendSrcMsg->
msg_len);}else if(DCS_GSM7_SPANISH==g_zUfiSms_Language){iTotalLen=
zUfiSms_ConvertUcs2ToSpanish(acConvertContent,acTmpContent,pSendSrcMsg->msg_len)
;}else if(DCS_GSM7_PORTUGUESE==g_zUfiSms_Language){iTotalLen=
zUfiSms_ConvertUcs2ToPortuguese(acConvertContent,acTmpContent,pSendSrcMsg->
msg_len);}else{iTotalLen=zUfiSms_ConvertUcs2ToGsmDefault(acConvertContent,
acTmpContent,pSendSrcMsg->msg_len);}pSendSrcMsg->msg_len=iTotalLen;pSmsConverted
=acTmpContent;}if(iTotalLen>(sizeof(acConvertContent)-(0x6e8+1906-0xe59))){
iTotalLen=sizeof(acConvertContent)-(0x4f2+8277-0x2546);}pDestConcatMsg->sms_len=
iTotalLen;if((iTotalLen>ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX)||(g_zUfiSms_Language==
DCS_PORTUGUESE&&iTotalLen>(0x501+3429-0x11cb))||((g_zUfiSms_Dcs==DCS_USC)&&
iTotalLen>ZTE_WMS_SMS_MSG_CHAR_MAX)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x3d\x3d\x3d\x3d\x3d\x73\x65\x6e\x64\x20\x63\x6f\x6e\x74\x61\x63\x74\x20\x73\x6d\x73\x2e" "\n"
);if(g_zUfiSms_Dcs==DCS_USC){iUnitlen=ZTE_WMS_SMS_CONCAT_ELEMNT_UCS_LEN*
(0x11d1+2731-0x1c7a);}else{if(g_zUfiSms_Language==NOT_DEFINE_LANGUAGE||
g_zUfiSms_Language==DCS_GSM7_DEFAULT){iUnitlen=ZTE_WMS_SMS_CONCAT_ELEMNT_ASC_LEN
;}else{iUnitlen=ZTE_WMS_SMS_CONCAT_ELEMNT_LANGUAGE_LEN;}}while(iTotalLen>
(0x62a+3795-0x14fd)&&iSegNo<ZTE_WMS_CONCAT_SMS_COUNT_MAX){memcpy(pDestConcatMsg
->msg_contents[iSegNo],pSmsConverted,iUnitlen);iTotalLen-=iUnitlen;pSmsConverted
+=iUnitlen;iSegNo++;}pDestConcatMsg->total_msg=iSegNo;}else{iUnitlen=iTotalLen;
pDestConcatMsg->total_msg=(0xf33+5897-0x263b);memcpy(pDestConcatMsg->
msg_contents[(0x22bf+1007-0x26ae)],pSmsConverted,iTotalLen);printf(
"\x5b\x53\x4d\x53\x5d\x20\x3d\x3d\x3d\x3d\x3d\x73\x65\x6e\x64\x20\x6e\x6f\x72\x6d\x61\x6c\x20\x73\x6d\x73\x2e\x6c\x65\x6e\x3a\x25\x64\x2e\x63\x6f\x6e\x74\x65\x6e\x74\x3a\x25\x73\x2e" "\n"
,iUnitlen,pDestConcatMsg->msg_contents[(0xb79+3902-0x1ab7)]);}
g_zUfiSms_ConcatTotalNum=pDestConcatMsg->total_msg;pDestConcatMsg->
current_sending=(0x3b5+5930-0x1adf);memcpy(&(pDestConcatMsg->date),&(pSendSrcMsg
->date),sizeof(T_zUfiSms_Date));tmp_i=atoi(pDestConcatMsg->date.timezone);if(
tmp_i<INT_MIN+(0x25c+1765-0x940)||tmp_i>INT_MAX-(0x8fc+2043-0x10f6)){printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x70\x44\x65\x73\x74\x43\x6f\x6e\x63\x61\x74\x4d\x73\x67\x20\x74\x69\x6d\x65\x7a\x6f\x6e\x65\x3a\x25\x64\x2e" "\n"
,tmp_i);return ZUFI_FAIL;}iTimeZone=tmp_i*(0x37f+4717-0x15e8);memset(
pDestConcatMsg->date.timezone,(0x2209+586-0x2453),sizeof(pDestConcatMsg->date.
timezone));if(iTimeZone>(0x1b2+7465-0x1edb)){snprintf(pDestConcatMsg->date.
timezone,sizeof(pDestConcatMsg->date.timezone),"\x2b\x25\x64",iTimeZone);}else{
snprintf(pDestConcatMsg->date.timezone,sizeof(pDestConcatMsg->date.timezone),
"\x25\x64",iTimeZone);}pSmsConverted=NULL;return iUnitlen;}void 
zUfiSms_FillDateheader(T_zUfiSms_SubmitTpdu*ptSubmit,T_zUfiSms_ConcatInfo*
ptConcatSms,T_zUfiSms_DbStoreData*ptDbSaveData){unsigned char iHeaderNum=
(0xfa+1875-0x84d);iHeaderNum=ptSubmit->user_data.num_headers;ptSubmit->
user_data_header_present=TRUE;if((0x31a+7061-0x1eaf)==ptConcatSms->
current_sending){g_zUfiSms_ConcatSmsReference++;(void)zUfiSms_SetConcatMaxRefer(
g_zUfiSms_ConcatSmsReference);}ptSubmit->user_data.headers[iHeaderNum].header_id
=WMS_UDH_CONCAT_8;ptSubmit->user_data.headers[iHeaderNum].u.concat_8.total_sm=
ptConcatSms->total_msg;ptSubmit->user_data.headers[iHeaderNum].u.concat_8.
seq_num=ptConcatSms->current_sending+(0x8d4+4459-0x1a3e);ptSubmit->user_data.
headers[iHeaderNum].u.concat_8.msg_ref=g_zUfiSms_ConcatSmsReference%
(0xdda+797-0xff8);ptSubmit->user_data.num_headers++;ptDbSaveData->concat_sms=
(0x7bc+7240-0x2403);ptDbSaveData->concat_info[(0x85+3874-0xfa7)]=
g_zUfiSms_ConcatSmsReference;}int zUfiSms_MakeFinalCmgsBuf(){SMS_PARAM tSmsData;
int iPduLength=(0xa21+6316-0x22cd);int nSmscLength=(0xc74+4208-0x1ce4);char 
Tempstrr[(0x762+3288-0x1438)]={(0x6fc+2497-0x10bd)};char Temp_sms_vp[
(0xd2b+5274-0x21bd)]={(0x1d9+1673-0x862)};char tmpBuf1[(0x18bc+1272-0x1db1)]={
(0x847+7357-0x2504)};char tmpBuf2[(0xdd5+4914-0x2101)]={(0xd89+4063-0x1d68)};
CHAR smsCenter[(0x8a5+5473-0x1dd4)]={(0xfcb+3959-0x1f42)};memset(&tSmsData,
(0x187c+482-0x1a5e),sizeof(SMS_PARAM));memset(&g_zUfiSms_FinalCmgsBuf,
(0x36c+6180-0x1b90),sizeof(T_zSms_SendSmsReq));if(CODE_GSM7==
g_zUfiSms_SendingSms.TP_DCS){int i=(0x234+5599-0x1813);for(;i<
g_zUfiSms_SendingSms.TP_UDLength;i++){tSmsData.TP_UD[i]=g_zUfiSms_SendingSms.
TP_UD[i];}tSmsData.TP_UDLength=g_zUfiSms_SendingSms.TP_UDLength;}else{snprintf(
tSmsData.TP_UD,sizeof(tSmsData.TP_UD),"\x25\x73",g_zUfiSms_SendingSms.TP_UD);
tSmsData.TP_UDLength=strlen(tSmsData.TP_UD);}sc_cfg_get(NV_SMS_CENTER_NUM,
smsCenter,sizeof(smsCenter));snprintf(tSmsData.SCA,sizeof(tSmsData.SCA),
"\x25\x73",smsCenter);snprintf(tSmsData.TPA,sizeof(tSmsData.TPA),"\x25\x73",
g_zUfiSms_SendingSms.TPA);tSmsData.TP_DCS=g_zUfiSms_SendingSms.TP_DCS;sc_cfg_get
(NV_REPORT_ENABLE,Tempstrr,sizeof(Tempstrr));if(((0x104d+939-0x13f8)==strncmp(
Tempstrr,"\x31",(0xb73+3128-0x17aa)))&&(g_zUfiSms_ConcatSms.current_sending==
g_zUfiSms_ConcatSms.total_msg-(0x8a0+3280-0x156f))){tSmsData.TP_SRR=
(0x160f+4195-0x2671);}else{tSmsData.TP_SRR=(0x452+8327-0x24d9);}tSmsData.TP_UDHI
=g_zUfiSms_SendingSms.TP_UDHI;tSmsData.TP_VP=(0x16bd+2139-0x1e19);sc_cfg_get(
NV_SMS_VP,Temp_sms_vp,sizeof(Temp_sms_vp));if((0x1e40+1857-0x2581)==strncmp(
Temp_sms_vp,"\x6c\x6f\x6e\x67\x65\x73\x74",(0x1c22+703-0x1eda))){tSmsData.TP_VP=
(0x1a12+409-0x1aac);}else if((0x5e6+2734-0x1094)==strncmp(Temp_sms_vp,
"\x6f\x6e\x65\x5f\x64\x61\x79",(0x1828+565-0x1a56))){tSmsData.TP_VP=
(0x15a6+170-0x15a9);}else if((0x111f+2043-0x191a)==strncmp(Temp_sms_vp,
"\x6f\x6e\x65\x77\x65\x65\x6b",(0x4f9+1022-0x8f0))){tSmsData.TP_VP=
(0xe73+3862-0x1cdc);}else if((0x17f+8286-0x21dd)==strncmp(Temp_sms_vp,
"\x74\x77\x65\x6c\x76\x65\x68",(0x15d+1856-0x896))){tSmsData.TP_VP=
(0x21c6+411-0x22d2);}tSmsData.TP_PID=(0x2070+1518-0x265e);
#if (0x68f+885-0xa03)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x62\x65\x67\x69\x6e\x3a\x25\x73" "\n",
g_zUfiSms_FinalCmgsBuf.pdu);
#endif    
iPduLength=EncodePdu_Submit(&tSmsData,g_zUfiSms_FinalCmgsBuf.pdu);
#if (0x2a3+7857-0x2153)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4d\x61\x6b\x65\x46\x69\x6e\x61\x6c\x43\x6d\x67\x73\x42\x75\x66\x20\x6d\x61\x6b\x65\x20\x70\x64\x75\x20\x64\x61\x74\x61" "\n"
);printf("\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x25\x73" "\n",
g_zUfiSms_FinalCmgsBuf.pdu);
#endif
(void)String2Bytes(g_zUfiSms_FinalCmgsBuf.pdu,tmpBuf1,(0xce4+1905-0x1453));
Bytes2String(tmpBuf1,tmpBuf2,strlen(tmpBuf1));nSmscLength=atoi(tmpBuf2);if(
nSmscLength<(0x3aa+7430-0x20b0)||nSmscLength>INT_MAX-(0x29f+5023-0x163d)){
at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x6e\x53\x6d\x73\x63\x4c\x65\x6e\x67\x74\x68\x20\x65\x72\x72\x3a\x25\x64" "\n"
,nSmscLength);nSmscLength=(0x147c+3521-0x223d);;}nSmscLength++;
g_zUfiSms_FinalCmgsBuf.length=iPduLength/(0xf30+1624-0x1586)-nSmscLength;
#if (0x104+2082-0x925)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4d\x61\x6b\x65\x46\x69\x6e\x61\x6c\x43\x6d\x67\x73\x42\x75\x66\x20\x6d\x61\x6b\x65\x20\x65\x6e\x64\x20\x70\x64\x75\x20\x64\x61\x74\x61" "\n"
);printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x6c\x65\x6e\x3a\x25\x64\x2c\x20\x25\x73" "\n"
,g_zUfiSms_FinalCmgsBuf.length,g_zUfiSms_FinalCmgsBuf.pdu);
#endif
return ZUFI_SUCC;}int zUfiSms_FillSubmitTpdu(T_zUfiSms_ConcatInfo*ptConcatSms,
T_zUfiSms_GroupInfo*ptGroupSms,int iSmsLen,T_zUfiSms_SubmitTpdu*ptSubmit,
T_zUfiSms_DbStoreData*ptDbSaveData){if(NULL==ptSubmit||NULL==ptConcatSms||NULL==
ptGroupSms||NULL==ptDbSaveData){return-(0x442+45-0x46e);}ptSubmit->
reject_duplicates=FALSE;ptSubmit->reply_path_present=FALSE;ptSubmit->
user_data_header_present=FALSE;ptSubmit->status_report_enabled=
g_zUfiSms_CurSmsPara.status_report_on;if(((0x9d6+5537-0x1f76)<ptConcatSms->
total_msg)&&(ptConcatSms->current_sending+(0xe26+1143-0x129c)<ptConcatSms->
total_msg)){ptSubmit->status_report_enabled=(0xf90+5958-0x26d6);}ptSubmit->
message_reference=g_zUfiSms_MsgRefer;ptSubmit->pid=WMS_PID_DEFAULT;ptSubmit->dcs
.msg_class=(wms_message_class_e_type)(0xab0+5004-0x1e38);ptSubmit->dcs.
is_compressed=(0x18e8+1312-0x1e08);ptSubmit->dcs.alphabet=(g_zUfiSms_Dcs==
DCS_ASC)?WMS_GW_ALPHABET_7_BIT_DEFAULT:WMS_GW_ALPHABET_UCS2;ptSubmit->validity.
format=WMS_GW_VALIDITY_RELATIVE;zUfiSms_DecodeRelativeTime(g_zUfiSms_CurSmsPara.
tp_validity_period,&ptSubmit->validity.u.time);ptSubmit->user_data.num_headers=
(0x9f2+2660-0x1456);if(ptConcatSms->total_msg>(0x15ba+3293-0x2296)){
zUfiSms_FillDateheader(ptSubmit,ptConcatSms,ptDbSaveData);ptDbSaveData->
concat_info[(0x5aa+2428-0xf24)]=ptConcatSms->current_sending+(0x173+7395-0x1e55)
;ptDbSaveData->concat_info[(0x1b9c+1735-0x2262)]=ptConcatSms->total_msg;}if(
g_zUfiSms_Language==DCS_PORTUGUESE){UINT8 i=ptSubmit->user_data.num_headers;
ptSubmit->user_data_header_present=TRUE;ptSubmit->user_data.headers[i].header_id
=WMS_UDH_NAT_LANG_SS;ptSubmit->user_data.headers[ptSubmit->user_data.num_headers
].u.nat_lang_ss.nat_lang_id=WMS_UDH_NAT_LANG_PORTUGUESE;ptSubmit->user_data.
num_headers++;g_zUfiSms_IsLanguageShift=WMS_UDH_NAT_LANG_SS;}ptSubmit->user_data
.sm_len=iSmsLen;memcpy(ptSubmit->user_data.sm_data,ptConcatSms->msg_contents[
ptConcatSms->current_sending],iSmsLen);if(ptGroupSms->receivers[ptGroupSms->
current_receiver][(0xd9a+3014-0x1960)]==((char)(0xe3c+2309-0x1716))){(void)
zUfiSms_CharToInt(ptGroupSms->receivers[ptGroupSms->current_receiver]+
(0xa69+3224-0x1700),strlen(ptGroupSms->receivers[ptGroupSms->current_receiver])-
(0xf19+698-0x11d2),ptSubmit->address.digits);ptSubmit->address.number_type=
WMS_NUMBER_INTERNATIONAL;ptSubmit->address.number_of_digits=strlen(ptGroupSms->
receivers[ptGroupSms->current_receiver])-(0x28f+7521-0x1fef);}else if(ptGroupSms
->receivers[ptGroupSms->current_receiver][(0x93f+3713-0x17c0)]==
((char)(0x536+2197-0xd9b))&&ptGroupSms->receivers[ptGroupSms->current_receiver][
(0x3b6+4789-0x166a)]==((char)(0x799+7672-0x2561))){(void)zUfiSms_CharToInt(
ptGroupSms->receivers[ptGroupSms->current_receiver]+(0x1e0b+836-0x214d),strlen(
ptGroupSms->receivers[ptGroupSms->current_receiver])-(0x8b+2376-0x9d1),ptSubmit
->address.digits);ptSubmit->address.number_type=WMS_NUMBER_INTERNATIONAL;
ptSubmit->address.number_of_digits=strlen(ptGroupSms->receivers[ptGroupSms->
current_receiver])-(0xf7c+2285-0x1867);}else{(void)zUfiSms_CharToInt(ptGroupSms
->receivers[ptGroupSms->current_receiver],strlen(ptGroupSms->receivers[
ptGroupSms->current_receiver]),ptSubmit->address.digits);ptSubmit->address.
number_type=WMS_NUMBER_UNKNOWN;ptSubmit->address.number_of_digits=strlen(
ptGroupSms->receivers[ptGroupSms->current_receiver]);}ptSubmit->address.
digit_mode=(wms_digit_mode_e_type)(0x693+4731-0x190e);ptSubmit->address.
number_mode=(wms_number_mode_e_type)(0x41d+7122-0x1fef);ptSubmit->address.
number_plan=WMS_NUMBER_PLAN_TELEPHONY;memset(&g_zUfiSms_SendingSms,
(0x1c30+1178-0x20ca),sizeof(SMS_PARAM));snprintf(g_zUfiSms_SendingSms.TPA,sizeof
(g_zUfiSms_SendingSms.TPA),"\x25\x73",ptGroupSms->receivers[ptGroupSms->
current_receiver]);if(g_zUfiSms_Language!=NOT_DEFINE_LANGUAGE){
g_zUfiSms_SendingSms.TP_DCS=CODE_GSM7;}else{if(g_zUfiSms_Dcs==DCS_USC){
g_zUfiSms_SendingSms.TP_DCS=CODE_UCS2;}else{g_zUfiSms_SendingSms.TP_DCS=
CODE_GSM8;}}if(g_zUfiSms_SendingSms.TP_DCS==CODE_GSM7){
zUfiSms_FillGlobalTpudGsm7(ptSubmit,ptConcatSms,ptDbSaveData);}else{
zUfiSms_FillGlobalTpudUcs2(ptSubmit,ptConcatSms,ptDbSaveData);}(void)
zUfiSms_MakeFinalCmgsBuf();return ZUFI_SUCC;}void zUfiSms_FillSca(
T_zUfiSms_ClientMsg*ptClientMsg){char sca[ZTE_WMS_SCA_LEN_MAX]={
(0x2b2+6230-0x1b08)};int i=(0x50a+8345-0x25a3);if(NULL==ptClientMsg){return;}
memcpy((void*)sca,(void*)(g_zUfiSms_CurSmsPara.sca),sizeof(g_zUfiSms_CurSmsPara.
sca));if(sca[(0xa47+482-0xc29)]==((char)(0x9dd+2252-0x127e))){ptClientMsg->u.
gw_message.sc_address.number_type=WMS_NUMBER_INTERNATIONAL;}ptClientMsg->u.
gw_message.sc_address.digit_mode=WMS_DIGIT_MODE_8_BIT;ptClientMsg->u.gw_message.
sc_address.number_plan=WMS_NUMBER_PLAN_TELEPHONY;ptClientMsg->u.gw_message.
sc_address.number_of_digits=strlen(sca);if(sca[(0xceb+5689-0x2324)]==
((char)(0x274+1852-0x985))){ptClientMsg->u.gw_message.sc_address.
number_of_digits--;for(i=(0xdb0+1475-0x1373);i<ptClientMsg->u.gw_message.
sc_address.number_of_digits;i++){sca[i]=sca[i+(0x1bc5+1819-0x22df)];}}else if(
sca[(0x1345+205-0x1412)]==((char)(0x10d5+3047-0x1c8c))&&sca[(0x1365+3711-0x21e3)
]==((char)(0x14e7+2513-0x1e88))){ptClientMsg->u.gw_message.sc_address.
number_of_digits-=(0x10d8+3714-0x1f58);for(i=(0x9fd+7299-0x2680);i<ptClientMsg->
u.gw_message.sc_address.number_of_digits;i++){sca[i]=sca[i+(0x18ea+1194-0x1d92)]
;}}(void)zUfiSms_CharToInt(sca,ptClientMsg->u.gw_message.sc_address.
number_of_digits,ptClientMsg->u.gw_message.sc_address.digits);}void 
zUfiSms_FillDbSaveData(T_zUfiSms_ClientMsg*ptClientMsg,T_zUfiSms_ClientTsData*
ptClientData,T_zUfiSms_ConcatInfo*ptConcatSms,T_zUfiSms_GroupInfo*ptGroupSms,int
 iSmsLen,T_zUfiSms_DbStoreData*ptDbSaveData){if(NULL==ptClientMsg||NULL==
ptClientData||NULL==ptConcatSms||NULL==ptGroupSms||NULL==ptDbSaveData){at_print(
LOG_ERR,"\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73" "\n");return;
}ptDbSaveData->mem_store=ptClientMsg->msg_hdr.mem_store;ptDbSaveData->index=
ptClientMsg->msg_hdr.index;ptDbSaveData->mode=ptClientMsg->msg_hdr.message_mode;
ptDbSaveData->tag=ptClientMsg->msg_hdr.tag;memset(ptDbSaveData->number,
(0x1754+840-0x1a9c),ZTE_WMS_ADDRESS_LEN_MAX+(0x1602+3344-0x2311));memcpy(
ptDbSaveData->number,ptGroupSms->receivers[ptGroupSms->current_receiver],strlen(
ptGroupSms->receivers[ptGroupSms->current_receiver]));ptDbSaveData->tp_dcs=
DCS_USC;ptDbSaveData->tp_pid=ptClientData->u.gw_pp.u.submit.pid;ptDbSaveData->
msg_ref=ptClientData->u.gw_pp.u.submit.message_reference;memset(ptDbSaveData->
sms_content,(0x1387+4-0x138b),sizeof(ptDbSaveData->sms_content));if(
g_zUfiSms_Dcs==DCS_USC){ptDbSaveData->alphabet=WMS_GW_ALPHABET_UCS2;}else if(
g_zUfiSms_Dcs==DCS_ASC){ptDbSaveData->alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;}(
void)zUfiSms_DispatchWtoi(ptConcatSms->msg_contents[ptConcatSms->current_sending
],iSmsLen,(UINT8*)ptDbSaveData->sms_content);memcpy(&(ptDbSaveData->julian_date)
,&(ptConcatSms->date),sizeof(T_zUfiSms_Date));memcpy(&g_zUfiSms_DbStoreData[
ptConcatSms->current_sending],ptDbSaveData,sizeof(zte_wms_db_sms_data_s_type));}
void zUfiSms_SetPduData(T_zUfiSms_ClientMsg*ptClientMsg,T_zUfiSms_DbStoreData*
ptDbSaveData){T_zUfiSms_ClientTsData tClientTsData;ptClientMsg->msg_hdr.
mem_store=WMS_MEMORY_STORE_NV_GW;ptClientMsg->msg_hdr.tag=WMS_TAG_MO_NOT_SENT;
ptClientMsg->msg_hdr.message_mode=WMS_MESSAGE_MODE_GW;ptClientMsg->u.gw_message.
is_broadcast=FALSE;memset((void*)&tClientTsData,(0x1e4c+167-0x1ef3),sizeof(
wms_client_ts_data_s_type));tClientTsData.format=WMS_FORMAT_GW_PP;tClientTsData.
u.gw_pp.tpdu_type=WMS_TPDU_SUBMIT;(void)zUfiSms_FillSubmitTpdu(&
g_zUfiSms_ConcatSms,&g_zUfiSms_GroupSms,g_zUfiSms_UnitLen,&tClientTsData.u.gw_pp
.u.submit,ptDbSaveData);(void)wms_ts_encode(&tClientTsData,&ptClientMsg->u.
gw_message.raw_ts_data);zUfiSms_FillSca(ptClientMsg);zUfiSms_FillDbSaveData(
ptClientMsg,&tClientTsData,&g_zUfiSms_ConcatSms,&g_zUfiSms_GroupSms,
g_zUfiSms_UnitLen,ptDbSaveData);}int zUfiSms_StoreNormalSmsToDb(
T_zUfiSms_DbStoreData*ptDbSaveData,const char*pMemStore,long iSmsId){int result=
ZUFI_SUCC;char*pContent=NULL;int iTotalCount=(0x63c+482-0x81e);pContent=(char*)
malloc((0x505+8624-0x26b1)*ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX);if(pContent==
NULL){return ZUFI_FAIL;}memset(pContent,(0x1420+89-0x1479),(0x77b+7921-0x2668)*
ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX);if(WMS_GW_ALPHABET_7_BIT_DEFAULT==
ptDbSaveData->alphabet){static char data[(0x1010+1278-0x150a)*
ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX+(0xde4+1538-0x13e5)]={(0x543+8288-0x25a3)};
memset(data,(0xf3d+4161-0x1f7e),(0xdbd+3857-0x1cca)*
ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX+(0x1820+1143-0x1c96));(void)
zUfiSms_DecodeContent((char*)ptDbSaveData->sms_content,strlen(ptDbSaveData->
sms_content),FALSE,data);strncpy(pContent,data,(0x8a5+3058-0x1493)*
ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX-(0x673+7637-0x2447));}else{strncpy(
pContent,ptDbSaveData->sms_content,(0x2ac+8396-0x2374)*
ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX-(0x1236+448-0x13f5));}ptDbSaveData->tp_dcs
=(unsigned char)(0x160+8893-0x241b);if(-(0x1db3+174-0x1e60)==iSmsId){if(
ZUFI_FAIL==zUfiSms_GetTotalCount(pMemStore,&iTotalCount)){free(pContent);
pContent=NULL;return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x74\x6f\x72\x65\x4e\x6f\x72\x6d\x61\x6c\x53\x6d\x73\x54\x6f\x44\x62\x20\x69\x54\x6f\x74\x61\x6c\x43\x6f\x75\x6e\x74\x3d\x25\x64" "\n"
,iTotalCount);if(iTotalCount>=g_zUfiSms_StoreCapablity[(strcmp(pMemStore,
"\x6e\x76")?ZTE_WMS_MEMORY_SIM:ZTE_WMS_MEMORY_NV)]){free(pContent);pContent=NULL
;return-(0x999+6787-0x241b);}if(ZUFI_FAIL==zUfiSms_InsertNormalSmsToDb(
ptDbSaveData,pMemStore,pContent)){result=ZUFI_FAIL;}}else{if(ZUFI_FAIL==
zUfiSms_UpdateNormalSmsToDb(ptDbSaveData,pMemStore,pContent,iSmsId)){result=
ZUFI_FAIL;}}free(pContent);pContent=NULL;return result;}static int 
zUfiSms_ConcatDataFree(T_zUfiSms_DbStoreData*ptDbSaveData,int count,char**
out_result){int i=(0x8bb+3073-0x14bc);for(i=(0x132a+2342-0x1c50);i<count;i++){if
(ptDbSaveData->concat_info[(0x469+13-0x474)]==i+(0x133f+2542-0x1d2c)){free(
out_result[i]);out_result[i]=NULL;break;}}return(0x9ac+6734-0x23fa);}int 
zUfiSms_AddNewSmsToConcatData(T_zUfiSms_DbStoreData*ptDbSaveData,char*
pOldContent,char*pFormatNewContent,char*pRealNewContent,T_zUfiSms_DbStoreStr*pac
,int*pConcatTotalNum,int len){int count=(0x169+3451-0xee4);char**out_result=NULL
;char cSegChar=((char)(0x8a7+4988-0x1be8));int i=(0xaf9+1567-0x1118);int 
iTotalSegNum=(0x13a5+2589-0x1dc2);char acContentSeg[(0x741+3524-0x1503)*
ZTE_WMS_SMS_MSG_CONTENT_STORE_LEN_MAX*ZTE_WMS_SMS_COUNT_MAX]={
(0xb66+6733-0x25b3)};char*pCurConPos=acContentSeg;boolean isEsc=FALSE;if(NULL==
pOldContent){return-(0x344+5670-0x1969);}count=zUfiSms_SplitString(pOldContent,&
out_result,cSegChar);for(i=(0x18f1+2287-0x21e0);i<count;i++){if(ptDbSaveData->
concat_info[(0x52+1513-0x639)]==i+(0xed0+3615-0x1cee)){out_result[i]=(char*)
malloc(sizeof(ptDbSaveData->sms_content));memset(out_result[i],
(0x6e4+6976-0x2224),sizeof(ptDbSaveData->sms_content));if(
WMS_GW_ALPHABET_7_BIT_DEFAULT==ptDbSaveData->alphabet){isEsc=
zUfiSms_DecodeContent(ptDbSaveData->sms_content,strlen(ptDbSaveData->sms_content
),isEsc,out_result[i]);}else{strncpy(out_result[i],ptDbSaveData->sms_content,
sizeof(ptDbSaveData->sms_content)-(0x2351+185-0x2409));}break;}}for(i=
(0xa3a+5882-0x2134);i<count;i++){snprintf(acContentSeg+strlen(acContentSeg),
sizeof(acContentSeg)-strlen(acContentSeg),"\x25\x73",out_result[i]);strcat(
pFormatNewContent,out_result[i]);if(i!=count-(0x169c+2502-0x2061)){strcat(
pFormatNewContent,"\x3b");}}strncpy(pRealNewContent,acContentSeg,len);
zUfiSms_ConcatDataFree(ptDbSaveData,count,out_result);free(out_result);
out_result=NULL;count=zUfiSms_SplitString(pac->IndStr,&out_result,cSegChar);for(
i=(0x209b+1501-0x2678);i<count;i++){if(ptDbSaveData->concat_info[
(0x1352+4603-0x254b)]==i+(0x1a97+2360-0x23ce)){out_result[i]=(char*)malloc(
SHORT_INT_LEN);memset(out_result[i],(0x71a+6603-0x20e5),SHORT_INT_LEN);snprintf(
out_result[i],SHORT_INT_LEN,"\x25\x64",ptDbSaveData->index);break;}}for(i=
(0x1794+3167-0x23f3);i<count;i++){snprintf(pac->FormatInd+strlen(pac->FormatInd)
,sizeof(pac->FormatInd)-strlen(pac->FormatInd),"\x25\x73",out_result[i]);if(i!=
count-(0xb3c+6749-0x2598)){snprintf(pac->FormatInd+strlen(pac->FormatInd),sizeof
(pac->FormatInd)-strlen(pac->FormatInd),"\x3b");}}zUfiSms_ConcatDataFree(
ptDbSaveData,count,out_result);free(out_result);out_result=NULL;count=
zUfiSms_SplitString(pac->Seg_Seq,&out_result,cSegChar);for(i=
(0x1a74+1759-0x2153);i<count;i++){if(ptDbSaveData->concat_info[
(0x23c3+91-0x241c)]==i+(0x955+426-0xafe)){out_result[i]=(char*)malloc(
SHORT_INT_LEN);memset(out_result[i],(0x8c8+4451-0x1a2b),SHORT_INT_LEN);snprintf(
out_result[i],SHORT_INT_LEN,"\x25\x64",ptDbSaveData->concat_info[
(0x93a+1701-0xfdd)]);break;}}for(i=(0x47d+253-0x57a);i<count;i++){snprintf(pac->
FormatSeq+strlen(pac->FormatSeq),sizeof(pac->FormatSeq)-strlen(pac->FormatSeq),
"\x25\x73",out_result[i]);if(i!=count-(0x9d9+3656-0x1820)){snprintf(pac->
FormatSeq+strlen(pac->FormatSeq),sizeof(pac->FormatSeq)-strlen(pac->FormatSeq),
"\x3b");}if((0x9d0+5928-0x20f8)!=strcmp(out_result[i],"")){iTotalSegNum++;}}*
pConcatTotalNum=iTotalSegNum;zUfiSms_ConcatDataFree(ptDbSaveData,count,
out_result);free(out_result);out_result=NULL;return(0xf4f+4590-0x213d);}int 
zUfiSms_UpdateConcatSms(T_zUfiSms_DbStoreData*ptDbSaveData,const char*pStorePos,
long iSmsId){T_zUfiSms_DbStoreStr ac={(0x3d0+3474-0x1162)};char*pOldContent=NULL
;char*pFormatNewContent=NULL;char*pRealNewContent=NULL;int iTotalNum=
(0x1213+204-0x12df);int result=ZUFI_SUCC;int spaceLen=(0x84d+5434-0x1d83)*
ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX*ZTE_WMS_CONCAT_SMS_COUNT_MAX+(0x212+3131-0xe49);
pOldContent=(char*)malloc(spaceLen);pFormatNewContent=(char*)malloc(spaceLen);
pRealNewContent=(char*)malloc(spaceLen);if(pOldContent==NULL||pFormatNewContent
==NULL||pRealNewContent==NULL){if(pOldContent)free(pOldContent);if(
pFormatNewContent)free(pFormatNewContent);if(pRealNewContent)free(
pRealNewContent);return ZUFI_FAIL;}memset(pRealNewContent,(0x277+542-0x495),
spaceLen);memset(pOldContent,(0x1ab+6482-0x1afd),spaceLen);memset(
pFormatNewContent,(0x708+5268-0x1b9c),spaceLen);(void)zUfiSms_GetConcatInfo(
pStorePos,iSmsId,&ac,pOldContent,spaceLen);printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x20\x2d\x30\x20\x49\x6e\x64\x53\x74\x72\x3a\x25\x73\x2c\x53\x65\x67\x5f\x53\x65\x71\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x49\x6e\x64\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x53\x65\x71\x3a\x25\x73" "\n"
,ac.IndStr,ac.Seg_Seq,ac.FormatInd,ac.FormatSeq);if(-(0xb64+1967-0x1312)==
zUfiSms_AddNewSmsToConcatData(ptDbSaveData,pOldContent,pFormatNewContent,
pRealNewContent,&ac,&iTotalNum,spaceLen)){result=ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x20\x30\x20\x49\x6e\x64\x53\x74\x72\x3a\x25\x73\x2c\x53\x65\x67\x5f\x53\x65\x71\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x49\x6e\x64\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x53\x65\x71\x3a\x25\x73" "\n"
,ac.IndStr,ac.Seg_Seq,ac.FormatInd,ac.FormatSeq);ptDbSaveData->tp_dcs=
(0x2a4+6201-0x1adb);if(ZUFI_FAIL==zUfiSms_UpdateConcatSmsToDb(ptDbSaveData,
pStorePos,pFormatNewContent,pRealNewContent,&ac,iTotalNum,iSmsId)){result=
ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x74\x65\x65\x74\x20\x31\x20\x49\x6e\x64\x53\x74\x72\x3a\x25\x73\x2c\x53\x65\x67\x5f\x53\x65\x71\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x49\x6e\x64\x3a\x25\x73\x2c\x46\x6f\x72\x6d\x61\x74\x53\x65\x71\x3a\x25\x73" "\n"
,ac.IndStr,ac.Seg_Seq,ac.FormatInd,ac.FormatSeq);free(pRealNewContent);free(
pOldContent);free(pFormatNewContent);pRealNewContent=NULL;pOldContent=NULL;
pFormatNewContent=NULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x55\x70\x64\x61\x74\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x20\x73\x75\x63\x63\x65\x73\x73\x2e" "\n"
);return result;}int zUfiSms_InsertConcatSms(T_zUfiSms_DbStoreData*ptDbSaveData,
const char*pStorePos){T_zUfiSms_DbStoreStr ac={(0x549+2322-0xe5b)};int 
iSms_TotalCount=(0x4e5+683-0x790);int i=(0x192+8117-0x2147);char acTmpContent[
(0x64c+3143-0x1193)];int iConcatNum=(0x19dc+4-0x19e0);char*pFormatConcat=NULL;
char*pContent=NULL;int spaceLen=(0x15a8+2081-0x1dc5)*
ZTE_WMS_SMS_MSG_CONTENT_LEN_MAX*ZTE_WMS_CONCAT_SMS_COUNT_MAX+
(0x17cc+2238-0x2086);if(NULL==ptDbSaveData||NULL==pStorePos){return ZUFI_FAIL;}
printf(
"\x5b\x53\x4d\x53\x5d\x20\x65\x6e\x74\x65\x72\x20\x49\x6e\x73\x65\x72\x74\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x2e" "\n"
);memset(acTmpContent,(0x322+9-0x32b),sizeof(acTmpContent));iSms_TotalCount=
ptDbSaveData->concat_info[(0x9cb+5212-0x1e26)];for(i=(0x88c+6720-0x22cb);i<
iSms_TotalCount;i++){strcat(ac.IndStr,"\x3b");strcat(ac.Seg_Seq,"\x3b");strcat(
acTmpContent,"\x3b");}pFormatConcat=(char*)malloc(spaceLen);if(NULL==
pFormatConcat){return ZUFI_FAIL;}memset(pFormatConcat,(0x8b7+5421-0x1de4),
spaceLen);pContent=(char*)malloc(spaceLen);if(pContent==NULL){free(pFormatConcat
);return ZUFI_FAIL;}memset(pContent,(0xe3a+5421-0x2367),spaceLen);if(-
(0x1f52+1494-0x2527)==zUfiSms_AddNewSmsToConcatData(ptDbSaveData,acTmpContent,
pFormatConcat,pContent,&ac,&iConcatNum,spaceLen)){free(pFormatConcat);free(
pContent);pFormatConcat=NULL;pContent=NULL;return ZUFI_FAIL;}ptDbSaveData->
tp_dcs=(0x642+1767-0xd27);if(ZUFI_FAIL==zUfiSms_InsertConcatSmsToDb(ptDbSaveData
,pStorePos,pFormatConcat,pContent,&ac,iConcatNum)){free(pFormatConcat);free(
pContent);pFormatConcat=NULL;pContent=NULL;return ZUFI_FAIL;}free(pFormatConcat)
;free(pContent);pFormatConcat=NULL;pContent=NULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x49\x6e\x73\x65\x72\x74\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x20\x73\x75\x63\x63\x65\x73\x73\x2e" "\n"
);return ZUFI_SUCC;}int zUfiSms_StoreConcatSmsToDb(T_zUfiSms_DbStoreData*
ptDbSaveData,char*pMemStore){long iSmsId=(0x20a8+1395-0x261b);int total_count=
(0x125a+1150-0x16d8);if(NULL==ptDbSaveData||NULL==pMemStore){return ZUFI_FAIL;}
iSmsId=zUfiSms_SearchConcatSmsInDb(ptDbSaveData,pMemStore);if(-
(0x158d+3092-0x21a0)!=iSmsId){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x74\x6f\x72\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x20\x45\x6e\x74\x65\x72\x20\x55\x70\x64\x61\x74\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x20\x53\x6d\x73\x49\x64\x3a\x25\x64\x2e" "\n"
,iSmsId);return zUfiSms_UpdateConcatSms(ptDbSaveData,pMemStore,iSmsId);}else{if(
ZUFI_FAIL==zUfiSms_GetTotalCount(pMemStore,&total_count)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x74\x6f\x72\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x20\x47\x65\x74\x54\x6f\x74\x61\x6c\x43\x6f\x75\x6e\x74\x20\x46\x61\x69\x6c\x65\x64\x2e" "\n"
);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x74\x6f\x72\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x20\x70\x4d\x65\x6d\x53\x74\x6f\x72\x65\x3d\x25\x73\x2c\x74\x6f\x74\x61\x6c\x5f\x63\x6f\x75\x6e\x74\x3d\x25\x64\x2e" "\n"
,pMemStore,total_count);if(total_count>=g_zUfiSms_StoreCapablity[(strcmp(
pMemStore,"\x6e\x76")?ZTE_WMS_MEMORY_SIM:ZTE_WMS_MEMORY_NV)]){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x74\x6f\x72\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x54\x6f\x44\x62\x20\x53\x6d\x73\x20\x6d\x65\x6d\x6f\x72\x79\x20\x69\x73\x20\x46\x75\x6c\x6c\x2e" "\n"
);return ZUFI_FAIL;}return zUfiSms_InsertConcatSms(ptDbSaveData,pMemStore);}}int
 zUfiSms_WriteSmsToDb(T_zUfiSms_DbStoreData*ptDbSaveData,zUfiSms_StoreType 
iMemStore,long iSmsId){char acDbMemStore[(0x1075+2290-0x195d)];int iTotalCount=
(0x1de5+223-0x1ec4);int id=(0x190f+2797-0x23fc);UINT8 needCheckMemory=
(0x4d2+5175-0x1908);if(NULL==ptDbSaveData){return ZUFI_FAIL;}memset(acDbMemStore
,(0xd9b+340-0xeef),sizeof(acDbMemStore));if(WMS_STORAGE_TYPE_UIM_V01==iMemStore)
{strncpy(acDbMemStore,ZTE_WMS_DB_SIM_TABLE,sizeof(acDbMemStore)-
(0x24b+4356-0x134e));}else{strncpy(acDbMemStore,ZTE_WMS_DB_NV_TABLE,sizeof(
acDbMemStore)-(0x197c+2633-0x23c4));}if((0x72b+2181-0xfaf)==ptDbSaveData->
concat_sms){id=zUfiSms_SearchConcatSmsInDb(ptDbSaveData,&acDbMemStore);if(-
(0x7ff+3164-0x145a)!=id){needCheckMemory=(0x70+9021-0x23ad);}}if(needCheckMemory
==(0x35d+3864-0x1274)){if(ZUFI_FAIL==zUfiSms_GetTotalCount(acDbMemStore,&
iTotalCount)){return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x53\x6d\x73\x54\x6f\x44\x62\x20\x69\x54\x6f\x74\x61\x6c\x43\x6f\x75\x6e\x74\x3d\x25\x64\x28\x6e\x65\x65\x64\x43\x68\x65\x63\x6b\x4d\x65\x6d\x6f\x72\x79\x3d\x3d\x31\x29" "\n"
,iTotalCount);if(iTotalCount>=g_zUfiSms_StoreCapablity[(strcmp(acDbMemStore,
"\x6e\x76")?ZTE_WMS_MEMORY_SIM:ZTE_WMS_MEMORY_NV)]){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x53\x6d\x73\x54\x6f\x44\x62\x20\x6d\x65\x6d\x6f\x72\x79\x20\x66\x75\x6c\x6c\x2c\x20\x65\x72\x72\x6f\x72" "\n"
);return ZTE_WMS_NV_MEMORY_FULL;}}if(ptDbSaveData->concat_info[
(0x206+9043-0x2557)]==(0x405+312-0x53d)||ptDbSaveData->concat_info[
(0x11d1+1131-0x163a)]>ptDbSaveData->concat_info[(0x643+7942-0x2548)]){
ptDbSaveData->concat_sms=(0x96b+6609-0x233c);}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x57\x72\x69\x74\x65\x53\x6d\x73\x54\x6f\x44\x62\x20\x54\x6f\x74\x61\x6c\x43\x6f\x75\x6e\x74\x3a\x25\x64\x2e\x63\x6f\x6e\x63\x61\x74\x5f\x73\x6d\x73\x3a\x25\x64\x2e\x63\x6f\x6e\x63\x61\x74\x5f\x69\x6e\x66\x6f\x5b\x31\x5d\x3a\x25\x64" "\n"
,iTotalCount,ptDbSaveData->concat_sms,ptDbSaveData->concat_info[
(0x1120+467-0x12f2)]);if((0x687+7507-0x23d9)==ptDbSaveData->concat_sms){if(
ZTE_WMS_CONCAT_SMS_COUNT_MAX<ptDbSaveData->concat_info[(0xea0+2336-0x17bf)]){
return ZUFI_FAIL;}else{return zUfiSms_StoreConcatSmsToDb(ptDbSaveData,
acDbMemStore);}}else{return zUfiSms_StoreNormalSmsToDb(ptDbSaveData,acDbMemStore
,iSmsId);}}T_zUfiSms_CmdStatus zUfiSms_SendOutSms(T_zUfiSms_DbStoreData*
ptDbSaveData,int cid){int atRes=(0xb18+403-0xcab);if(NULL==ptDbSaveData){return 
WMS_CMD_FAILED;}if(!g_zUfiSms_IsConcatSendSuc){ptDbSaveData->tag=
WMS_TAG_TYPE_MO_NOT_SENT_V01;if(ZUFI_SUCC==zUfiSms_WriteSmsToDb(ptDbSaveData,
WMS_STORAGE_TYPE_NV_V01,-(0x18af+78-0x18fc))){g_zUfiSms_MsgRefer++;ptDbSaveData
->msg_ref=g_zUfiSms_MsgRefer;(void)zUfiSms_SetMaxReference(g_zUfiSms_MsgRefer);}
}else{CHAR sendfailRetry[(0x10cb+4763-0x2334)]={(0x20d6+518-0x22dc)};sc_cfg_get(
NV_SENDFAIL_RETRY,sendfailRetry,sizeof(sendfailRetry));if((0x25d+4915-0x1590)==
strcmp("\x31",sendfailRetry)){g_zUfiSms_SendFailedRetry=(0xf97+4988-0x2310);}
atRes=zSms_SendCmgsReq();if(atRes!=ZSMS_RESULT_OK){zSms_RecvCmgsErr();}else{
zSms_RecvCmgsOk();}}return WMS_CMD_SUCCESS;}int zUfiSms_SendConcatSms(int cid){
int atRes=(0x458+1101-0x8a5);T_zUfiSms_ClientMsg tClientMsg;
T_zUfiSms_DbStoreData tDbSaveData;int result=ZUFI_FAIL;if(g_zUfiSms_ConcatSms.
current_sending>=g_zUfiSms_ConcatSms.total_msg){return ZUFI_FAIL;}memset((void*)
&tClientMsg,(0x3b7+4975-0x1726),sizeof(T_zUfiSms_ClientMsg));memset((void*)&
tDbSaveData,(0x709+115-0x77c),sizeof(tDbSaveData));zUfiSms_SetPduData(&
tClientMsg,&tDbSaveData);if(!g_zUfiSms_IsConcatSendSuc){tDbSaveData.tag=
WMS_TAG_TYPE_MO_NOT_SENT_V01;(void)zUfiSms_WriteSmsToDb(&tDbSaveData,
WMS_STORAGE_TYPE_NV_V01,-(0x2d7+3304-0xfbe));g_zUfiSms_SendFailedCount++;}else{
CHAR sendfailRetry[(0x147b+2388-0x1d9d)]={(0x100b+135-0x1092)};sc_cfg_get(
NV_SENDFAIL_RETRY,sendfailRetry,sizeof(sendfailRetry));if((0x562+4360-0x166a)==
strcmp("\x31",sendfailRetry)){g_zUfiSms_SendFailedRetry=(0xc0+6116-0x18a1);}
atRes=zSms_SendCmgsReq();if(atRes!=ZSMS_RESULT_OK){zSms_RecvCmgsErr();}else{
zSms_RecvCmgsOk();}}g_zUfiSms_ConcatSms.current_sending++;if(g_zUfiSms_ConcatSms
.current_sending<g_zUfiSms_ConcatSms.total_msg){if(g_zUfiSms_ConcatSms.sms_len<
g_zUfiSms_UnitLen*(g_zUfiSms_ConcatSms.current_sending+(0xce9+3769-0x1ba1))){
g_zUfiSms_UnitLen=g_zUfiSms_ConcatSms.sms_len-g_zUfiSms_UnitLen*
g_zUfiSms_ConcatSms.current_sending;}}if(g_zUfiSms_ConcatSms.current_sending==
g_zUfiSms_ConcatSms.total_msg&&!g_zUfiSms_IsConcatSendSuc){T_zUfiSms_StatusInfo 
tSendStatus;memset((void*)&tSendStatus,(0x7fc+7055-0x238b),sizeof(
T_zUfiSms_StatusInfo));tSendStatus.err_code=ZTE_SMS_CMS_NONE;tSendStatus.
send_failed_count=g_zUfiSms_SendFailedCount;tSendStatus.delete_failed_count=
(0x8ef+5687-0x1f26);tSendStatus.cmd_status=WMS_CMD_FAILED;tSendStatus.cmd=
WMS_SMS_CMD_MSG_SEND;(void)zUfiSms_SetCmdStatus(&tSendStatus);sc_cfg_set(
NV_SMS_SEND_RESULT,"\x66\x61\x69\x6c");sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");
return ZUFI_FAIL;}if(!g_zUfiSms_IsConcatSendSuc){zUfiSms_SendConcatSms(cid);}
return result;}int zSvr_sendCmgs(VOID){int atRes=(0xf3+5450-0x163d);int i=
(0x33d+593-0x58e);atRes=zSms_SendCmgsReq();if(atRes!=ZSMS_RESULT_OK){CHAR 
sendfailRetry[(0x872+2009-0x1019)]={(0x940+280-0xa58)};sc_cfg_get(
NV_SENDFAIL_RETRY,sendfailRetry,sizeof(sendfailRetry));if((0x751+919-0xae8)==
strcmp("\x31",sendfailRetry)){for(i=(0x100+1060-0x524);i<SMS_RETRY_COUNT;i++){
atRes=zSms_SendCmgsReq();if(atRes==ZSMS_RESULT_OK){break;}}}}return atRes;}
T_zUfiSms_CmdStatus zUfiSms_SendSms(VOID){T_zUfiSms_ClientMsg tClientMsg;
T_zUfiSms_DbStoreData tDbSaveData;int res=(0x110d+3144-0x1d55);if(-
(0xf3d+2702-0x19ca)==g_zUfiSms_UnitLen){printf(
"\x5b\x53\x4d\x53\x5d\x20\x21\x21\x21\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x53\x6d\x73\x3a\x20\x4e\x6f\x20\x63\x6f\x6e\x74\x65\x6e\x74\x21\x2e" "\n"
);return WMS_CMD_FAILED;}while(g_zUfiSms_ConcatSms.current_sending<
g_zUfiSms_ConcatSms.total_msg){memset((void*)&tClientMsg,(0x11+8572-0x218d),
sizeof(T_zUfiSms_ClientMsg));memset((void*)&tDbSaveData,(0x18f7+2685-0x2374),
sizeof(tDbSaveData));zUfiSms_SetPduData(&tClientMsg,&tDbSaveData);if(!
g_zUfiSms_IsConcatSendSuc){tDbSaveData.tag=WMS_TAG_TYPE_MO_NOT_SENT_V01;(void)
zUfiSms_WriteSmsToDb(&tDbSaveData,WMS_STORAGE_TYPE_NV_V01,-(0x4f0+8547-0x2652));
g_zUfiSms_SendFailedCount++;}else{res=zSvr_sendCmgs();if(res!=ZSMS_RESULT_OK){
zSms_RecvCmgsErr();}else{zSms_RecvCmgsOk();}}g_zUfiSms_ConcatSms.current_sending
++;if(g_zUfiSms_ConcatSms.current_sending<g_zUfiSms_ConcatSms.total_msg){if(
g_zUfiSms_ConcatSms.sms_len<g_zUfiSms_UnitLen*(g_zUfiSms_ConcatSms.
current_sending+(0x7d3+3360-0x14f2))){g_zUfiSms_UnitLen=g_zUfiSms_ConcatSms.
sms_len-g_zUfiSms_UnitLen*g_zUfiSms_ConcatSms.current_sending;}}if(
g_zUfiSms_ConcatSms.current_sending==g_zUfiSms_ConcatSms.total_msg&&!
g_zUfiSms_IsConcatSendSuc){T_zUfiSms_StatusInfo tSendStatus;memset((void*)&
tSendStatus,(0xefb+1248-0x13db),sizeof(T_zUfiSms_StatusInfo));tSendStatus.
err_code=ZTE_SMS_CMS_NONE;tSendStatus.send_failed_count=
g_zUfiSms_SendFailedCount;tSendStatus.delete_failed_count=(0x63c+8091-0x25d7);
tSendStatus.cmd_status=WMS_CMD_FAILED;tSendStatus.cmd=WMS_SMS_CMD_MSG_SEND;(void
)zUfiSms_SetCmdStatus(&tSendStatus);sc_cfg_set(NV_SMS_SEND_RESULT,
"\x66\x61\x69\x6c");sc_cfg_set(NV_SMS_DB_CHANGE,"\x31");}}
#if (0x3ca+2428-0xd46)	
if(g_zUfiSms_ConcatSms.total_msg>(0x723+1587-0xd55)){printf(
"\x3d\x3d\x3d\x3d\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x2e" "\n"
);zUfiSms_SendConcatSms(cid);}else{memset((void*)&tClientMsg,(0x46+6455-0x197d),
sizeof(T_zUfiSms_ClientMsg));memset((void*)&tDbSaveData,(0x8b4+1271-0xdab),
sizeof(tDbSaveData));zUfiSms_SetPduData(&tClientMsg,&tDbSaveData);printf(
"\x3d\x3d\x3d\x3d\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x6e\x6f\x72\x61\x6d\x6c\x53\x6d\x73\x2e" "\n"
);if(WMS_CMD_FAILED==zUfiSms_SendOutSms(&tDbSaveData,cid)){printf(
"\x21\x21\x21\x21\x21\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x4f\x75\x74\x53\x6d\x73\x20\x46\x61\x69\x6c\x2e" "\n"
);g_zUfiSms_SendFailedCount++;return WMS_CMD_FAILED;}}
#endif
return WMS_CMD_SUCCESS;}int zUfiSms_FormatDeliverTimestamp(T_zUfiSms_Date tData,
T_zUfiSms_TimeStamp*ptTimestamp){unsigned char tTimeZone[(0x210+7930-0x2105)]={
(0x7fd+4815-0x1acc)};int tmp_i=(0x487+2625-0xec8);memset(ptTimestamp,
(0x67c+5287-0x1b23),sizeof(T_zUfiSms_TimeStamp));if(strlen(tData.year)==
(0x2b8+3185-0xf28)){ptTimestamp->year=zUfiSms_atohex(
((char)(0x1468+2394-0x1d92)))*(0x5af+3921-0x14f0)+zUfiSms_atohex(tData.year[
(0x22e2+47-0x2311)]);}else if(strlen(tData.year)==(0x34c+5454-0x1898)){
ptTimestamp->year=zUfiSms_atohex(tData.year[(0x18f7+1346-0x1e39)])*
(0x1f8a+1849-0x26b3)+zUfiSms_atohex(tData.year[(0x13f0+4685-0x263c)]);}else if(
strlen(tData.year)==(0x106+4813-0x13cf)){ptTimestamp->year=zUfiSms_atohex(tData.
year[(0x11a8+5142-0x25bc)])*(0x4c4+2390-0xe0a)+zUfiSms_atohex(tData.year[
(0x1caf+2037-0x24a1)]);}else{printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x79\x65\x61\x72\x2e");return ZUFI_FAIL;}if(
strlen(tData.month)==(0x606+5698-0x1c47)){ptTimestamp->month=zUfiSms_atohex(
((char)(0xe12+2086-0x1608)))*(0x1440+1848-0x1b68)+zUfiSms_atohex(tData.month[
(0x1140+950-0x14f6)]);}else if(strlen(tData.month)==(0x184a+1994-0x2012)){
ptTimestamp->month=zUfiSms_atohex(tData.month[(0x1f70+1437-0x250d)])*
(0x9a1+3570-0x1783)+zUfiSms_atohex(tData.month[(0xb87+3584-0x1986)]);}else{
printf("\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x64\x61\x79\x2e");}if(strlen(tData.day)
==(0xa92+271-0xba0)){ptTimestamp->day=zUfiSms_atohex(
((char)(0x1b2b+1520-0x20eb)))*(0x8b+5561-0x1634)+zUfiSms_atohex(tData.day[
(0x15bf+485-0x17a4)]);}else if(strlen(tData.day)==(0x4b6+7372-0x2180)){
ptTimestamp->day=zUfiSms_atohex(tData.day[(0xf2f+810-0x1259)])*
(0x1f80+774-0x2276)+zUfiSms_atohex(tData.day[(0x12a5+1477-0x1869)]);}else{printf
("\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x64\x61\x79\x2e");}if(strlen(tData.hour)==
(0xe1b+3648-0x1c5a)){ptTimestamp->hour=zUfiSms_atohex(
((char)(0x81b+4150-0x1821)))*(0x1860+610-0x1ab2)+zUfiSms_atohex(tData.hour[
(0x8e6+7344-0x2596)]);}else if(strlen(tData.hour)==(0x9f9+6674-0x2409)){
ptTimestamp->hour=zUfiSms_atohex(tData.hour[(0x7b1+6450-0x20e3)])*
(0xee5+2281-0x17be)+zUfiSms_atohex(tData.hour[(0x5cf+7329-0x226f)]);}else{printf
("\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x68\x6f\x75\x72\x2e");}if(strlen(tData.min)==
(0x14ad+3322-0x21a6)){ptTimestamp->minute=zUfiSms_atohex(
((char)(0x825+6581-0x21aa)))*(0xdd5+716-0x1091)+zUfiSms_atohex(tData.min[
(0x246+7764-0x209a)]);}else if(strlen(tData.min)==(0xa2+5233-0x1511)){
ptTimestamp->minute=zUfiSms_atohex(tData.min[(0x8d4+602-0xb2e)])*
(0x10f+5918-0x181d)+zUfiSms_atohex(tData.min[(0x46f+783-0x77d)]);}else{printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x6d\x69\x6e\x75\x74\x65\x2e");}if(strlen(tData
.sec)==(0x201a+712-0x22e1)){ptTimestamp->second=zUfiSms_atohex(
((char)(0xea+8676-0x229e)))*(0x445+2296-0xd2d)+zUfiSms_atohex(tData.sec[
(0xbf8+2485-0x15ad)]);}else if(strlen(tData.sec)==(0x22c+4373-0x133f)){
ptTimestamp->second=zUfiSms_atohex(tData.sec[(0xa95+608-0xcf5)])*
(0xf47+5266-0x23c9)+zUfiSms_atohex(tData.sec[(0x20d9+218-0x21b2)]);}else{printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x73\x65\x63\x6f\x6e\x64\x2e");}tmp_i=atoi(
tData.timezone);if(tmp_i<INT_MIN+(0x52+6588-0x1a0d)||tmp_i>INT_MAX-
(0xa98+868-0xdfb)){printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x74\x44\x61\x74\x61\x20\x74\x69\x6d\x65\x7a\x6f\x6e\x65\x3a\x25\x64\x2e" "\n"
,tmp_i);return ZUFI_FAIL;}memset(tTimeZone,(0x143+2692-0xbc7),sizeof(tTimeZone))
;snprintf(tTimeZone,sizeof(tTimeZone),"\x25\x64",tmp_i*(0x18f+4035-0x114e));if(
tData.timezone[(0x18d5+1298-0x1de7)]==((char)(0x1864+3283-0x250a))){if(strlen(
tTimeZone)==(0x159+9186-0x2539)){ptTimestamp->timezone=zUfiSms_atohex(
((char)(0x12ff+4201-0x2338)))*(0x1631+2684-0x209d)+zUfiSms_atohex(tTimeZone[
(0xb3c+4280-0x1bf3)]);}else if(strlen(tTimeZone)==(0x93+5555-0x1643)){
ptTimestamp->timezone=zUfiSms_atohex(tTimeZone[(0x8b7+2073-0x10cf)])*
(0xb38+2688-0x15ae)+zUfiSms_atohex(tTimeZone[(0x8d8+1078-0xd0c)]);}else{printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x2d\x74\x69\x6d\x65\x7a\x6f\x6e\x65\x2e");}
ptTimestamp->timezone=(0x102d+1027-0x1430)-ptTimestamp->timezone;}else{if(strlen
(tTimeZone)==(0x1adf+1914-0x2258)){ptTimestamp->timezone=zUfiSms_atohex(
((char)(0xe62+1648-0x14a2)))*(0x68a+8256-0x26ba)+zUfiSms_atohex(tTimeZone[
(0x13a+4554-0x1304)]);}else if(strlen(tTimeZone)==(0xce3+897-0x1062)){
ptTimestamp->timezone=zUfiSms_atohex(tTimeZone[(0x1d3+1769-0x8bc)])*
(0xc10+1775-0x12f5)+zUfiSms_atohex(tTimeZone[(0x19a3+2549-0x2397)]);}else{printf
("\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x2b\x74\x69\x6d\x65\x7a\x6f\x6e\x65\x2e");}}
return ZUFI_SUCC;}void zUfiSms_FillDeliver(T_zUfiSms_DeliverPdu*deliver,
T_zUfiSms_ConcatInfo*concat_sms,T_zUfiSms_DbStoreData*ptDbSaveData){static 
UINT16 msg_ref=(0x1608+1461-0x1bbd);deliver->user_data_header_present=TRUE;if(
(0x1492+2254-0x1d60)==concat_sms->current_sending){g_zUfiSms_ConcatSmsReference
++;(void)zUfiSms_SetConcatMaxRefer(g_zUfiSms_ConcatSmsReference);}deliver->
user_data_header_present=TRUE;deliver->user_data.num_headers=(0x35+7852-0x1ee0);
deliver->user_data.headers[(0x4c3+6409-0x1dcc)].header_id=WMS_UDH_CONCAT_8;
deliver->user_data.headers[(0x1374+1277-0x1871)].u.concat_8.msg_ref=msg_ref;
deliver->user_data.headers[(0x177+7563-0x1f02)].u.concat_8.total_sm=concat_sms->
total_msg;deliver->user_data.headers[(0x1bd5+2671-0x2644)].u.concat_8.seq_num=
concat_sms->current_sending+(0xe2f+6194-0x2660);ptDbSaveData->concat_sms=
(0x724+615-0x98a);ptDbSaveData->concat_info[(0x26f+508-0x46b)]=msg_ref;}void 
zUfiSms_FillDeliverPdu(T_zUfiSms_DeliverPdu*ptDeliver,T_zUfiSms_ConcatInfo*
concat_sms,T_zUfiSms_GroupInfo*group_sms,int iSmsLen,T_zUfiSms_DbStoreData*
ptDbSaveData){if(NULL==concat_sms||NULL==group_sms){return;}ptDeliver->more=
FALSE;ptDeliver->reply_path_present=FALSE;ptDeliver->status_report_enabled=
g_zUfiSms_CurSmsPara.status_report_on;ptDeliver->pid=WMS_PID_DEFAULT;ptDeliver->
dcs.msg_class=WMS_MESSAGE_CLASS_NONE;ptDeliver->dcs.is_compressed=FALSE;if(
g_zUfiSms_Dcs==DCS_ASC){ptDeliver->dcs.alphabet=WMS_GW_ALPHABET_7_BIT_DEFAULT;}
else{ptDeliver->dcs.alphabet=WMS_GW_ALPHABET_UCS2;}if(concat_sms->total_msg>
(0x22bb+384-0x243a)){zUfiSms_FillDeliver(ptDeliver,concat_sms,ptDbSaveData);
ptDbSaveData->concat_sms=(0xcd2+2306-0x15d3);ptDbSaveData->concat_info[
(0x1af8+689-0x1da7)]=concat_sms->current_sending+(0x683+666-0x91c);ptDbSaveData
->concat_info[(0x3c7+1972-0xb7a)]=concat_sms->total_msg;ptDbSaveData->
concat_info[(0xb51+5432-0x2089)]=ptDeliver->user_data.headers[
(0x44f+3040-0x102f)].u.concat_8.msg_ref;}else{ptDeliver->
user_data_header_present=FALSE;ptDeliver->user_data.num_headers=
(0x105b+5782-0x26f1);}ptDeliver->user_data.sm_len=iSmsLen;memcpy(ptDeliver->
user_data.sm_data,concat_sms->msg_contents[concat_sms->current_sending],iSmsLen)
;if(group_sms->receivers[group_sms->current_receiver][(0xb2c+5719-0x2183)]==
((char)(0xbc0+5348-0x2079))){(void)zUfiSms_CharToInt(group_sms->receivers[
group_sms->current_receiver]+(0x884+5883-0x1f7e),strlen(group_sms->receivers[
group_sms->current_receiver])-(0x47+5246-0x14c4),ptDeliver->address.digits);
ptDeliver->address.number_type=WMS_NUMBER_INTERNATIONAL;ptDeliver->address.
number_of_digits=(UINT8)strlen(group_sms->receivers[group_sms->current_receiver]
)-(0x107+4958-0x1464);}else{(void)zUfiSms_CharToInt(group_sms->receivers[
group_sms->current_receiver],strlen(group_sms->receivers[group_sms->
current_receiver]),ptDeliver->address.digits);ptDeliver->address.number_type=
WMS_NUMBER_UNKNOWN;ptDeliver->address.number_of_digits=(UINT8)strlen(group_sms->
receivers[group_sms->current_receiver]);}ptDeliver->address.digit_mode=
WMS_DIGIT_MODE_4_BIT;ptDeliver->address.number_mode=
WMS_NUMBER_MODE_NONE_DATA_NETWORK;ptDeliver->address.number_plan=
WMS_NUMBER_PLAN_TELEPHONY;}T_zUfiSms_CmdStatus zUfiSms_SaveConcatSms(
T_zUfiSms_SaveReq*ptSaveSms,T_zUfiSms_ConcatInfo*ptConcatSms,T_zUfiSms_GroupInfo
*ptGroupSms,T_zUfiSms_DbStoreData*ptDbSaveData,int iSmsLen){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x61\x76\x65\x43\x6f\x6e\x63\x61\x74\x53\x6d\x73\x20\x6d\x65\x6d\x5f\x73\x74\x6f\x72\x65\x3d\x25\x64\x28\x4e\x56\x3d\x3d\x30\x31\x29" "\n"
,ptSaveSms->mem_store);if(ptSaveSms->mem_store==WMS_STORAGE_TYPE_NV_V01){if(
ZUFI_FAIL==zUfiSms_WriteSmsToDb(ptDbSaveData,WMS_STORAGE_TYPE_NV_V01,-
(0x10d3+2128-0x1922))){at_print(LOG_ERR,
"\x77\x72\x69\x74\x65\x20\x73\x6d\x73\x20\x74\x6f\x20\x6e\x76\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return WMS_CMD_FAILED;}}else{return WMS_CMD_FAILED;}g_zUfiSms_MsgRefer++;
ptDbSaveData->msg_ref=g_zUfiSms_MsgRefer;(void)zUfiSms_SetMaxReference(
g_zUfiSms_MsgRefer);return WMS_CMD_SUCCESS;}T_zUfiSms_CmdStatus 
zUfiSms_SaveNormalSms(T_zUfiSms_SaveReq*ptSaveSms,T_zUfiSms_DbStoreData*
ptDbSaveData){g_zUfiSms_MsgRefer++;ptDbSaveData->msg_ref=g_zUfiSms_MsgRefer;if(
ptSaveSms->mem_store==WMS_STORAGE_TYPE_NV_V01){if(ZUFI_SUCC==
zUfiSms_WriteSmsToDb(ptDbSaveData,WMS_STORAGE_TYPE_NV_V01,ptSaveSms->id)){(void)
zUfiSms_SetMaxReference(g_zUfiSms_MsgRefer);return WMS_CMD_SUCCESS;}else{printf(
"\x77\x72\x69\x74\x65\x20\x73\x6d\x73\x20\x74\x6f\x20\x6e\x76\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return WMS_CMD_FAILED;}}return WMS_CMD_SUCCESS;}T_zUfiSms_CmdStatus 
zUfiSms_SaveSmsToDb(T_zUfiSms_SaveReq*ptSaveSms,T_zUfiSms_ConcatInfo*ptConcatSms
,T_zUfiSms_GroupInfo*ptGroupSms,int iSmsLength){T_zUfiSms_ClientTsData 
tClientTsData;T_zUfiSms_ClientMsg tClientMsg;T_zUfiSms_DbStoreData tDbSaveData;
T_zUfiSms_CmdStatus result=WMS_CMD_SUCCESS;int current_sending=
(0x1297+3902-0x21d5);if(NULL==ptSaveSms||NULL==ptConcatSms||NULL==ptGroupSms||(-
(0x1244+4171-0x228e)==iSmsLength)){return WMS_CMD_FAILED;}for(ptConcatSms->
current_sending=(0xd44+3211-0x19cf);ptConcatSms->current_sending<ptConcatSms->
total_msg;ptConcatSms->current_sending++){memset((void*)&tClientMsg,
(0x8e0+5488-0x1e50),sizeof(T_zUfiSms_ClientMsg));memset((void*)&tClientTsData,
(0x1dd1+437-0x1f86),sizeof(T_zUfiSms_ClientTsData));memset((void*)&tDbSaveData,
(0x2268+60-0x22a4),sizeof(T_zUfiSms_DbStoreData));tClientMsg.msg_hdr.mem_store=(
ptSaveSms->mem_store==WMS_STORAGE_TYPE_UIM_V01)?WMS_STORAGE_TYPE_UIM_V01:
WMS_STORAGE_TYPE_NV_V01;tClientMsg.msg_hdr.tag=(T_zUfiSms_SmsTag)ptSaveSms->tags
;tClientMsg.msg_hdr.message_mode=WMS_MESSAGE_MODE_GW;tClientMsg.u.gw_message.
is_broadcast=FALSE;tClientTsData.format=WMS_FORMAT_GW_PP;switch(ptSaveSms->tags)
{case WMS_TAG_TYPE_MO_SENT_V01:case WMS_TAG_TYPE_MO_NOT_SENT_V01:case
(0xd25+410-0xebb):{tClientTsData.u.gw_pp.tpdu_type=WMS_TPDU_SUBMIT;(void)
zUfiSms_FillSubmitTpdu(ptConcatSms,ptGroupSms,iSmsLength,&tClientTsData.u.gw_pp.
u.submit,&tDbSaveData);break;}case WMS_TAG_TYPE_MT_READ_V01:case 
WMS_TAG_TYPE_MT_NOT_READ_V01:{tClientTsData.u.gw_pp.tpdu_type=WMS_TPDU_DELIVER;(
void)zUfiSms_FormatDeliverTimestamp(ptSaveSms->date,&(tClientTsData.u.gw_pp.u.
deliver.timestamp));zUfiSms_FillDeliverPdu(&tClientTsData.u.gw_pp.u.deliver,
ptConcatSms,ptGroupSms,iSmsLength,&tDbSaveData);break;}default:{printf(
"\x75\x6e\x6b\x6e\x6f\x77\x6e\x20\x74\x61\x67\x73\x2e");break;}}(void)
wms_ts_encode(&tClientTsData,&tClientMsg.u.gw_message.raw_ts_data);
zUfiSms_FillSca(&tClientMsg);zUfiSms_FillDbSaveData(&tClientMsg,&tClientTsData,
ptConcatSms,ptGroupSms,iSmsLength,&tDbSaveData);strncpy(tDbSaveData.
draft_group_id,ptSaveSms->draft_group_id,sizeof(tDbSaveData.draft_group_id)-
(0x6bf+1430-0xc54));if(TRUE==g_zUfiSms_MemFullFlag[ZTE_WMS_MEMORY_NV]){result=
WMS_CMD_FAILED;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x61\x76\x65\x53\x6d\x73\x54\x6f\x44\x62\x20\x4e\x56\x20\x6d\x65\x6d\x6f\x72\x79\x20\x69\x73\x20\x66\x75\x6c\x6c\x2c\x73\x61\x76\x65\x20\x65\x72\x72\x6f\x72" "\n"
);}else{if(tDbSaveData.concat_sms==(0x236+1752-0x90d)){result=
zUfiSms_SaveConcatSms(ptSaveSms,ptConcatSms,ptGroupSms,&tDbSaveData,iSmsLength);
current_sending=ptConcatSms->current_sending+(0x323+128-0x3a2);if(ptConcatSms->
sms_len<iSmsLength*(current_sending+(0x1bed+1110-0x2042))){iSmsLength=
ptConcatSms->sms_len-iSmsLength*current_sending;}}else{result=
zUfiSms_SaveNormalSms(ptSaveSms,&tDbSaveData);}}}return result;}int 
zUfiSms_DeleteSmsInSim(){char str_index[(0x132f+630-0x1525)]={(0x420+1842-0xb52)
};int index=(0xc61+38-0xc87);int is_cc=(0xc4+8023-0x201b);int iSmsId=
(0xba9+699-0xe64);T_zUfiSms_ModifyTag tDeleteInfo={(0x19d5+813-0x1d02)};char 
StrValue[(0x3c9+2791-0xea6)]={(0x11f+2167-0x996)};memset(&tDeleteInfo,
(0xfbc+3456-0x1d3c),sizeof(T_zUfiSms_ModifyTag));iSmsId=g_zUfiSms_DelMsg.sim_id[
g_zUfiSms_DelMsg.sim_index];if(-(0xfc8+4284-0x2083)==(is_cc=zUfiSms_IsConcatSms(
iSmsId))){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x6d\x73\x49\x6e\x53\x69\x6d\x20\x63\x68\x65\x63\x6b\x20\x63\x6f\x6e\x63\x61\x74\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x6d\x73\x49\x6e\x53\x69\x6d\x20\x69\x73\x5f\x63\x63\x3a\x25\x64\x2c\x20\x69\x64\x3d\x25\x64" "\n"
,is_cc,iSmsId);if((0x63a+887-0x9b0)==is_cc){if(ZUFI_FAIL==zUfiSms_GetSmsIndex(
iSmsId,&tDeleteInfo,is_cc)){return ZUFI_FAIL;}g_zUfiSms_DelMsg.sim_index++;
g_zUfiSms_DelMsg.sim_index_count--;while(tDeleteInfo.num_of_indices>
(0xd58+6109-0x2535)){index=tDeleteInfo.indices[tDeleteInfo.id_index];
g_deleteIndex.index[g_deleteIndex.total]=index;g_deleteIndex.total++;tDeleteInfo
.id_index++;tDeleteInfo.num_of_indices--;}}else{memset(str_index,
(0x2d+9577-0x2596),sizeof(str_index));if(ZUFI_FAIL==zUfiSms_GetStorePosById(
"\x69\x6e\x64",str_index,sizeof(str_index),iSmsId)){at_print(LOG_ERR,
"\x67\x65\x74\x20\x69\x6e\x64\x65\x78\x20\x66\x72\x6f\x6d\x20\x64\x62\x20\x66\x61\x69\x6c\x64\x2e"
);return ZUFI_FAIL;}index=atoi(str_index);g_deleteIndex.index[g_deleteIndex.
total]=index;g_deleteIndex.total++;g_zUfiSms_DelMsg.sim_index++;g_zUfiSms_DelMsg
.sim_index_count--;}(void)zUfiSms_DeleteSmsInDb();return ZUFI_SUCC;}
T_zUfiSms_CmdStatus zUfiSms_DeleteSimSms(VOID){int atRes=(0x1a23+1349-0x1f68);
char StrValue[(0x1ee5+1878-0x2631)]={(0x45c+8488-0x2584)};zUfiSms_SetSmsLocation
(SMS_LOCATION_SIM);memset(&g_deleteIndex,(0x1f5f+904-0x22e7),sizeof(
T_zUfiSms_DelIndexInfo));while(g_zUfiSms_DelMsg.sim_index_count>
(0x1689+2728-0x2131)){if(ZUFI_FAIL==zUfiSms_DeleteSmsInSim()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x69\x6d\x53\x6d\x73\x20\x64\x65\x6c\x65\x74\x65\x20\x61\x6c\x6c\x3a\x25\x64\x20\x73\x6d\x73\x20\x66\x61\x69\x6c\x65\x64" "\n"
,WMS_STORAGE_TYPE_UIM_V01);return WMS_CMD_FAILED;}}while(g_deleteIndex.cur_index
<g_deleteIndex.total){atRes=zSms_SendCmgdReq(g_deleteIndex.index[g_deleteIndex.
cur_index]);if(atRes==ZSMS_RESULT_OK){zSms_RecvCmgdOk();}else{zSms_RecvCmgdErr()
;}g_deleteIndex.cur_index++;}zSms_RecvCmgdFinish();printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x69\x6d\x53\x6d\x73\x20\x73\x75\x63\x63\x65\x73\x73" "\n"
);return WMS_CMD_SUCCESS;}void zUfiSms_GetReportStatus(char*pdu_tmp,int*stat){
unsigned char tmp;unsigned char first_octet;if(pdu_tmp==NULL){return;}(void)
String2Bytes(pdu_tmp,&tmp,(0x73c+956-0xaf6));if(tmp==(0x1f09+1849-0x2642)){
pdu_tmp+=(0xe04+1532-0x13fe);}else{tmp=(tmp+(0x49b+6757-0x1eff))*
(0x5d9+2479-0xf86);pdu_tmp+=tmp;}(void)String2Bytes(pdu_tmp,&tmp,
(0x2549+11-0x2552));first_octet=tmp;if(first_octet&(0x1df6+2207-0x2693)){*stat=
(0x54+1967-0x7fe);}}T_zUfiSms_TpduType zUfiSms_GetTpduType(UINT8*pData){
T_zUfiSms_TpduType iTpduType;UINT8 mti=(0xc0+3519-0xe7f);mti=(T_zUfiSms_TpduType
)(pData[(0xbb7+3658-0x1a01)]&(0xd35+3297-0x1a13));switch(mti){case
(0x9c6+3411-0x1719):iTpduType=WMS_TPDU_DELIVER;break;case(0x152+5226-0x15bb):
iTpduType=WMS_TPDU_SUBMIT;break;case(0xd80+3333-0x1a83):iTpduType=
WMS_TPDU_STATUS_REPORT;break;default:iTpduType=WMS_TPDU_MAX;break;}return 
iTpduType;}static void zUfiSms_FormatDeliverNumber(wms_address_s_type tAddress,
unsigned char*pNumber){UINT8 number_type=(0x16db+3183-0x234a);memset(pNumber,
(0x16d7+2718-0x2175),ZTE_WMS_ADDRESS_LEN_MAX+(0x14d+8460-0x2258));if(tAddress.
number_type==WMS_NUMBER_INTERNATIONAL){pNumber[(0x1a4d+1400-0x1fc5)]=
((char)(0x2f9+350-0x42c));pNumber++;}if(tAddress.digit_mode!=
WMS_DIGIT_MODE_8_BIT){(void)zUfiSms_SmsiAddrToStr(tAddress,(byte*)pNumber,&
number_type);}else{memcpy(pNumber,tAddress.digits,tAddress.number_of_digits*
sizeof(tAddress.digits[(0x9fc+2469-0x13a1)]));}}byte*zUfiSms_UtilTimeStamp(
T_zUfiSms_TimeStamp zte_wms_time,byte*res_ptr,T_zUfiSms_Date*date){UINT8 tmp;if(
NULL==date){return NULL;}*res_ptr++=((char)(0x164d+1190-0x1ad1));tmp=
zte_wms_time.year;res_ptr=zUfiSms_SmsiUtilitoaFill((tmp&(0x15a2+1858-0x1cd5))+((
tmp>>(0x1729+3362-0x2447))*(0x5cb+2030-0xdaf)),res_ptr);zUfiSms_SprintfTime(date
->year,sizeof(date->year),zte_wms_time.year);*res_ptr++=
((char)(0xf8d+4641-0x217f));tmp=zte_wms_time.month;res_ptr=
zUfiSms_SmsiUtilitoaFill((tmp&(0x95b+4306-0x1a1e))+((tmp>>(0x176+9211-0x256d))*
(0x9e4+4846-0x1cc8)),res_ptr);zUfiSms_SprintfTime(date->month,sizeof(date->month
),zte_wms_time.month);*res_ptr++=((char)(0x14b6+251-0x1582));tmp=zte_wms_time.
day;res_ptr=zUfiSms_SmsiUtilitoaFill((tmp&(0xdb0+1592-0x13d9))+((tmp>>
(0x1703+3954-0x2671))*(0x13e9+621-0x164c)),res_ptr);zUfiSms_SprintfTime(date->
day,sizeof(date->day),zte_wms_time.day);*res_ptr++=((char)(0xfe1+277-0x10ca));
tmp=zte_wms_time.hour;res_ptr=zUfiSms_SmsiUtilitoaFill((tmp&(0x5fc+6208-0x1e2d))
+((tmp>>(0x40f+2023-0xbf2))*(0x192a+2500-0x22e4)),res_ptr);zUfiSms_SprintfTime(
date->hour,sizeof(date->hour),zte_wms_time.hour);*res_ptr++=
((char)(0x8c2+578-0xaca));tmp=zte_wms_time.minute;res_ptr=
zUfiSms_SmsiUtilitoaFill((tmp&(0x5b8+3919-0x14f8))+((tmp>>(0xdda+33-0xdf7))*
(0x6e9+3937-0x1640)),res_ptr);zUfiSms_SprintfTime(date->min,sizeof(date->min),
zte_wms_time.minute);*res_ptr++=((char)(0x9d0+4183-0x19ed));tmp=zte_wms_time.
second;res_ptr=zUfiSms_SmsiUtilitoaFill((tmp&(0x1826+457-0x19e0))+((tmp>>
(0x2014+922-0x23aa))*(0x18c+9534-0x26c0)),res_ptr);zUfiSms_SprintfTime(date->sec
,sizeof(date->sec),zte_wms_time.second);if(zte_wms_time.timezone<
(0x1929+1458-0x1edb)){*res_ptr++=((char)(0x1de1+727-0x208b));tmp=(UINT8)(
zte_wms_time.timezone*(-(0x23c+3614-0x1059)));snprintf(date->timezone,sizeof(
date->timezone),"\x2d\x25\x64",-(0x1bb7+2320-0x24c6)*zte_wms_time.timezone);}
else{*res_ptr++=((char)(0x17c5+1917-0x1f17));tmp=(UINT8)zte_wms_time.timezone;
snprintf(date->timezone,sizeof(date->timezone),"\x2b\x25\x64",zte_wms_time.
timezone);}res_ptr=zUfiSms_SmsiUtilitoaFill(tmp,res_ptr);*res_ptr++=
((char)(0xc21+3564-0x19eb));return res_ptr;}T_zUfiSms_CmdStatus 
zUfiSms_HandleReport(unsigned char*ptPduData){T_zUfiSms_RawTsData tRawTsData;
T_zUfiSms_ClientTsData tClientTsData;int iReportStatus=(0x4a9+5117-0x18a6);
unsigned char acDeliverNumber[ZTE_WMS_ADDRESS_LEN_MAX+(0x14d5+1242-0x19ae)];
unsigned char tTpScts[ZTE_WMS_TP_SCTS_LEN_MAX+(0x3b9+3131-0xff3)];T_zUfiSms_Date
 tSmsDate;char acRecFlag[(0xb94+5141-0x1fa4)]={(0x1565+2384-0x1eb5)};int 
iRpCount=(0x13c9+4830-0x26a7);char tmp[(0x8a8+7482-0x25d8)];int tmp_i=
(0x606+1665-0xc87);unsigned int pos=(0x1b3a+951-0x1ef1);if(NULL==ptPduData){
return WMS_CMD_FAILED;}memset(acDeliverNumber,(0x103a+1137-0x14ab),sizeof(
acDeliverNumber));memset(&tSmsDate,(0xf43+3792-0x1e13),sizeof(T_zUfiSms_Date));
memset(tTpScts,(0x100d+5238-0x2483),sizeof(tTpScts));memset(&tRawTsData,
(0x941+4694-0x1b97),sizeof(T_zUfiSms_RawTsData));memset(&tClientTsData,
(0x16a6+973-0x1a73),sizeof(T_zUfiSms_ClientTsData));snprintf(tmp,sizeof(tmp),
"\x25\x58",ptPduData[(0x5b6+7529-0x231f)]);tmp_i=atoi(tmp);if(tmp_i<
(0xc68+3499-0x1a13)||tmp_i>INT_MAX-(0xea3+4536-0x205a)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x70\x74\x50\x64\x75\x44\x61\x74\x61\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return WMS_CMD_FAILED;}pos=tmp_i+(0x143+7663-0x1f31);if(pos>=
ZSMS_PDU_SIZE){return WMS_CMD_FAILED;}memcpy((void*)tRawTsData.data,(void*)(
ptPduData+pos),sizeof(tRawTsData.data));tRawTsData.tpdu_type=zUfiSms_GetTpduType
(ptPduData+pos);tRawTsData.format=WMS_FORMAT_GW_PP;(void)wms_ts_decode(&
tRawTsData,&tClientTsData);if(WMS_TPDU_STATUS_REPORT!=tClientTsData.u.gw_pp.
tpdu_type){at_print(LOG_ERR,
"\x64\x6f\x65\x73\x20\x6e\x6f\x74\x20\x67\x77\x2f\x77\x63\x64\x6d\x61\x20\x73\x6d\x73\x20\x72\x65\x70\x6f\x72\x74\x20\x73\x74\x61\x74\x75\x73\x2e"
);return WMS_CMD_FAILED;}switch(tClientTsData.u.gw_pp.u.status_report.tp_status)
{case WMS_TP_STATUS_RECEIVED_OK:case WMS_TP_STATUS_UNABLE_TO_CONFIRM_DELIVERY:
case WMS_TP_STATUS_REPLACED:{iReportStatus=(0x133a+4868-0x263d);break;}case 
WMS_TP_STATUS_TRYING_CONGESTION:case WMS_TP_STATUS_TRYING_SME_BUSY:case 
WMS_TP_STATUS_TRYING_NO_RESPONSE_FROM_SME:case 
WMS_TP_STATUS_TRYING_SERVICE_REJECTED:case 
WMS_TP_STATUS_TRYING_QOS_NOT_AVAILABLE:case WMS_TP_STATUS_TRYING_SME_ERROR:{
iReportStatus=(0x1d2b+1523-0x231b);break;}default:{iReportStatus=
(0x1ad2+1307-0x1feb);break;}}zUfiSms_FormatDeliverNumber(tClientTsData.u.gw_pp.u
.status_report.address,acDeliverNumber);(void)zUfiSms_UtilTimeStamp(
tClientTsData.u.gw_pp.u.status_report.timestamp,tTpScts,&tSmsDate);if(ZUFI_FAIL
==zUfiSms_InsertReportStatusToDb(acDeliverNumber,&tSmsDate,iReportStatus)){
at_print(LOG_ERR,
"\x75\x70\x64\x61\x74\x65\x20\x73\x6d\x73\x20\x72\x65\x70\x6f\x72\x74\x20\x73\x74\x61\x74\x75\x73\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return WMS_CMD_FAILED;}memset(acRecFlag,(0xd8a+2403-0x16ed),sizeof(acRecFlag))
;sc_cfg_get(ZTE_WMS_NVCONFIG_SMS_REPORT,acRecFlag,sizeof(acRecFlag));iRpCount=
atoi(acRecFlag);if(iRpCount<(0x1100+4860-0x23fc)||iRpCount>INT_MAX-
(0x816+2769-0x12e6)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x69\x52\x70\x43\x6f\x75\x6e\x74\x20\x65\x72\x72\x3a\x25\x64" "\n"
,iRpCount);return WMS_CMD_FAILED;}memset(acRecFlag,(0xa90+5237-0x1f05),sizeof(
acRecFlag));snprintf(acRecFlag,sizeof(acRecFlag),"\x25\x64",iRpCount+
(0x1bf1+706-0x1eb2));sc_cfg_set(ZTE_WMS_NVCONFIG_SMS_REPORT,acRecFlag);return 
WMS_CMD_SUCCESS;}void zUfiSms_DelModemSms(int in_index){
#if (0x16dd+487-0x18c4) 
char StrValue[(0x4c9+1786-0xbb9)]={(0xc97+5860-0x237b)};printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x4d\x6f\x64\x65\x6d\x53\x6d\x73\x20\x66\x75\x6e\x20\x75\x73\x65\x64\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21\x21" "\n"
);snat_print(LOG_DEBUGStrValue,sizeof(StrValue),"\x25\x64",in_index);
zSvr_InnerSendMsg(ZUFI_MODULE_ID_AT_LOCAL,ZUFI_MODULE_ID_AT_UNSOLI,
MSG_CMD_AT_DEL_SIM_SMS,strlen(StrValue),StrValue);
#endif
int atRes=(0xb08+6377-0x23f1);atRes=zSms_SendCmgdReq(in_index);if(atRes==
ZSMS_RESULT_OK){zSms_RecvCmgdOk();}else{zSms_RecvCmgdErr();}zSms_RecvCmgdFinish(
);}VOID zUfiSms_getModifyInfo(T_zUfiSms_ModifyFlag*ptModifyBuff){int i=
(0x114f+2651-0x1baa);memset(&g_zUfiSms_modifyMsg,(0x77+3540-0xe4b),sizeof(
T_zUfiSms_ModifySms));for(i=(0x7d1+904-0xb59);i<ptModifyBuff->total_id;i++){
g_zUfiSms_modifyMsg.sim_id[g_zUfiSms_modifyMsg.sim_count]=ptModifyBuff->id[i];
g_zUfiSms_modifyMsg.sim_count++;g_zUfiSms_modifyMsg.sim_index_count++;}}int 
zUfiSms_GetUnreadSmsIndexInSim(){char str_index[(0x16ed+2820-0x2171)]={
(0x4f9+4891-0x1814)};int index=(0x22ea+735-0x25c9);int is_cc=(0x1b01+458-0x1ccb)
;int iSmsId=(0x172+7389-0x1e4f);T_zUfiSms_ModifyTag tModifyInfo={
(0x132b+3922-0x227d)};char StrValue[(0x1e3a+332-0x1f7c)]={(0x8d0+6832-0x2380)};
memset(&tModifyInfo,(0x1672+2345-0x1f9b),sizeof(T_zUfiSms_ModifyTag));iSmsId=
g_zUfiSms_modifyMsg.sim_id[g_zUfiSms_modifyMsg.sim_index];if(-
(0x1692+879-0x1a00)==(is_cc=zUfiSms_IsConcatSms(iSmsId))){at_print(LOG_ERR,
"\x63\x68\x65\x63\x6b\x20\x63\x6f\x6e\x63\x61\x74\x20\x66\x61\x69\x6c\x65\x64\x2e"
);return ZUFI_FAIL;}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x55\x6e\x72\x65\x61\x64\x53\x6d\x73\x49\x6e\x64\x65\x78\x49\x6e\x53\x69\x6d\x20\x69\x73\x5f\x63\x63\x3a\x25\x64"
,is_cc);if((0x5a1+3563-0x138b)==is_cc){if(ZUFI_FAIL==zUfiSms_GetSmsIndex(iSmsId,
&tModifyInfo,is_cc)){return ZUFI_FAIL;}g_zUfiSms_modifyMsg.sim_index++;
g_zUfiSms_modifyMsg.sim_index_count--;while(tModifyInfo.num_of_indices>
(0xaa4+1068-0xed0)){index=tModifyInfo.indices[tModifyInfo.id_index];
g_modifyIndex.index[g_modifyIndex.total]=index;g_modifyIndex.total++;tModifyInfo
.id_index++;tModifyInfo.num_of_indices--;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x55\x6e\x72\x65\x61\x64\x53\x6d\x73\x49\x6e\x64\x65\x78\x49\x6e\x53\x69\x6d\x20\x5b\x31\x20\x3d\x3d\x20\x69\x73\x5f\x63\x63\x5d\x20\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x64\x2c\x20\x74\x6f\x74\x61\x6c\x3d\x25\x64" "\n"
,index,g_modifyIndex.total);}}else{memset(str_index,(0x31c+2542-0xd0a),sizeof(
str_index));if(ZUFI_FAIL==zUfiSms_GetStorePosById("\x69\x6e\x64",str_index,
sizeof(str_index),iSmsId)){at_print(LOG_ERR,
"\x67\x65\x74\x20\x69\x6e\x64\x65\x78\x20\x66\x72\x6f\x6d\x20\x64\x62\x20\x66\x61\x69\x6c\x64\x2e"
);return ZUFI_FAIL;}index=atoi(str_index);g_modifyIndex.index[g_modifyIndex.
total]=index;g_modifyIndex.total++;g_zUfiSms_modifyMsg.sim_index++;
g_zUfiSms_modifyMsg.sim_index_count--;printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x47\x65\x74\x55\x6e\x72\x65\x61\x64\x53\x6d\x73\x49\x6e\x64\x65\x78\x49\x6e\x53\x69\x6d\x20\x5b\x31\x20\x21\x3d\x20\x69\x73\x5f\x63\x63\x5d\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x64\x2c\x20\x74\x6f\x74\x61\x6c\x3d\x25\x64" "\n"
,index,g_modifyIndex.total);}return ZUFI_SUCC;}void zUfiSms_ModifyModemSms(
T_zUfiSms_ModifyFlag*ptModifyBuff){int atRes=(0x1ce8+1630-0x2346);char StrValue[
(0x14cc+2663-0x1f29)]={(0xa67+340-0xbbb)};printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4d\x6f\x64\x69\x66\x79\x4d\x6f\x64\x65\x6d\x53\x6d\x73\x20\x70\x74\x4d\x6f\x64\x69\x66\x79\x42\x75\x66\x66\x2d\x3e\x74\x79\x70\x65\x20\x3d\x20\x25\x64\x21" "\n"
,ptModifyBuff->type);{zUfiSms_getModifyInfo(ptModifyBuff);memset(&g_modifyIndex,
(0x6ed+3714-0x156f),sizeof(T_zUfiSms_ModifyIndexInfo));printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4d\x6f\x64\x69\x66\x79\x4d\x6f\x64\x65\x6d\x53\x6d\x73\x20\x70\x74\x4d\x6f\x64\x69\x66\x79\x42\x75\x66\x66\x2d\x3e\x74\x6f\x74\x61\x6c\x5f\x69\x64\x20\x3d\x20\x25\x64\x21" "\n"
,ptModifyBuff->total_id);while(g_zUfiSms_modifyMsg.sim_index_count>
(0x1a9+6662-0x1baf)){if(ZUFI_FAIL==zUfiSms_GetUnreadSmsIndexInSim()){at_print(
LOG_ERR,
"\x64\x65\x6c\x65\x74\x65\x20\x61\x6c\x6c\x3a\x25\x64\x20\x73\x6d\x73\x20\x66\x61\x69\x6c\x65\x64" "\n"
,WMS_STORAGE_TYPE_UIM_V01);return;}}while(g_modifyIndex.cur_index<g_modifyIndex.
total){snprintf(StrValue,sizeof(StrValue),"\x25\x64",g_modifyIndex.index[
g_modifyIndex.cur_index]);atRes=zSms_SendCmgrReq(g_modifyIndex.index[
g_modifyIndex.cur_index]);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x4d\x6f\x64\x69\x66\x79\x4d\x6f\x64\x65\x6d\x53\x6d\x73\x20\x69\x6e\x64\x65\x78\x20\x3d\x20\x25\x73\x20" "\n"
,StrValue);g_modifyIndex.cur_index++;}}}static int zUfiSms_FormatGwDbData(
T_zUfiSms_ClientTsData*ts_data_ptr,T_zUfiSms_DbStoreData*db_data){int result=
ZUFI_SUCC;if((NULL==ts_data_ptr)||(NULL==db_data)){at_print(LOG_ERR,
"\x69\x6e\x76\x61\x6c\x69\x64\x20\x69\x6e\x70\x75\x74\x73\x2e");return ZUFI_FAIL
;}if(WMS_FORMAT_GW_PP==ts_data_ptr->format){switch(ts_data_ptr->u.gw_pp.
tpdu_type){case WMS_TPDU_DELIVER:{result=zUfiSms_FormatDeliverDbdata(ts_data_ptr
,db_data);break;}case WMS_TPDU_SUBMIT:{result=zUfiSms_FormatSubmitDbdata(
ts_data_ptr,db_data);break;}default:{break;}}}return result;}int 
zUfiSms_DecodeSmsData(T_zUfiSms_DbStoreData*pDb_Data,int msg_index,
zUfiSms_StoreType iStorePos,T_SmsStatus bSms_Status,wms_message_format_enum_v01 
format,long iPdu_Len,unsigned char*pPdu_Received){T_zUfiSms_RawTsData raw_ts;
T_zUfiSms_ClientTsData ts_data_ptr;char tmp[(0x16d5+2337-0x1fec)];int tmp_i=
(0x799+2747-0x1254);unsigned int pos=(0x8da+1630-0xf38);int result=ZUFI_SUCC;if(
NULL==pDb_Data){return ZUFI_FAIL;}pDb_Data->mem_store=(unsigned long)iStorePos;
pDb_Data->index=(unsigned short)msg_index;if(RECEIVED_UNREAD==bSms_Status){
pDb_Data->tag=WMS_TAG_TYPE_MT_NOT_READ_V01;}else if(RECEIVED_READ==bSms_Status){
pDb_Data->tag=WMS_TAG_TYPE_MT_READ_V01;}else if(STORED_UNSEND==bSms_Status){
pDb_Data->tag=WMS_TAG_TYPE_MO_NOT_SENT_V01;}else{pDb_Data->tag=
WMS_TAG_TYPE_MO_SENT_V01;}pDb_Data->mode=(unsigned short)format;memset(&raw_ts,
(0xb16+6300-0x23b2),sizeof(T_zUfiSms_RawTsData));memset(&ts_data_ptr,
(0x1aec+92-0x1b48),sizeof(wms_client_ts_data_s_type));memset(tmp,
(0x1d9+8450-0x22db),sizeof(tmp));snprintf(tmp,sizeof(tmp),"\x25\x64",
pPdu_Received[(0x11aa+3028-0x1d7e)]);tmp_i=atoi(tmp);if(tmp_i<
(0x9eb+1855-0x112a)||tmp_i>INT_MAX-(0x6a+8839-0x22f0)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x70\x50\x64\x75\x5f\x52\x65\x63\x65\x69\x76\x65\x64\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);return ZUFI_FAIL;}pos=tmp_i+(0x14a5+1313-0x19c5);if(pos>=iPdu_Len){
return ZUFI_FAIL;}raw_ts.len=iPdu_Len-pos;memcpy((void*)raw_ts.data,(void*)(
pPdu_Received+pos),WMS_MAX_LEN);raw_ts.tpdu_type=zUfiSms_GetTpduType(
pPdu_Received+pos);raw_ts.format=(WMS_MESSAGE_FORMAT_CDMA_V01==format)?
WMS_FORMAT_CDMA:WMS_FORMAT_GW_PP;(void)wms_ts_decode(&raw_ts,&ts_data_ptr);
switch(format){case WMS_MESSAGE_FORMAT_CDMA_V01:{result=ZUFI_SUCC;break;}case 
WMS_MESSAGE_FORMAT_GW_PP_V01:{result=zUfiSms_FormatGwDbData(&ts_data_ptr,
pDb_Data);break;}case WMS_MESSAGE_FORMAT_GW_BC_V01:case 
WMS_MESSAGE_FORMAT_MWI_V01:{result=ZUFI_FAIL;break;}default:{result=ZUFI_FAIL;
break;}}return result;}T_zUfiSms_CmdStatus IsSmsLoadSuccess(void){char IsInit[
(0x77f+7959-0x2672)]={(0xb6f+3488-0x190f)};sc_cfg_get(NV_SMS_LOAD_RESULT,IsInit,
sizeof(IsInit));if((0x850+411-0x9eb)==strcmp("\x6f\x6b",IsInit)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x20\x4c\x6f\x61\x64\x20\x73\x75\x63\x63\x65\x73\x73\x21" "\n"
);return WMS_CMD_SUCCESS;}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x6d\x73\x20\x4c\x6f\x61\x64\x20\x77\x72\x6f\x6e\x67\x20\x21" "\n"
);return WMS_CMD_FAILED;}}
#if (0x12bf+4164-0x2303)
int zUfiSms_FormatSms(CHAR*pSmsRawContent,int contentSize,T_zUfiSms_SmsItem*
ptSmsPara,int iCmdId){char*P1=strchr(pSmsRawContent,((char)(0x8bd+1151-0xd10)));
if(NULL==P1){return ZUFI_FAIL;}char*P2=strchr((char*)(P1+(0x7af+3088-0x13be)),
((char)(0x1611+1465-0x1b9e)));if(NULL==P2){return ZUFI_FAIL;}char*P3=strchr((
char*)(P2+(0xa56+1665-0x10d6)),((char)(0x1232+2072-0x1a1e)));atBase_PreProcRes(
pSmsRawContent,contentSize);if((0x15ec+1236-0x1abf)==iCmdId){if(P3==P2+
(0x397+3432-0x10fe)){sscanf(pSmsRawContent,
"\x25\x64\x20\x25\x64\x20\x25\x64\x20",&ptSmsPara->index,&ptSmsPara->stat,&
ptSmsPara->length);}else{sscanf(pSmsRawContent,
"\x25\x64\x20\x25\x64\x20\x25\x33\x32\x73\x20\x25\x64\x20",&ptSmsPara->index,&
ptSmsPara->stat,ptSmsPara->alpha,&ptSmsPara->length);}}else if(
(0x1200+770-0x1500)==iCmdId){if(P2==P1+(0x507+6108-0x1ce2)){sscanf(
pSmsRawContent,"\x25\x64\x20\x25\x64\x20",&ptSmsPara->stat,&ptSmsPara->length);}
else{sscanf(pSmsRawContent,"\x25\x64\x20\x25\x33\x32\x73\x20\x25\x64\x20",&
ptSmsPara->stat,ptSmsPara->alpha,&ptSmsPara->length);}}atBase_RestoreString(
ptSmsPara->alpha);atBase_RestoreString(ptSmsPara->pdu);return ZUFI_SUCC;}
#endif
void zUfiSms_CmglRespProc(T_zSms_SmsInd*pSmsItem){T_zUfiSms_CmdStatus result=
WMS_CMD_PROCESSING;T_zUfiSms_DbStoreData db_data={(0x1c8a+703-0x1f49)};
zUfiSms_StoreType mem_store=WMS_STORAGE_TYPE_UIM_V01;unsigned char pdu_tmp[
ZSMS_PDU_SIZE]={(0x142b+4269-0x24d8)};int total_count=(0xa22+3285-0x16f7);int 
sim_capability=(0x1208+4847-0x24f7);printf(
"\x5b\x53\x4d\x53\x5d\x20\x45\x6e\x74\x65\x72\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x6d\x67\x6c\x52\x65\x73\x70\x50\x72\x6f\x63\x21\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2f\x73\x74\x61\x74\x3a\x25\x64\x2f\x6c\x65\x6e\x67\x74\x68\x3a\x25\x64\x21" "\n"
,pSmsItem->index,pSmsItem->stat,pSmsItem->length);printf(
"\x5b\x53\x4d\x53\x5d\x20\x70\x64\x75\x20\x64\x61\x74\x61\x5f\x6c\x65\x6e\x3a\x25\x64\x2c\x20\x73\x74\x72\x3a\x25\x73\x21" "\n"
,strlen(pSmsItem->pdu),pSmsItem->pdu);memset(&db_data,(0x402+669-0x69f),sizeof(
db_data));memset(pdu_tmp,(0x2d8+2886-0xe1e),sizeof(pdu_tmp));(void)String2Bytes(
pSmsItem->pdu,pdu_tmp,(int)strlen(pSmsItem->pdu));
#if (0x19eb+1578-0x2014)
zUfiSms_GetReportStatus(pSmsItem->pdu,&pSmsItem->stat);if((0x1522+2601-0x1f46)==
pSmsItem->stat){printf(
"\x5b\x53\x4d\x53\x5d\x20\x45\x6e\x74\x65\x72\x20\x70\x53\x6d\x73\x49\x74\x65\x6d\x2d\x3e\x73\x74\x61\x74\x20\x3d\x3d\x20\x35" "\n"
);(void)zUfiSms_HandleReport(pdu_tmp);zUfiSms_DelModemSms(pSmsItem->index);
return;}
#endif
#if (0xbf5+3620-0x1a18)
printf("\x2a\x2a\x2a\x2a\x75\x6e\x64\x65\x63\x6f\x64\x65\x3a\x25\x73" "\n",
pdu_tmp);
#endif
(void)zUfiSms_DecodeSmsData(&db_data,pSmsItem->index,mem_store,(T_SmsStatus)
pSmsItem->stat,WMS_MESSAGE_FORMAT_GW_PP_V01,pSmsItem->length,pdu_tmp);
#if (0x242+3946-0x11ab)
printf("\x2a\x2a\x2a\x2a\x64\x65\x63\x6f\x64\x65\x65\x64\x3a\x25\x73" "\n",
db_data.sms_content);
#endif
(void)zUfiSms_WriteSmsToDb(&db_data,mem_store,-(0x56+3601-0xe66));{}
#if (0xa2b+7018-0x2594)
if(SMS_LOCATION_SIM==g_zUfiSms_CurLocation){CHAR simCapability[
(0xcf5+837-0x1008)]={(0x1c5+8627-0x2378)};sc_cfg_get(
ZTE_WMS_NVCONFIG_SIM_CAPABILITY,simCapability,sizeof(simCapability));
sim_capability=atoi(simCapability);(void)zUfiSms_GetTotalCount(
ZTE_WMS_DB_SIM_TABLE,&total_count);if(total_count==sim_capability){
zUfiSms_DelModemSms(pSmsItem->index);}}
#endif
}void zUfiSms_InitDb(void){if(ZUFI_FAIL==zUfiSms_CheckStoreDir()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x68\x65\x63\x6b\x73\x74\x6f\x72\x65\x44\x69\x72\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);return;}
#ifdef WEBS_SECURITY
if(access(ZTE_WMS_DB_PATH,F_OK)!=(0x67+4684-0x12b3)){if((access(
ZTE_WMS_TMP1_PATH,F_OK)==(0x1fcd+1597-0x260a))&&(access(ZTE_WMS_SEC_PATH,F_OK)!=
(0xa1a+4986-0x1d94))){if(rename(ZTE_WMS_TMP1_PATH,ZTE_WMS_SEC_PATH)!=
(0x3fa+3679-0x1259)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x57\x4d\x53\x5f\x54\x4d\x50\x31\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);}}if((access(ZTE_WMS_TMP0_PATH,F_OK)==(0x233d+515-0x2540))&&(access(
ZTE_WMS_SEC_PATH,F_OK)!=(0x4f2+5124-0x18f6))){if(rename(ZTE_WMS_TMP0_PATH,
ZTE_WMS_SEC_PATH)!=(0x1665+1841-0x1d96)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x57\x4d\x53\x5f\x54\x4d\x50\x30\x5f\x50\x41\x54\x48\x20\x66\x61\x69\x6c\x65\x64\x2e" "\n"
);}}if(access(ZTE_WMS_SEC_PATH,F_OK)==(0x3bb+5242-0x1835)){char rnum_buf[
(0xb98+625-0xdf1)]={(0x127+1953-0x8c8)};char cmd[(0x249f+234-0x2509)]={
(0x2+4864-0x1302)};sc_cfg_get("\x72\x6e\x75\x6d\x5f\x61\x74",rnum_buf,sizeof(
rnum_buf));snprintf(cmd,sizeof(cmd),
"\x2f\x62\x69\x6e\x2f\x6f\x70\x65\x6e\x73\x73\x6c\x20\x65\x6e\x63\x20\x2d\x64\x20\x2d\x61\x65\x73\x32\x35\x36\x20\x2d\x73\x61\x6c\x74\x20\x2d\x69\x6e\x20\x25\x73\x20\x2d\x6f\x75\x74\x20\x25\x73\x20\x2d\x70\x61\x73\x73\x20\x70\x61\x73\x73\x3a\x25\x73"
,ZTE_WMS_SEC_PATH,ZTE_WMS_DB_PATH,rnum_buf);zxic_system(cmd);}}
#endif		
if(ZUFI_FAIL==zUfiSms_CheckSmsDb()){zUfiSms_GetDefaultCfgPara();}if(ZUFI_SUCC!=
zUfiSms_CreateAllTable()){printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x43\x72\x65\x61\x74\x65\x41\x6c\x6c\x54\x61\x62\x6c\x65\x20\x66\x61\x69\x6c\x65\x64" "\n"
);return;}if(ZUFI_SUCC!=zUfiSms_LoadSmsPara()){zUfiSms_GetDefaultPara();}(void)
zUfiSms_CheckMemoryFull(ZTE_WMS_MEMORY_NV);zUfiSms_SendSmsStatusInfo(
MSG_SMS_DEFAULT);g_zUfiSms_MsgRefer=zUfiSms_GetSmsMaxReferInDb();
g_zUfiSms_ConcatSmsReference=zUfiSms_GetConcatMaxReferInDb();}void 
zUfiSms_CfgSmsNvInit(void){char tmp[(0x598+4533-0x1743)]={(0x2bd+8089-0x2256)};
sc_cfg_set(NV_SMS_STORE,"");sc_cfg_set(NV_SMS_LOAD_RESULT,"");sc_cfg_set(
ZTE_WMS_NVCONFIG_RECEVIED,"");sc_cfg_set(NV_SMS_CENTER_NUM,"");snprintf(tmp,
sizeof(tmp),"\x25\x64",ZTE_WMS_DB_MSG_COUNT_MAX);sc_cfg_set(
ZTE_WMS_NVCONFIG_NV_CAPABILITY,tmp);}VOID zUfiSms_InitCmdStatus(
T_zUfiSms_StatusInfo*pStatusInfo,T_zUfiSms_CmdType iCmdId){memset((void*)
pStatusInfo,(0x1344+1032-0x174c),sizeof(T_zUfiSms_StatusInfo));pStatusInfo->cmd=
iCmdId;pStatusInfo->cmd_status=WMS_CMD_PROCESSING;pStatusInfo->err_code=
ZTE_SMS_CMS_NONE;pStatusInfo->send_failed_count=(0x930+6077-0x20ed);pStatusInfo
->delete_failed_count=(0x650+3643-0x148b);(void)zUfiSms_SetCmdStatus(pStatusInfo
);}void zUfiSms_CfgInit(void){char tmp[(0x3e3+2582-0xdef)]={(0x229+5668-0x184d)}
;sc_cfg_set(NV_SMS_STATE,"");sc_cfg_set(ZTE_WMS_NVCONFIG_RECEVIED,"");sc_cfg_set
(NV_SMS_LOAD_RESULT,"");sc_cfg_set(NV_SMS_RECV_RESULT,"");snprintf(tmp,sizeof(
tmp),"\x25\x64",ZTE_WMS_DB_MSG_COUNT_MAX);sc_cfg_set(
ZTE_WMS_NVCONFIG_NV_CAPABILITY,tmp);sc_cfg_set(ZTE_WMS_NVCONFIG_SIM_CAPABILITY,
"");sc_cfg_set(ZTE_WMS_NVCONFIG_SMS_REPORT,"");sc_cfg_set(NV_SMS_DB_CHANGE,
"\x31");sc_cfg_set(NV_SMS_CENTER_NUM,"");}int zUfiSms_IsUnreadSms(
T_zUfiSms_MemoryType mem_store){int total_count=(0x1458+1854-0x1b96);char buf[
(0x306+2836-0xe06)]={(0x45c+3697-0x12cd)};if(ZUFI_FAIL==zUfiSms_GetTagCountInDb(
mem_store,WMS_TAG_TYPE_MT_NOT_READ_V01,&total_count)){return FALSE;}sprintf(buf,
"\x25\x64",total_count);sc_cfg_set(
"\x73\x6d\x73\x5f\x75\x6e\x72\x65\x61\x64\x5f\x63\x6f\x75\x6e\x74",buf);if(
(0x1914+2542-0x2302)==total_count){return FALSE;}else{return TRUE;}}VOID 
zUfiSms_SendSmsStatusInfo(wms_message_status_info sms_op){
T_zUfi_SmsStatusInfoInd ind={(0xa44+5552-0x1ff4)};CHAR temp[(0xa2d+4473-0x1b74)]
={(0x13dd+520-0x15e5)};if(sms_op==MSG_SMS_NEW){ind.sms_new_ind=
(0xeab+1077-0x12df);}if(sms_op==MSG_SMS_READING){ind.sms_is_reading=
(0x1097+935-0x143d);}if(zUfiSms_IsUnreadSms(ZTE_WMS_MEMORY_NV)){ind.
sms_unread_ind=(0x933+5558-0x1ee8);}else{ind.sms_unread_ind=(0x2c5+528-0x4d5);}
sc_cfg_get("\x73\x6d\x73\x5f\x75\x6e\x72\x65\x61\x64\x5f\x63\x6f\x75\x6e\x74",
temp,sizeof(temp));ind.sms_unread_count=atoi(temp);if((g_zUfiSms_MemFullFlag[
WMS_STORAGE_TYPE_NV_V01]==TRUE)||(g_zUfiSms_MemFullFlag[WMS_STORAGE_TYPE_UIM_V01
]==TRUE)){ind.sms_memory_full_ind=(0x24b+9078-0x25c0);}else{ind.
sms_memory_full_ind=(0xa59+1197-0xf06);}printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x53\x65\x6e\x64\x53\x6d\x73\x53\x74\x61\x74\x75\x73\x49\x6e\x66\x6f\x20\x75\x6e\x72\x65\x61\x64\x20\x3d\x20\x25\x64\x2c\x20\x66\x75\x6c\x6c\x3d\x25\x64\x2c\x20\x6e\x65\x77\x3d\x25\x64\x2c\x20\x72\x65\x61\x64\x69\x6e\x67\x3d\x25\x64" "\n"
,ind.sms_unread_ind,ind.sms_memory_full_ind,ind.sms_new_ind,ind.sms_is_reading);
ipc_send_message(MODULE_ID_SMS,MODULE_ID_MMI,MSG_CMD_SMS_STATUS_INFO_IND,sizeof(
T_zUfi_SmsStatusInfoInd),&ind,(0x4af+5793-0x1b50));if(ind.sms_memory_full_ind==
(0x15d5+2248-0x1e9c)){sc_cfg_set(NV_SMS_INIT_STATUS,
"\x73\x6d\x73\x5f\x6d\x65\x6d\x6f\x72\x79\x5f\x66\x75\x6c\x6c");}else if(ind.
sms_unread_ind==(0x49b+8111-0x2449)){sc_cfg_set(NV_SMS_INIT_STATUS,
"\x73\x6d\x73\x5f\x75\x6e\x72\x65\x61\x64");}else{sc_cfg_set(NV_SMS_INIT_STATUS,
"\x73\x6d\x73\x5f\x6e\x6f\x72\x6d\x61\x6c");}return;}VOID BakNotificationSms(
char*pushSms,int pushSmsLen){FILE*fp=NULL;int len;fp=fopen(
"\x2f\x75\x73\x72\x2f\x64\x6d\x2f\x66\x6f\x74\x61\x5f\x70\x75\x73\x68\x5f\x6d\x73\x67\x2e\x64\x61\x74\x61"
,"\x77\x2b");if(fp==NULL){printf(
"\x5b\x53\x4d\x53\x5d\x20\x42\x61\x6b\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x53\x6d\x73\x20\x66\x6f\x70\x65\x6e\x20\x65\x72\x72\x6f\x72" "\n"
);at_print(LOG_ERR,
"\x66\x6f\x70\x65\x6e\x28\x20\x2f\x75\x73\x72\x2f\x64\x6d\x2f\x66\x6f\x74\x61\x5f\x70\x75\x73\x68\x5f\x6d\x73\x67\x2e\x64\x61\x74\x61\x20\x2c\x20\x27\x77\x2b\x27\x29\x20\x65\x72\x72\x6f\x72\x21"
);return;}len=fwrite(pushSms,(0x17c5+3472-0x2554),pushSmsLen,fp);if(len==
pushSmsLen){printf(
"\x5b\x53\x4d\x53\x5d\x20\x42\x61\x6b\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x53\x6d\x73\x20\x77\x72\x69\x74\x65\x20\x74\x6f\x20\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x62\x61\x6b\x20\x66\x69\x6c\x65\x20\x4f\x4b\x2c\x20\x6c\x65\x6e\x3d\x25\x64\x20" "\n"
,len);printf(
"\x77\x72\x69\x74\x65\x20\x74\x6f\x20\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x62\x61\x6b\x20\x66\x69\x6c\x65\x20\x4f\x4b\x2c\x20\x6c\x65\x6e\x3d\x25\x64\x21"
,len);fclose(fp);return;}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x42\x61\x6b\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x53\x6d\x73\x20\x77\x72\x69\x74\x65\x20\x74\x6f\x20\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x62\x61\x6b\x20\x66\x69\x6c\x65\x20\x66\x61\x69\x6c\x64\x2c\x20\x6c\x65\x6e\x3d\x25\x64\x20" "\n"
,len);printf(
"\x77\x72\x69\x74\x65\x20\x74\x6f\x20\x4e\x6f\x74\x69\x66\x69\x63\x61\x74\x69\x6f\x6e\x62\x61\x6b\x20\x66\x69\x6c\x65\x20\x66\x61\x69\x6c\x64\x2c\x20\x6c\x65\x6e\x3d\x25\x64\x21"
,len);fclose(fp);return;}}
