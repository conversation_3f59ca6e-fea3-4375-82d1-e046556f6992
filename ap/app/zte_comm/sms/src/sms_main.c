
#include <errno.h>
#include <sys/msg.h>
#include <semaphore.h>
#include <limits.h>
#include <pthread.h>
#include "sms_code.h"
typedef VOID(*pAtWeb_SmsMsgProc)(UINT8*pDatabuf);typedef struct{UINT32 msg_id;
VOID(*func_ptr)(UINT8*pDatabuf);BOOL need_block;}T_zSmsHandleTable;VOID 
atWeb_SendSms(UINT8*pDatabuf);VOID atWeb_DelSmsByIndex(UINT8*pDatabuf);VOID 
atWeb_DelSmsByType(UINT8*pDatabuf);VOID atWeb_ReadSms(UINT8*pDatabuf);VOID 
atWeb_SaveSms(UINT8*pDatabuf);VOID atWeb_SetSms(UINT8*pDatabuf);VOID 
atWeb_OutdateSmsCheck(UINT8*pDatabuf);int zSms_SendZmgrReq(int index);extern 
UINT8 g_zUfiSms_MemFullFlag[ZTE_WMS_MEMORY_MAX];extern T_zSms_SendSmsReq 
g_zUfiSms_FinalCmgsBuf;extern int g_zUfiSms_CurConcatSegNo;extern 
T_zUfiSms_DbStoreData g_zUfiSms_DbStoreData[ZTE_WMS_CONCAT_SMS_COUNT_MAX];extern
 int g_zUfiSms_SendFailedCount;extern int g_zUfiSms_ConcatTotalNum;extern UINT8 
g_zUfiSms_IsConcatSendSuc;int iSmsIndex=(0x4cc+1247-0x9ab);int g_zSms_MsqId=-
(0x221+7653-0x2005);int g_zSms_LocalMsqId=-(0x4c9+8726-0x26de);sem_t 
g_sms_sem_id;T_zSms_optRsp g_smsOptRsp={(0xd55+4362-0x1e5f)};static const 
T_zSmsHandleTable SmsHandleWebTab[]={{MSG_CMD_SEND_SMS,atWeb_SendSms,TRUE},{
MSG_CMD_DEL_SMS_BY_INDEX,atWeb_DelSmsByIndex,TRUE},{MSG_CMD_SMS_MODIFY_TAG,
atWeb_ReadSms,TRUE},{MSG_CMD_DRAFTS_SAVE,atWeb_SaveSms,FALSE},{
MSG_CMD_SMS_LOCATION_SET,atWeb_SetSms,TRUE},{MSG_CMD_SMS_OUTDATE_CHECK,
atWeb_OutdateSmsCheck,TRUE},{(0xf28+4820-0x21fc),NULL,FALSE}};VOID atWeb_SendSms
(UINT8*pDatabuf){T_zGoaheadMsgBuf*ptMessage=NULL;T_zUfiSms_StatusInfo tStatus={
(0x127d+3938-0x21df)};assert(pDatabuf!=NULL);ptMessage=(T_zGoaheadMsgBuf*)
pDatabuf;zUfiSms_InitCmdStatus(&tStatus,WMS_SMS_CMD_MSG_SEND);(void)
zUfiSms_SendRawSms((T_zUfiSms_SendReq*)ptMessage->msg_data);}VOID 
atWeb_DelSmsByIndex(UINT8*pDatabuf){T_zUfiSms_DelReq tDelReq={
(0xba9+2603-0x15d4)};T_zUfiSms_CmdStatus result=WMS_CMD_PROCESSING;
T_zUfiSms_StatusInfo tStatus={(0x201+6650-0x1bfb)};assert(pDatabuf!=NULL);printf
("[SMS] atWeb_DelSmsByIndex recv msg\n");memcpy(&tDelReq,pDatabuf,sizeof(
T_zUfiSms_DelReq));zUfiSms_InitCmdStatus(&tStatus,WMS_SMS_CMD_MSG_DELETE);result
=zUfiSms_DeleteSms(&tDelReq);tStatus.cmd_status=result;(void)
zUfiSms_SetCmdStatus(&tStatus);zUfiMmi_SendSmsStatus();}VOID atWeb_DelSmsByType(
UINT8*pDatabuf){
#if (0x355+2959-0xee4)
WEB_DEL_SMS_BY_TYPE*req=NULL;req=(WEB_DEL_SMS_BY_TYPE*)pDatabuf;assert(req!=NULL
);if(req->eLocation!=ZSMS_LOCATION_SIM){ZTE_LOG(LOG_ERR,
"\x7a\x53\x6d\x73\x5f\x50\x72\x65\x70\x44\x65\x6c\x42\x79\x54\x79\x70\x65\x20\x70\x61\x72\x61\x20\x4e\x55\x4c\x4c\x2e" "\n"
);return;}zSms_ChangeMainState(ZSMS_STATE_DELING);ZTE_LOG(LOG_DEBUG,
"\x7a\x53\x6d\x73\x5f\x50\x72\x65\x70\x44\x65\x6c\x42\x79\x54\x79\x70\x65\x20\x70\x73\x74\x52\x65\x71\x2d\x3e\x65\x42\x6f\x78\x4e\x61\x6d\x65\x3d\x25\x64" "\n"
,req->eBoxName);SMS_DeleteRecordFromXML(SMS_LOCATION_SIM,(0x79+3976-0x1001),req
->eBoxName);if(req->eBoxName==SMS_INBOX){sms_LoadSmsFromSim();}
#endif
}VOID atWeb_ReadSms(UINT8*pDatabuf){T_zGoaheadMsgBuf*ptMessage=NULL;
T_zUfiSms_CmdStatus result=WMS_CMD_PROCESSING;T_zUfiSms_StatusInfo tStatus={
(0x9d1+4683-0x1c1c)};assert(pDatabuf!=NULL);ptMessage=(T_zGoaheadMsgBuf*)
pDatabuf;zUfiSms_InitCmdStatus(&tStatus,WMS_SMS_CMD_MSG_MODIFY_TAG);result=
zUfiSms_ModifySmsTag((T_zUfiSms_ModifyFlag*)ptMessage->msg_data);tStatus.
cmd_status=result;(void)zUfiSms_SetCmdStatus(&tStatus);}VOID atWeb_SaveSms(UINT8
*pDatabuf){T_zGoaheadMsgBuf*ptMessage=NULL;T_zUfiSms_CmdStatus result=
WMS_CMD_PROCESSING;T_zUfiSms_StatusInfo tStatus={(0x1368+1683-0x19fb)};assert(
pDatabuf!=NULL);ptMessage=(T_zGoaheadMsgBuf*)pDatabuf;zUfiSms_InitCmdStatus(&
tStatus,WMS_SMS_CMD_MSG_WRITE);result=zUfiSms_WriteRawSms((T_zUfiSms_SaveReq*)
ptMessage->msg_data);if(g_zUfiSms_MemFullFlag[ZTE_WMS_MEMORY_NV]){tStatus.
err_code=ZTE_SMS_CMS_MEM_FULL;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x57\x65\x62\x5f\x53\x61\x76\x65\x53\x6d\x73\x20\x6d\x65\x6d\x20\x66\x75\x6c\x6c" "\n"
);}tStatus.cmd_status=result;(void)zUfiSms_SetCmdStatus(&tStatus);
zUfiSms_ChangeMainState(SMS_STATE_SAVING);sc_cfg_set(NV_SMS_SAVE_RESULT,
"\x6f\x6b");}VOID atWeb_SetSms(UINT8*pDatabuf){T_zGoaheadMsgBuf*ptMessage=NULL;
T_zUfiSms_CmdStatus result=WMS_CMD_PROCESSING;T_zUfiSms_StatusInfo tStatus={
(0x1740+3356-0x245c)};printf(
"\x49\x4e\x54\x4f\x20\x61\x74\x57\x65\x62\x5f\x53\x65\x74\x53\x6d\x73\x2e" "\n")
;assert(pDatabuf!=NULL);ptMessage=(T_zGoaheadMsgBuf*)pDatabuf;
zUfiSms_InitCmdStatus(&tStatus,WMS_SMS_CMD_CFG_SET_PARAMETERS);result=
zUfiSms_SetSmsPara((T_zUfiSms_ParaInfo*)ptMessage->msg_data);tStatus.cmd_status=
result;(void)zUfiSms_SetCmdStatus(&tStatus);}VOID atWeb_OutdateSmsCheck(UINT8*
pDatabuf){T_zUfiSms_DelReq tSmsDel={(0x1ea3+130-0x1f25)};
zUfiSms_CheckDbOutdateSms(ZTE_WMS_DB_NV_TABLE,&tSmsDel);printf(
"\x2d\x2d\x2d\x2d\x6f\x75\x74\x20\x63\x6f\x75\x6e\x20\x6e\x76\x74\x3a\x20\x25\x64\x2d\x2d\x2d\x2d" "\n"
,tSmsDel.all_or_count);if(tSmsDel.all_or_count>(0xe7f+2706-0x1911)){
atWeb_DelSmsByIndex(&tSmsDel);}memset(&tSmsDel,(0xa8f+5393-0x1fa0),sizeof(
T_zUfiSms_DelReq));zUfiSms_CheckDbOutdateSms(ZTE_WMS_DB_SIM_TABLE,&tSmsDel);
printf(
"\x2d\x2d\x2d\x2d\x6f\x75\x74\x20\x63\x6f\x75\x6e\x74\x20\x73\x69\x6d\x3a\x20\x25\x64\x2d\x2d\x2d\x2d" "\n"
,tSmsDel.all_or_count);if(tSmsDel.all_or_count>(0x11d5+3768-0x208d)){
atWeb_DelSmsByIndex(&tSmsDel);}}VOID zSms_HandleWebMsg(MSG_BUF*ptMsgBuf){UINT32 
i=(0x15bf+1610-0x1c09);assert(ptMsgBuf!=NULL);printf(
"\x73\x6d\x73\x20\x72\x65\x63\x76\x20\x6d\x73\x67\x20\x66\x72\x6f\x6d\x20\x77\x65\x62\x73\x65\x72\x76\x65\x72\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);while((0x103b+4119-0x2052)!=SmsHandleWebTab[i].msg_id){if(
ptMsgBuf->usMsgCmd==SmsHandleWebTab[i].msg_id){if(SmsHandleWebTab[i].need_block
&&ptMsgBuf->src_id!=MODULE_ID_SMS){ipc_send_message(MODULE_ID_SMS,
MODULE_ID_SMS_LOCAL,ptMsgBuf->usMsgCmd,ptMsgBuf->usDataLen,(unsigned char*)
ptMsgBuf->aucDataBuf,(0x120+4897-0x1441));}else if(NULL!=SmsHandleWebTab[i].
func_ptr){SmsHandleWebTab[i].func_ptr(ptMsgBuf->aucDataBuf);}break;}i++;}}SINT32
 zSms_SendMsg(USHORT Msg_cmd,USHORT us_DataLen,UCHAR*pData){printf(
"\x73\x6d\x73\x20\x73\x65\x6e\x64\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,Msg_cmd);ipc_send_message(MODULE_ID_SMS,MODULE_ID_AT_CTL,Msg_cmd,us_DataLen,(
unsigned char*)pData,(0x1843+708-0x1b07));return(0x500+4496-0x1690);}SINT32 
zSms_SendCmgsReq(VOID){T_zSms_SendSmsReq sendSmsInfo={(0x114b+2610-0x1b7d)};
memset(&sendSmsInfo,(0x1b3d+2466-0x24df),sizeof(T_zSms_SendSmsReq));sendSmsInfo.
length=g_zUfiSms_FinalCmgsBuf.length;if(strlen(g_zUfiSms_FinalCmgsBuf.pdu)<
ZSMS_PDU_SIZE-(0x1a06+1740-0x20d1)){memcpy(sendSmsInfo.pdu,
g_zUfiSms_FinalCmgsBuf.pdu,strlen(g_zUfiSms_FinalCmgsBuf.pdu));}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6d\x67\x73\x52\x65\x71\x20\x70\x64\x75\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x25\x73" "\n"
,g_zUfiSms_FinalCmgsBuf.pdu);memcpy(sendSmsInfo.pdu,g_zUfiSms_FinalCmgsBuf.pdu,
ZSMS_PDU_SIZE-(0x323+5290-0x17cb));}*(sendSmsInfo.pdu+strlen(
g_zUfiSms_FinalCmgsBuf.pdu))=ZSMS_CTRL_Z_CHAR;
#if (0x1bbc+1807-0x22ca)
printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6d\x67\x73\x52\x65\x71\x20\x73\x65\x6e\x64\x20\x64\x61\x74\x61" "\n"
);printf("\n" "\x5b\x53\x4d\x53\x5d\x25\x73" "\n",sendSmsInfo.pdu);
#endif
zSms_SendMsg(MSG_CMD_SENDSMS_REQ,sizeof(T_zSms_SendSmsReq),&sendSmsInfo);
sem_wait(&g_sms_sem_id);if(g_smsOptRsp.result==(0x9c+7223-0x1cd2)){return 
ZSMS_RESULT_OK;}else{return ZSMS_RESULT_ERROR;}}VOID zSms_RecvCmgsOk(VOID){
printf(
"\x73\x6d\x73\x20\x73\x65\x6e\x64\x65\x64\x20\x73\x75\x63\x63\x65\x73\x73\x2e\x20" "\n"
);g_zUfiSms_CurConcatSegNo++;if(g_zUfiSms_CurConcatSegNo>
ZTE_WMS_CONCAT_SMS_COUNT_MAX){return;}g_zUfiSms_DbStoreData[
g_zUfiSms_CurConcatSegNo-(0x7+113-0x77)].tag=WMS_TAG_TYPE_MO_SENT_V01;
zUfiSms_CmgsRespProc();}VOID zSms_RecvCmgsErr(VOID){printf(
"\x73\x6d\x73\x20\x73\x65\x6e\x64\x65\x64\x20\x66\x61\x69\x6c\x2e\x20" "\n");
g_zUfiSms_CurConcatSegNo++;if(g_zUfiSms_CurConcatSegNo>
ZTE_WMS_CONCAT_SMS_COUNT_MAX){return;}g_zUfiSms_SendFailedCount++;printf(
"\x73\x65\x6e\x64\x20\x73\x6d\x73\x20\x66\x61\x69\x6c\x65\x64\x2c\x73\x6f\x20\x77\x72\x69\x74\x65\x20\x73\x6d\x73\x20\x74\x6f\x20\x64\x72\x61\x66\x74\x62\x6f\x78\x2e" "\n"
);g_zUfiSms_DbStoreData[g_zUfiSms_CurConcatSegNo-(0x1456+4322-0x2537)].tag=
WMS_TAG_TYPE_MO_NOT_SENT_V01;if(g_zUfiSms_ConcatTotalNum>(0x6f+1816-0x786)){
g_zUfiSms_IsConcatSendSuc=FALSE;}zUfiSms_CmgsRespProc();}SINT32 zSms_SendCmgdReq
(UINT8 index){T_zSms_DelSmsReq delSmsReq={(0x986+4552-0x1b4e)};delSmsReq.index=
index;zSms_SendMsg(MSG_CMD_DELSMS_REQ,sizeof(T_zSms_DelSmsReq),&delSmsReq);
sem_wait(&g_sms_sem_id);if(g_smsOptRsp.result==(0x169f+388-0x1822)){return 
ZSMS_RESULT_OK;}else{return ZSMS_RESULT_ERROR;}}VOID zSms_RecvCmgdOk(VOID){CHAR 
strUsed[(0x5a5+3367-0x12c2)]={(0x14f7+1669-0x1b7c)};int used=(0xd+4722-0x127f);
int tmp_i=(0x4bc+4514-0x165e);sc_cfg_set(NV_SMS_DEL_RESULT,"\x6f\x6b");printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x74\x20\x73\x69\x6d\x5f\x64\x65\x6c\x5f\x72\x65\x73\x75\x6c\x74\x20\x74\x6f\x20\x4f\x4b\x2e\x20" "\n"
);sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed,sizeof(strUsed));tmp_i=atoi(
strUsed);if(tmp_i<(0xa4f+5820-0x210b)||tmp_i>INT_MAX-(0x472+230-0x557)){at_print
(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x57\x4d\x53\x5f\x4e\x56\x43\x4f\x4e\x46\x49\x47\x5f\x53\x49\x4d\x5f\x43\x41\x52\x44\x5f\x55\x53\x45\x44\x20\x74\x6d\x70\x5f\x69\x20\x65\x72\x72\x3a\x25\x64" "\n"
,tmp_i);tmp_i=(0x1f5a+1595-0x2595);}used=tmp_i-(0x867+4498-0x19f8);if(used<
(0xb7d+3421-0x18da)){used=(0xe78+1046-0x128e);}memset(&strUsed,
(0xf43+1426-0x14d5),(0x136c+4097-0x2363));snprintf(strUsed,sizeof(strUsed),
"\x25\x64",used);sc_cfg_set(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed);}VOID 
zSms_RecvCmgdErr(VOID){sc_cfg_set(NV_SMS_DEL_RESULT,"\x66\x61\x69\x6c");printf(
"\x5b\x53\x4d\x53\x5d\x20\x73\x65\x74\x20\x73\x69\x6d\x5f\x64\x65\x6c\x5f\x72\x65\x73\x75\x6c\x74\x20\x74\x6f\x20\x66\x61\x69\x6c\x2e\x20" "\n"
);}VOID zSms_RecvCmgdFinish(VOID){char StrValue[(0xcbf+4054-0x1c8b)]={
(0x4aa+729-0x783)};CHAR strTotal[(0x7ca+6750-0x221e)]={(0xd85+1927-0x150c)};CHAR
 strUsed[(0x1a4+4334-0x1288)]={(0x4c7+8188-0x24c3)};int total=
(0x1f0+8418-0x22d2);int used=(0x29f+8939-0x258a);int remain=(0x1841+2287-0x2130)
;sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strUsed,sizeof(strUsed));used=atoi(
strUsed);if(used<(0x5b8+671-0x857)||used>INT_MAX-(0xf28+2221-0x17d4)){at_print(
LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x57\x4d\x53\x5f\x4e\x56\x43\x4f\x4e\x46\x49\x47\x5f\x53\x49\x4d\x5f\x43\x41\x52\x44\x5f\x55\x53\x45\x44\x20\x65\x72\x72\x3a\x25\x64" "\n"
,used);used=(0x2e2+1583-0x911);}sc_cfg_get(ZTE_WMS_NVCONFIG_SIM_CARD_TOTAL,
strTotal,sizeof(strTotal));total=atoi(strTotal);if(total<(0x90a+2495-0x12c9)||
total>INT_MAX-(0x709+2053-0xf0d)){at_print(LOG_ERR,
"\x5b\x53\x4d\x53\x5d\x57\x4d\x53\x5f\x4e\x56\x43\x4f\x4e\x46\x49\x47\x5f\x53\x49\x4d\x5f\x43\x41\x52\x44\x5f\x54\x4f\x54\x41\x4c\x20\x65\x72\x72\x3a\x25\x64" "\n"
,total);total=(0x23db+317-0x2518);}remain=total-used;if(remain<(0xb09+261-0xc0e)
){remain=(0x60b+3113-0x1234);}memset(&StrValue,(0x9d8+3496-0x1780),
(0x24f1+331-0x2632));snprintf(StrValue,sizeof(StrValue),"\x25\x64",remain);
sc_cfg_set(ZTE_WMS_NVCONFIG_SIM_CARD_REMAIN,StrValue);printf(
"\x5b\x53\x4d\x53\x5d\x20\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x65\x6c\x65\x74\x65\x53\x69\x6d\x53\x6d\x73\x20\x75\x73\x65\x64\x3d\x25\x64\x2c\x72\x65\x6d\x61\x69\x6e\x3d\x25\x64\x2c\x74\x6f\x74\x61\x6c\x3d\x25\x64" "\n"
,used,remain,total);zUfiSms_ChangeMainState(SMS_STATE_DELED);sc_cfg_set(
NV_SMS_DB_CHANGE,"\x31");}int zSms_SendZmenaReq(SINT32 avail){T_zSms_StroageReq 
storageReq={(0x1277+3780-0x213b)};storageReq.type=avail;zSms_SendMsg(
MSG_CMD_STORAGE_CAP_REQ,sizeof(T_zSms_StroageReq),&storageReq);sem_wait(&
g_sms_sem_id);if(g_smsOptRsp.result==(0x935+2062-0x1142)){return ZSMS_RESULT_OK;
}else{return ZSMS_RESULT_ERROR;}}int zSms_SendCmgrReq(UINT8 index){
T_zSms_ModifyTagReq modTagReq={(0x1b08+983-0x1edf)};modTagReq.index=index;
zSms_SendMsg(MSG_CMD_MODIFY_TAG_REQ,sizeof(T_zSms_ModifyTagReq),&modTagReq);
sem_wait(&g_sms_sem_id);if(g_smsOptRsp.result==(0xedf+1239-0x13b5)){return 
ZSMS_RESULT_OK;}else{return ZSMS_RESULT_ERROR;}}int zSms_SetCscaReq(PSTR sca){
T_zSms_SetScaReq setscareq;strncpy(setscareq.sca,sca,sizeof(setscareq.sca)-
(0x980+1687-0x1016));zSms_SendMsg(MSG_CMD_SCA_SET_REQ,sizeof(T_zSms_SetScaReq),&
setscareq);sem_wait(&g_sms_sem_id);if(g_smsOptRsp.result==(0x173a+3986-0x26cb)){
return ZSMS_RESULT_OK;}else{return ZSMS_RESULT_ERROR;}}int zSms_SendCnmiReq(PSTR
 pAtCmdPara){T_zSms_NotifySetReq notifySetReq={(0x4d5+2903-0x102c)};if(
(0x253+4786-0x1505)==strcmp(pAtCmdPara,"\x73\x69\x6d")){notifySetReq.mt=
(0xa6d+4346-0x1b66);}else{notifySetReq.mt=(0x1f83+478-0x215f);}zSms_SendMsg(
MSG_CMD_NOTIFY_SET_REQ,sizeof(T_zSms_NotifySetReq),&notifySetReq);sem_wait(&
g_sms_sem_id);if(g_smsOptRsp.result==(0x86a+4143-0x1898)){return ZSMS_RESULT_OK;
}else{return ZSMS_RESULT_ERROR;}}VOID zSms_RecvCmtInd(UINT8*pDatabuf){CHAR 
needSMS[(0x5bf+6997-0x20e2)]={(0xf82+703-0x1241)};sc_cfg_get(NV_NEED_SUPPORT_SMS
,needSMS,sizeof(needSMS));if((0x1a43+893-0x1dc0)==strcmp(needSMS,"\x6e\x6f")){
printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}T_zSms_SmsInd tCmtRsp={(0x1ab3+1040-0x1ec3)};memcpy(&tCmtRsp,(
T_zSms_SmsInd*)pDatabuf,sizeof(T_zSms_SmsInd));zUfiSms_CmtRespProc(&tCmtRsp);
zUfiMmi_SendSmsStatus();sc_cfg_set(NV_SMS_RECV_RESULT,"\x6f\x6b");}VOID 
zSms_RecvCmtiInd(UINT8*pDatabuf){char sms_Main_state[(0xce1+2272-0x15a3)]={
(0x22d0+498-0x24c2)};T_zSms_SmsIndexInd*smsIdxInd=(T_zSms_SmsIndexInd*)pDatabuf;
CHAR needSMS[(0x92b+6247-0x2160)]={(0xf38+2859-0x1a63)};sc_cfg_get(
NV_NEED_SUPPORT_SMS,needSMS,sizeof(needSMS));if((0x72f+604-0x98b)==strcmp(
needSMS,"\x6e\x6f")){printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}sc_cfg_get(NV_SMS_STATE,sms_Main_state,sizeof(sms_Main_state));if(
strcmp(sms_Main_state,"\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67")==
(0xad8+4602-0x1cd2)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x69\x52\x73\x70\x3a\x20\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67" "\n"
);return;}if((0x1ae6+2479-0x2495)==strncmp("\x53\x4d",smsIdxInd->storetype,
(0x45c+1038-0x868))){zUfiSms_SetSmsLocation(SMS_LOCATION_SIM);
zUfiSms_ChangeMainState(SMS_STATE_RECVING);zSms_SendZmgrReq(smsIdxInd->index);}
else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x69\x52\x73\x70\x20\x3a\x73\x74\x6f\x72\x65\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x6e\x6f\x74\x20\x53\x4d\x2e" "\n"
);}sc_cfg_set(NV_SMS_RECV_RESULT,"");}VOID zSms_RecvCdsInd(UINT8*pDatabuf){CHAR 
needSMS[(0x880+94-0x8ac)]={(0x15e4+815-0x1913)};sc_cfg_get(NV_NEED_SUPPORT_SMS,
needSMS,sizeof(needSMS));if((0xd1f+688-0xfcf)==strcmp(needSMS,"\x6e\x6f")){
printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}T_zSms_SmsInd tCmtRsp={(0xf0a+5105-0x22fb)};memcpy(&tCmtRsp,(
T_zSms_SmsInd*)pDatabuf,sizeof(T_zSms_SmsInd));zUfiSms_CdsRespProc(&tCmtRsp);
zUfiMmi_SendSmsStatus();sc_cfg_set(NV_SMS_RECV_RESULT,"\x6f\x6b");}VOID 
zSms_RecvCdsiInd(UINT8*pDatabuf){char sms_Main_state[(0xd6c+3402-0x1a98)]={
(0xc1a+2887-0x1761)};T_zSms_SmsIndexInd*smsIdxInd=(T_zSms_SmsIndexInd*)pDatabuf;
CHAR needSMS[(0x851+7677-0x261c)]={(0x453+222-0x531)};sc_cfg_get(
NV_NEED_SUPPORT_SMS,needSMS,sizeof(needSMS));if((0x92f+5250-0x1db1)==strcmp(
needSMS,"\x6e\x6f")){printf(
"\x5b\x53\x4d\x53\x5d\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x6d\x74\x52\x73\x70\x20\x6e\x65\x65\x64\x53\x4d\x53\x3d\x6e\x6f\x21"
);return;}sc_cfg_get(NV_SMS_STATE,sms_Main_state,sizeof(sms_Main_state));if(
strcmp(sms_Main_state,"\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67")==
(0x11bc+5292-0x2668)){printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x3a\x20\x73\x6d\x73\x5f\x64\x65\x6c\x69\x6e\x67" "\n"
);return;}if((0x403+8424-0x24eb)==strncmp("\x53\x4d",smsIdxInd->storetype,
(0x17d6+1793-0x1ed5))){zUfiSms_SetSmsLocation(SMS_LOCATION_SIM);
zUfiSms_ChangeMainState(SMS_STATE_RECVING);zSms_SendZmgrReq(smsIdxInd->index);}
else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x64\x73\x69\x52\x73\x70\x20\x3a\x73\x74\x6f\x72\x65\x20\x6c\x6f\x63\x61\x74\x69\x6f\x6e\x20\x6e\x6f\x74\x20\x53\x4d\x2e" "\n"
);}sc_cfg_set(NV_SMS_RECV_RESULT,"");}int zSms_SendCnmaReq(int ack_mode){
T_zSms_SmsAckReq ackReq={(0x1b2+9091-0x2535)};CHAR ackPduStr[(0x131d+321-0x142c)
]={(0x595+4060-0x1571)};ackReq.ackmode=ack_mode;if(ack_mode==
(0x1f75+1790-0x2671)){zUfiSms_EncodePdu_DeliverReport(ackPduStr,
(0x444+5508-0x18f5));memcpy(ackReq.pdu,ackPduStr,strlen(ackPduStr));
#if (0xd77+3169-0x19d8)
if(strlen(ackPduStr)<ZSMS_PDU_SIZE-(0x741+778-0xa4a)){memcpy(ackReq.pdu,
ackPduStr,strlen(ackPduStr));}else{at_print(LOG_DEBUG
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6e\x6d\x61\x52\x65\x71\x20\x70\x64\x75\x20\x74\x6f\x6f\x20\x6c\x6f\x6e\x67\x3a\x25\x73" "\n"
,ackPduStr);memcpy(ackReq.pdu,ackPduStr,ZSMS_PDU_SIZE-(0xcd4+1936-0x1462));}
#endif     
*(ackReq.pdu+strlen(ackPduStr))=ZSMS_CTRL_Z_CHAR;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x43\x6e\x6d\x61\x52\x65\x71\x2e\x20\x70\x64\x75\x3d\x20\x25\x73" "\n"
,ackReq.pdu);ackReq.length=strlen(ackPduStr)/(0x148+3298-0xe28);}zSms_SendMsg(
MSG_CMD_SMSACK_REQ,sizeof(T_zSms_SmsAckReq),&ackReq);sem_wait(&g_sms_sem_id);if(
g_smsOptRsp.result==(0x550+648-0x7d7)){return ZSMS_RESULT_OK;}else{return 
ZSMS_RESULT_ERROR;}}int zSms_SendZmgrReq(int index){T_zSms_ReadSmsReq readSmsReq
={(0x86a+4846-0x1b58)};iSmsIndex=index;printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x53\x65\x6e\x64\x5a\x6d\x67\x72\x52\x65\x71\x20\x47\x65\x74\x20\x69\x6e\x64\x65\x78\x3a\x25\x64\x2e" "\n"
,iSmsIndex);readSmsReq.index=index;zSms_SendMsg(MSG_CMD_READSMS_REQ,sizeof(
T_zSms_ReadSmsReq),&readSmsReq);return(0x72a+3661-0x1577);}VOID zSms_RecvZmgrRsp
(UINT8*pDatabuf){T_zSms_SmsInd tCmgrRsp={(0x14ea+1110-0x1940)};memcpy(&tCmgrRsp,
(T_zSms_SmsInd*)pDatabuf,sizeof(T_zSms_SmsInd));tCmgrRsp.index=iSmsIndex;
zUfiSms_ZmgrRespProc(&tCmgrRsp);zUfiMmi_SendSmsStatus();}VOID zSms_RecvZmgrOk(
UINT8*pDatabuf){T_zSms_optRsp smsOptRsp={(0x8f7+831-0xc36)};memcpy(&smsOptRsp,(
T_zSms_optRsp*)pDatabuf,sizeof(T_zSms_optRsp));if(smsOptRsp.result==
(0xfca+1299-0x14dc)){sc_cfg_set(NV_SMS_RECV_RESULT,"\x6f\x6b");}else{printf(
"\x5b\x53\x4d\x53\x5d\x20\x61\x74\x53\x6d\x73\x5f\x52\x65\x63\x76\x5a\x6d\x67\x72\x45\x72\x72\x20\x20\x53\x4d\x53\x20\x7a\x6d\x67\x72\x20\x69\x73\x20\x66\x61\x69\x6c" "\n"
);sc_cfg_set(NV_SMS_RECV_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(
SMS_STATE_RECVED);}}VOID zSms_RecvZpbicInd(UINT8*pDatabuf){T_zAt_ZpbicRes*ptPara
=ZUFI_NULL;if(pDatabuf==NULL){return;}ptPara=(T_zAt_ZpbicRes*)(pDatabuf);if((
(0x1cf2+707-0x1fb4)==ptPara->result)&&((0x6aa+3259-0x1365)==ptPara->opertype)){
CHAR needSms[(0x1346+3231-0x1fb3)]={(0x1b9a+2862-0x26c8)};sc_cfg_get(
NV_NEED_SUPPORT_SMS,needSms,sizeof(needSms));if((0x1393+3727-0x2222)!=strcmp(
needSms,"\x6e\x6f")){zSvr_Zpbic_Sms_Init();}}}VOID zSms_RecvCpmsInd(UINT8*
pDatabuf){T_zSms_CpmsInd*cpmsInd=(T_zSms_CpmsInd*)pDatabuf;CHAR strBuf[
(0x149c+2301-0x1d8f)]={(0x12f+9349-0x25b4)};int remainSpace=(0x20b+7049-0x1d94);
snprintf(strBuf,sizeof(strBuf),"\x25\x64",cpmsInd->total);sc_cfg_set(
ZTE_WMS_NVCONFIG_SIM_CARD_TOTAL,strBuf);sc_cfg_set(
ZTE_WMS_NVCONFIG_SIM_CAPABILITY,strBuf);memset(&strBuf,(0x1039+940-0x13e5),
(0x629+5275-0x1aba));snprintf(strBuf,sizeof(strBuf),"\x25\x64",cpmsInd->used);
sc_cfg_set(ZTE_WMS_NVCONFIG_SIM_CARD_USED,strBuf);remainSpace=cpmsInd->total-
cpmsInd->used;memset(&strBuf,(0x14+6703-0x1a43),(0x2319+829-0x264c));snprintf(
strBuf,sizeof(strBuf),"\x25\x64",remainSpace);sc_cfg_set(
ZTE_WMS_NVCONFIG_SIM_CARD_REMAIN,strBuf);sc_cfg_set(NV_SMS_STORE,"\x6f\x6b");}
#define AT_CMD_MAX (0xbbf+4150-0x1bb5)
#define ZAT_TAB_REPLACE                     ((unsigned char )(\
(0x19a9+378-0x1a27)))    
#define ZAT_NULL_FILL                       ((unsigned char )(\
(0x292+9088-0x2515)))    
#define ZAT_SPACE_REPLACE                   ((unsigned char )(\
(0xdc8+3458-0x1a4c)))    
#define ZAT_LF_REPLACE                      ((unsigned char )(\
(0x7e3+3234-0x138a)))    
#define ZAT_CR_REPLACE                      ((unsigned char )((0x1741+33-0x1668)\
))    
static void atBase_PreProcRes(char*pParaLine,int paraSize){signed long flg=
(0x1484+4550-0x264a);unsigned long i=(0x14b7+551-0x16de);unsigned long length=
(0x3dd+2180-0xc61);char*pSource=pParaLine;char*pDest=NULL;char*pStrDestMalloc=(
char*)malloc(AT_CMD_MAX);if(NULL==pStrDestMalloc){return;}memset(pStrDestMalloc,
(0x1cb2+626-0x1f24),AT_CMD_MAX);assert(pParaLine!=NULL);pDest=pStrDestMalloc;
length=strlen(pParaLine);if((length==(0x1f06+1542-0x250c))||(length>=AT_CMD_MAX)
){free(pStrDestMalloc);return;}for(i=(0x13c9+2875-0x1f04);(i<length)&&(pDest-
pStrDestMalloc<AT_CMD_MAX);i++){if(((char)(0x909+6316-0x2193))==*pSource){flg=(
(0x2174+146-0x2206)==flg)?(0x37f+7337-0x2027):(0x1b56+1375-0x20b5);if(
((char)(0x7e5+7446-0x24d9))==*(pSource+(0xbf5+2720-0x1694))){*pDest++=(char)
ZAT_NULL_FILL;}}else if((((char)(0x1702+3433-0x243f))==*pSource)&&(
(0x745+321-0x886)==flg)){*pDest++=((char)(0x138f+3778-0x2231));if(
((char)(0x31d+851-0x644))==*(pSource+(0xf3+3981-0x107f))){*pDest++=
((char)(0x7b2+7663-0x2568));}else if('\0'==*(pSource+(0xd7+2979-0xc79))){*pDest
++=(char)ZAT_NULL_FILL;}}else{if((((char)(0x10e5+5287-0x256c))==*pSource)&&(
(0x1a4+4168-0x11eb)==flg)){*pDest++=(char)ZAT_SPACE_REPLACE;}else if(('\t'==*
pSource)&&((0xc30+787-0xf42)==flg)){*pDest++=(char)ZAT_TAB_REPLACE;}else if((
'\n'==*pSource)&&((0x178a+3387-0x24c4)==flg)){*pDest++=(char)ZAT_LF_REPLACE;}
else if(('\r'==*pSource)&&((0x58d+2626-0xfce)==flg)){*pDest++=(char)
ZAT_CR_REPLACE;}else{*pDest++=*pSource;}}pSource++;}memset(pParaLine,
(0xb27+5118-0x1f25),paraSize);strncpy(pParaLine,pStrDestMalloc,paraSize-
(0x21fb+1222-0x26c0));free(pStrDestMalloc);}VOID zSms_RecvCscaInd(UINT8*pDatabuf
){T_zSms_CscaInd cscaInd={(0x4fd+8010-0x2447)};
#if (0x1329+767-0x1627)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x7a\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x73\x63\x61\x49\x6e\x64\x20\x64\x61\x74\x61\x62\x75\x66\x3a\x25\x73" "\n"
,pDatabuf);
#endif
atBase_PreProcRes(pDatabuf,strlen(pDatabuf));sscanf(pDatabuf,
"\x25\x32\x31\x73\x20\x25\x32\x31\x73",cscaInd.sca,cscaInd.tosca);
#if (0x23b+3779-0x10fd)
printf(
"\x5b\x53\x4d\x53\x63\x6f\x72\x65\x6d\x5d\x7a\x53\x6d\x73\x5f\x52\x65\x63\x76\x43\x73\x63\x61\x49\x6e\x64\x20\x73\x63\x61\x3a\x25\x73\x2c\x20\x74\x6f\x73\x63\x61\x25\x73" "\n"
,cscaInd.sca,cscaInd.tosca);
#endif
sc_cfg_set(NV_SMS_CENTER_NUM,cscaInd.sca);zUfiSms_SetScaPara(cscaInd.sca);}VOID 
zSms_RecvZmglInd(UINT8*pDatabuf){zUfiSms_CmglRespProc((T_zSms_SmsInd*)pDatabuf);
}int zSms_SendSmsInitReq(VOID){zSms_SendMsg(MSG_CMD_SMSINIT_REQ,
(0xff1+3130-0x1c2b),NULL);return(0x9e4+1775-0x10d3);}VOID zSms_initAtOk(VOID){
T_zUfiSms_StatusInfo tStatus={(0x7ac+6644-0x21a0)};sc_cfg_set(NV_SMS_STORE,
"\x6f\x6b");sc_cfg_set(NV_SMS_LOAD_RESULT,"\x6f\x6b");tStatus.cmd_status=
WMS_CMD_SUCCESS;tStatus.cmd=WMS_SMS_CMD_INIT;(void)zUfiSms_SetCmdStatus(&tStatus
);zUfiSms_ChangeMainState(SMS_STATE_LOADED);}VOID zSms_initAtErr(VOID){
T_zUfiSms_StatusInfo tStatus={(0x12f2+1585-0x1923)};sc_cfg_set(
NV_SMS_LOAD_RESULT,"\x66\x61\x69\x6c");zUfiSms_ChangeMainState(SMS_STATE_LOADED)
;tStatus.cmd_status=WMS_CMD_FAILED;tStatus.cmd=WMS_SMS_CMD_INIT;(void)
zUfiSms_SetCmdStatus(&tStatus);}VOID zSms_RecvSmsInitRst(UINT8*pDatabuf){memcpy(
&g_smsOptRsp,pDatabuf,sizeof(T_zSms_optRsp));if(g_smsOptRsp.result==
(0xf45+5632-0x2544)){zSms_initAtOk();}else{zSms_initAtErr();}}UINT8 
zSms_SmsMsgCreat(VOID){g_zSms_MsqId=msgget(MODULE_ID_SMS,IPC_CREAT|
(0xc06+6346-0x2350));if(g_zSms_MsqId==-(0x1107+792-0x141e)){return ZUFI_FAIL;}
g_zSms_LocalMsqId=msgget(MODULE_ID_SMS_LOCAL,IPC_CREAT|(0x1f8+1137-0x4e9));if(
g_zSms_LocalMsqId==-(0xbe0+5617-0x21d0)){return ZUFI_FAIL;}sem_init(&
g_sms_sem_id,(0x16fa+2141-0x1f57),(0x15ad+2273-0x1e8e));return ZUFI_SUCC;}void 
zSms_HandleAtctlLocalMsg(MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x73\x6d\x73\x20\x6c\x6f\x63\x61\x6c\x20\x72\x65\x63\x76\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_ZPBIC_IND:
zSms_RecvZpbicInd(ptMsgBuf->aucDataBuf);break;case MSG_CMD_ZMGL_IND:
zSms_RecvZmglInd(ptMsgBuf->aucDataBuf);break;case MSG_CMD_NEWSMS_STATUS_IND:
zSms_RecvCdsInd(ptMsgBuf->aucDataBuf);break;case MSG_CMD_NEWSMS_IND:
zSms_RecvCmtInd(ptMsgBuf->aucDataBuf);default:break;}}VOID zSms_HandleAtctlMsg(
MSG_BUF*ptMsgBuf){assert(ptMsgBuf!=NULL);printf(
"\x73\x6d\x73\x20\x72\x65\x63\x76\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_SENDSMS_RSP:case 
MSG_CMD_DELSMS_RSP:case MSG_CMD_STORAGE_CAP_RSP:case MSG_CMD_MODIFY_TAG_RSP:case
 MSG_CMD_NOTIFY_SET_RSP:case MSG_CMD_SCA_SET_RSP:case MSG_CMD_SMSACK_RSP:{memcpy
(&g_smsOptRsp,ptMsgBuf->aucDataBuf,sizeof(T_zSms_optRsp));sem_post(&g_sms_sem_id
);}break;case MSG_CMD_CPMS_IND:zSms_RecvCpmsInd(ptMsgBuf->aucDataBuf);break;case
 MSG_CMD_CSCA_IND:zSms_RecvCscaInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_ZMGR_IND:zSms_RecvZmgrRsp(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_READSMS_RSP:zSms_RecvZmgrOk(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_SMSINIT_RSP:zSms_RecvSmsInitRst(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_NEWSMS_INDEX_IND:zSms_RecvCmtiInd(ptMsgBuf->aucDataBuf);break;case 
MSG_CMD_NEWSMS_STATUS_INDEX_IND:zSms_RecvCdsiInd(ptMsgBuf->aucDataBuf);break;
case MSG_CMD_NEWSMS_IND:case MSG_CMD_ZPBIC_IND:case MSG_CMD_ZMGL_IND:case 
MSG_CMD_NEWSMS_STATUS_IND:ipc_send_message(MODULE_ID_SMS,MODULE_ID_SMS_LOCAL,
ptMsgBuf->usMsgCmd,ptMsgBuf->usDataLen,(unsigned char*)ptMsgBuf->aucDataBuf,
(0x110+4762-0x13aa));break;default:break;}}VOID zSms_HandleResetToFactory(){CHAR
 clearSms[(0x173b+915-0x1a9c)]={(0xec3+710-0x1189)};sc_cfg_get(
NV_CLEAR_SMS_WHEN_RESTORE,clearSms,sizeof(clearSms));printf(
"\x61\x74\x57\x65\x62\x5f\x52\x65\x73\x74\x6f\x72\x65\x46\x61\x63\x74\x6f\x72\x79\x53\x65\x74\x74\x69\x6e\x67\x20\x65\x6e\x74\x65\x72\x65\x64\x21\x20" "\n"
);printf(
"\x63\x6c\x65\x61\x72\x5f\x73\x6d\x73\x5f\x77\x68\x65\x6e\x5f\x72\x65\x73\x74\x6f\x72\x65\x3d\x25\x73\x20" "\n"
,clearSms);if(strcmp(clearSms,"\x79\x65\x73")==(0xbd7+1393-0x1148)){printf(
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x72\x6f\x70\x41\x6c\x6c\x54\x61\x62\x6c\x65\x20\x65\x6e\x74\x65\x72\x65\x64\x21\x20" "\n"
);zUfiSms_DropAllTable();}else{printf(
"\x7a\x55\x66\x69\x53\x6d\x73\x5f\x44\x72\x6f\x70\x41\x6c\x6c\x54\x61\x62\x6c\x65\x45\x78\x63\x65\x70\x74\x53\x6d\x73\x20\x65\x6e\x74\x65\x72\x65\x64\x21\x20" "\n"
);zUfiSms_DropAllTableExceptSms();}ipc_send_message(MODULE_ID_SMS,
MODULE_ID_MAIN_CTRL,MSG_CMD_RESET_RSP,(0x82+3339-0xd8d),NULL,
(0x163f+1678-0x1ccd));}void zSms_HandleMainCtrlMsg(MSG_BUF*ptMsgBuf){assert(
ptMsgBuf!=NULL);printf(
"\x73\x6d\x73\x20\x72\x65\x63\x76\x20\x6d\x61\x69\x6e\x20\x63\x74\x72\x6c\x20\x6d\x73\x67\x20\x63\x6d\x64\x3a\x25\x64" "\n"
,ptMsgBuf->usMsgCmd);switch(ptMsgBuf->usMsgCmd){case MSG_CMD_RESET_NOTIFY:
zSms_HandleResetToFactory(ptMsgBuf->aucDataBuf);break;default:break;}}void 
sms_msg_thread_proc(void*arg){int iRet=(0x3db+214-0x4b1);MSG_BUF stMsg={
(0x1167+2156-0x19d3)};int msgSize=sizeof(MSG_BUF)-sizeof(long);int queueId=*((
int*)arg);prctl(PR_SET_NAME,"\x73\x6d\x73\x5f\x6c\x6f\x63\x61\x6c",
(0x7e4+4429-0x1931),(0xc72+3859-0x1b85),(0x1679+2536-0x2061));while(
(0x664+1294-0xb71)){iRet=(0x422+594-0x674);memset(&stMsg,(0x9da+3010-0x159c),
sizeof(MSG_BUF));iRet=msgrcv(queueId,&stMsg,msgSize,(0x1075+3027-0x1c48),
(0x177a+315-0x18b5));if(iRet>=(0x82f+1387-0xd9a)){switch(stMsg.src_id){case 
MODULE_ID_WEB_CGI:{zSms_HandleWebMsg(&stMsg);break;}case MODULE_ID_AT_CTL:{
zSms_HandleAtctlMsg(&stMsg);break;}case MODULE_ID_SMS:{zSms_HandleWebMsg(&stMsg)
;zSms_HandleAtctlLocalMsg(&stMsg);break;}case MODULE_ID_MAIN_CTRL:{
zSms_HandleMainCtrlMsg(&stMsg);break;}default:{break;}}}else{at_print(AT_DEBUG,
"\x65\x72\x72\x6e\x6f\x20\x3d\x20\x25\x64\x2c\x20\x65\x72\x72\x6d\x73\x67\x20\x3d\x20\x25\x73" "\n"
,errno,strerror(errno));}}}int sms_main(int argc,char*argv[]){pthread_t 
recv_thread_tid=(0xc64+2661-0x16c9);MSG_BUF msgBuf={(0x238c+105-0x23f5)};CHAR 
needSMS[(0x131+6447-0x1a2e)]={(0xdf7+1732-0x14bb)};prctl(PR_SET_NAME,
"\x73\x6d\x73\x5f\x6d\x61\x69\x6e",(0x1025+1489-0x15f6),(0x950+5664-0x1f70),
(0x808+1670-0xe8e));loglevel_init();sc_cfg_get(NV_NEED_SUPPORT_SMS,needSMS,
sizeof(needSMS));if((0x1c73+1667-0x22f6)!=strcmp(needSMS,"\x6e\x6f")){
zUfiSms_InitDb();zUfiSms_CfgSmsNvInit();zUfiMmi_SendSmsStatus();zSms_SmsMsgCreat
();}else{return-(0x1e69+1665-0x24e9);}printf(
"\x73\x6d\x73\x20\x61\x70\x70\x20\x69\x6e\x69\x74\x20\x66\x69\x6e\x69\x73\x68\x65\x64\x2c\x20\x77\x69\x6c\x6c\x20\x74\x6f\x20\x72\x65\x63\x65\x69\x76\x65\x20\x6d\x73\x67\x2c\x20\x6d\x73\x67\x69\x64\x3a\x25\x64" "\n"
,g_zSms_MsqId);if(pthread_create(&recv_thread_tid,NULL,sms_msg_thread_proc,(void
*)(&g_zSms_LocalMsqId))==-(0x274+1972-0xa27)){assert((0x581+4461-0x16ee));}
sms_msg_thread_proc(&g_zSms_MsqId);return(0x1df4+1459-0x23a7);}
