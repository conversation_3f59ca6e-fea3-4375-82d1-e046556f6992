#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************
include $(zte_app_mak)
#*******************************************************************************
# execute
#*******************************************************************************
EXEC    = uicc_agt_svr

#*******************************************************************************
# objects
#*******************************************************************************
OBJS    = uicc_agt_svr.o
		  
#*******************************************************************************
# include path
#*******************************************************************************
CFLAGS += -I. \
		  -I./inc \
		  -I$(zte_app_path)/include \
		  -I$(zte_lib_path)/libzte_vsim/inc \
		  -Wall \
		  -O -Dlinux=1 -DHIGH_SPEED=1 \
		  -DUICC_AGT_ITO_CLOUD_VSIM \
		  -g -Werror=implicit-function-declaration
		  
#*******************************************************************************
# library
#*******************************************************************************
LDLIBS += -lpthread
LDLIBS += -lzte_vsim -L$(zte_lib_path)/libzte_vsim
LDLIBS += -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS += -lsoft_timer_sc -L$(zte_lib_path)/libsoft_timer
LDLIBS += -lnvram_sc -L$(zte_lib_path)/libnvram

		   

#*******************************************************************************
# library path
#*******************************************************************************

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $(OBJS) -Wl,--start-group $(LDLIBS) -Wl,--end-group
	@cp $@ $@.elf
	
romfs:
	$(ROMFSINST)  /bin/$(EXEC)
	$(ROMFSINST) vSim.bin  /etc_ro/vSim.bin
	
clean:
	-rm -f $(EXEC) *.elf *.gdb *.o

