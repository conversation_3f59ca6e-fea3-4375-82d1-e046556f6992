
ls /bin/ui*
/bin/uicc_agt_svr
~ # /bin/uicc_agt_svr
/bin/uicc_agt_svr
@vsim@ open rpm0 fd: 5 er_no: 0
@vsim@ open userdata/vSim.bin fd: 7 er_no: 1
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =10 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 2fe2
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =9 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f07
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =32 bRecNum = 2
vSim zVcard_GetFileAttr error  wFid = 2f00
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f78
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =6 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6fd9
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =9 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f07
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =10 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 2fe2
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f78
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f43
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f43
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =28 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f40
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =28 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 6f40
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =40 bRecNum = 2
vSim zVcard_GetFileAttr error  wFid = 6f42
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =40 bRecNum = 2
vSim zVcard_GetFileAttr error  wFid = 6f42
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =176 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 6f3c
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =176 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 6f3c
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =30 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 6f47
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =30 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 6f47
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =76 bRecNum = 2
vSim zVcard_GetFileAttr error  wFid = 4f30
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =28 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f39
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =28 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f3a
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =1 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f31
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =1 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f32
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =13 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 4f4a
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =14 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f33
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =14 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f34
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =15 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f5a
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =15 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f5b
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f41
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f42
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =4 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f51
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =4 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f52
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =20 bRecNum = 2
vSim zVcard_GetFileAttr error  wFid = 4f4b
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =24 bRecNum = 4
vSim zVcard_GetFileAttr error  wFid = 4f4c
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f61
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 4f62
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =40 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 4f71
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =40 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 4f72
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =4 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 4f22
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 4f23
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =2 bRecNum = 1
vSim zVcard_GetFileAttr error  wFid = 4f24
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =28 bRecNum = 10
vSim zVcard_GetFileAttr error  wFid = 6f3a
vSim zVcard_IsValidFid wFid = 142a
vSim bRecLen =13 bRecNum = 5
vSim zVcard_GetFileAttr error  wFid = 6f4a
@vsim@ usim_reset_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 3f 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=31
@vsim@ apdu_rsp->data_rsp: 62 1d 82 02 78 21 83 02 3f 00 a5 03 80 01 71 8a 01 05 8b 03 2f 06 03 c6 06 90 01 00 83 01 01
@vsim@ usim_apdu_proc send 1028
@vsim@ atr_data: 3b 9f 94 80 1f c7 80 31 e0 73 fe 21 13 57 86 8d 0b 86 98 6a 18 86 ff 10 94 7b 60 00 00 00 00 00
@vsim@ usim_getatr_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 2f e2
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 2f e2 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=10
@vsim@ apdu_rsp->data_rsp: 98 68 00 00 05 02 00 81 70 92
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 2f 05
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=80,ins=10,p1=0,p2=0,lc=29
@vsim@ apdu_req->data_req: 0b e1 8f 63 11 00 00 0c 81 00 ff 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 2f 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 20 02 83 02 2f 00 8a 01 05 8b 03 2f 06 03 80 02 00 40 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 2f 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 20 02 83 02 2f 00 8a 01 05 8b 03 2f 06 03 80 02 00 40 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=32
@vsim@ apdu_rsp->data_rsp: 61 1d 4f 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 50 09 55 6e 69 76 65 72 53 49 4d ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 2f 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 20 02 83 02 2f 00 8a 01 05 8b 03 2f 06 03 80 02 00 40 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=32
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=4,p2=4,lc=16
@vsim@ apdu_req->data_req: a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=20,p1=0,p2=1,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=63,sw2=c3,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=2c,p1=0,p2=1,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=63,sw2=ca,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=8,p2=4,lc=2
@vsim@ apdu_req->data_req: 7f ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=20,p1=0,p2=81,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=63,sw2=c3,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=2c,p1=0,p2=81,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=63,sw2=ca,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 05
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 56
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f ad
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 62
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 73
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 08
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 09
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=9,p2=4,lc=4
@vsim@ apdu_req->data_req: 5f 3b 4f 20
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 7f ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 5f 3b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=26
@vsim@ apdu_rsp->data_rsp: 62 18 82 02 78 21 83 02 5f 3b 8a 01 05 8b 03 2f 06 03 c6 06 90 01 00 83 01 01
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 20
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 52
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 04 0a 83 02 4f 52 8a 01 05 8b 03 2f 06 03 80 02 00 28 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=67,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=8,p2=4,lc=4
@vsim@ apdu_req->data_req: 7f ff 6f 5c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 7f ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 5c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 5b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f dc
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f e3
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 43
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 6f 43 8a 01 05 8b 03 2f 06 03 80 02 00 02 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 78
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 6f 78 8a 01 05 8b 03 2f 06 03 80 02 00 02 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 08
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f b7
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 28 02 83 02 6f 42 8a 01 05 8b 03 2f 06 03 80 02 00 50 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=40
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff e0 0b 81 31 18 83 00 40 f3 ff ff 58 41 08 91 68 51 08 20 05 05 f0 41 00 9f 00 00 ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f c6
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f c5
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 07
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 6f 07 8a 01 05 8b 03 2f 06 03 80 02 00 09 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=9
@vsim@ apdu_rsp->data_rsp: 08 09 10 10 32 54 76 98 10
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 7e
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=80,ins=f2,p1=1,p2=c,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f e4
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f d9
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 6f d9 8a 01 05 8b 03 2f 06 03 80 02 00 06 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=6
@vsim@ apdu_rsp->data_rsp: 64 f0 80 64 f0 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 43
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: 62 16 82 02 01 21 83 02 6f 43 8a 01 05 8b 03 2f 06 03 80 02 00 02 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b0,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=176
@vsim@ apdu_req->data_req: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=176
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=176
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=176
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=176
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 b0 05 83 02 6f 3c 8a 01 05 8b 03 2f 06 03 80 02 03 70 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=176
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=30
@vsim@ apdu_req->data_req: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=30
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=30
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=30
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=30
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 47
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1e 05 83 02 6f 47 8a 01 05 8b 03 2f 06 03 80 02 00 96 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=30
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f b7
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 4b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 4b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 3b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=8,p2=4,lc=6
@vsim@ apdu_req->data_req: 7f 10 5f 3a 4f 30
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 4c 02 83 02 4f 30 8a 01 05 8b 03 2f 06 03 80 02 00 98 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 30
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 4c 02 83 02 4f 30 8a 01 05 8b 03 2f 06 03 80 02 00 98 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=76
@vsim@ apdu_req->data_req: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 30
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 4c 02 83 02 4f 30 8a 01 05 8b 03 2f 06 03 80 02 00 98 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=76
@vsim@ apdu_rsp->data_rsp: a8 23 c0 03 4f 39 01 c4 03 4f 5a 05 c6 03 4f 51 04 c5 03 4f 41 03 c9 03 4f 61 08 c1 03 4f 31 06 c3 03 4f 33 36 a9 05 ca 03 4f 71 09 aa 0f c2 03 4f 4a 0a c7 03 4f 4b 0b c8 03 4f 4c 0c ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 30
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 4c 02 83 02 4f 30 8a 01 05 8b 03 2f 06 03 80 02 00 98 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=76
@vsim@ apdu_rsp->data_rsp: a8 23 c0 03 4f 3a 11 c4 03 4f 5b 15 c6 03 4f 52 14 c5 03 4f 42 13 c9 03 4f 62 18 c1 03 4f 32 16 c3 03 4f 34 37 a9 05 ca 03 4f 72 19 aa 0f c2 03 4f 4a 0a c7 03 4f 4b 0b c8 03 4f 4c 0c ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=2
@vsim@ apdu_req->data_req: 00 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 41
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 41 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=2
@vsim@ apdu_req->data_req: 00 00
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 42
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 02 0a 83 02 4f 42 8a 01 05 8b 03 2f 06 03 80 02 00 14 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=2
@vsim@ apdu_rsp->data_rsp: 00 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=1
@vsim@ apdu_req->data_req: ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 31
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 31 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=1
@vsim@ apdu_req->data_req: ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 32
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 01 0a 83 02 4f 32 8a 01 05 8b 03 2f 06 03 80 02 00 0a 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=1
@vsim@ apdu_rsp->data_rsp: ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=28
@vsim@ apdu_req->data_req: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 39
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 39 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=28
@vsim@ apdu_req->data_req: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=6,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=7,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=8,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=9,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 3a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 0a 83 02 4f 3a 8a 01 05 8b 03 2f 06 03 80 02 01 18 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=a,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=13
@vsim@ apdu_req->data_req: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=13
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=13
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=13
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=13
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4a
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 0d 05 83 02 4f 4a 8a 01 05 8b 03 2f 06 03 80 02 00 41 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=5,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=13
@vsim@ apdu_rsp->data_rsp: 00 ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a2,p1=1,p2=4,lc=24
@vsim@ apdu_req->data_req: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6d,sw2=0,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=2,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=3,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 4f 4c
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 18 04 83 02 4f 4c 8a 01 05 8b 03 2f 06 03 80 02 00 60 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=4,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=24
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=8,p2=4,lc=4
@vsim@ apdu_req->data_req: 7f ff 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 7f ff
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 38
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 40
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 01 83 02 6f 40 8a 01 05 8b 03 2f 06 03 80 02 00 1c 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 40
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=27
@vsim@ apdu_rsp->data_rsp: 62 19 82 05 42 21 00 1c 01 83 02 6f 40 8a 01 05 8b 03 2f 06 03 80 02 00 1c 88 00
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=b2,p1=1,p2=4,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=28
@vsim@ apdu_rsp->data_rsp: ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff ff
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 7b
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=80,ins=f2,p1=0,p2=0,lc=0
@vsim@ apdu_req->data_req:
@vsim@ apdu_rsp->r_apdu_ptr: sw1=90,sw2=0,luicc=47
@vsim@ apdu_rsp->data_rsp: 62 2d 82 02 78 21 83 02 7f ff 84 10 a0 00 00 00 87 10 02 ff 86 ff ff 89 ff ff ff ff 8a 01 05 8b 03 2f 06 03 c6 09 90 01 00 83 01 01 83 01 81
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 60
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
@vsim@ apdu_req->c_apdu: cla=0,ins=a4,p1=0,p2=4,lc=2
@vsim@ apdu_req->data_req: 6f 61
@vsim@ apdu_rsp->r_apdu_ptr: sw1=6a,sw2=82,luicc=0
@vsim@ apdu_rsp->data_rsp:
@vsim@ usim_apdu_proc send 1028
