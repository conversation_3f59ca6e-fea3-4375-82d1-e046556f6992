1、原生操作方法

```shell

测试下发at顺序：

at+cfun=1//实体卡开机?

at+cgact=1,1

at+zgact=1,1//卡1的默认承载cid=1 拨号， 之后可以ping包

at+vsimdebug=0 启动vsim应用

at+shell=echo 1 > /proc/vsim_switch   切at通道到云卡

at+cgsn=865662072774566 //设置云卡IMEI，这个若是云卡获取的，则将此流程添加到代码

at+shell=echo 0 > /proc/vsim_switch   切at通道到实体卡



at+vsimdebug=31  // 等同Vsim_init，切换到vsim这一待

at+shell=echo 0 > /proc/vsim_switch   切at通道到实体卡

at+zgact=0,1?

at+shell=echo 1 > /proc/vsim_switch   切at通道到云卡（注: 目前两待不支持同时拨号）

at+cgact=1,5

at+zgact=1,5//卡2的默认承载cid=5 拨号，可以ping包，如果ping通，则这一待可以正常工作了。

at+vsimdebug=41 //关闭vsim这一带


注：限制实体卡cid=1~4，云卡cid=5~8
    云卡这一待 可以发下AT+CGDCONT? 去看下是否默认设置了承载，不然不会发起鉴权请求(uicc_agt_svr收不到鉴权码)
    或者拿到云卡卡设置好apn AT+CGDCONT=5,"IP","cmnet"
```


2. 联调


2.1 push

```shell
adb shell "mount -o remount,rw /"
adb push qrzl_ito_cloud_vsim_app /bin/
adb shell "chmod 777 /bin/qrzl_ito_cloud_vsim_app"

```

2.2 插外部卡种子卡拨号

打开 UeTester 发送AT
```
AT+CFUN=1

AT+CGACT=1,1

AT+ZGACT=1,1

```

adb shell 进入设备

``` shell
# 查看是否获取到ip
ifconfig 

# 运行程序拿卡
/bin/qrzl_ito_cloud_vsim_app

```

等拿到云卡（）
打开 UeTester 发送AT
AT+VSIMDEBUG=0
AT+VSIMDEBUG=31


查询是否鉴权完 是否注册到网络

AT+COPS?

at+shell=echo 0 > /proc/vsim_switch   切at通道到实体卡

at+zgact=0,1?

at+shell=echo 1 > /proc/vsim_switch   切at通道到云卡（注: 目前两待不支持同时拨号）

at+cgact=1,5

at+zgact=1,5//卡2的默认承载cid=5 拨号，可以ping包，如果ping通，则这一待可以正常工作了。

at+vsimdebug=41 //关闭vsim这一带
