/*******************************************************************************
 * Copyright by ZTE Corporation.
 *
 * File Name:    uicc_agt_local.c
 * File Mark:    
 * Description:  uicc ���������߳�
 * Others:        
 * Version:       V1.0
 * Author:        
 * Date:          
 * History 1:      
 *     Date: 
 *     Version:
 *     Author: 
 *     Modification:  
 * History 2: 
********************************************************************************/
 /****************************************************************************
* 	                                           Include files
****************************************************************************/
#include "vsim.h"
#include <linux/uicc_agt_client.h>
#include "../include/softap_api.h"  // 包含消息定义和IPC函数
#include <pthread.h>  // 线程支持
#include <time.h>     // 时间函数
#include <signal.h>   // 信号处理
#include <sys/msg.h>  // 消息队列
#include <unistd.h>   // usleep


/****************************************************************************
* 	                                           Local Macros
****************************************************************************/
#define VSIM_BIN_AUTH_LEN 520

/****************************************************************************
* 	                                           Local Types
****************************************************************************/

/****************************************************************************
* 	                                           Local Constants
****************************************************************************/

/****************************************************************************
* 	                                           Local Function Prototypes
****************************************************************************/

/****************************************************************************
* 	                                          Global Constants
****************************************************************************/

/****************************************************************************
* 	                                          Global Variables
****************************************************************************/
int g_fd = -1;

// IOT云VSIM相关变量
#ifdef UICC_AGT_ITO_CLOUD_VSIM
static int g_uicc_agt_msq = -1;  // qrzl_app消息队列ID
static int g_waiting_auth_response = 0;  // 是否正在等待鉴权响应
static char g_auth_response_data[512] = {0};  // 鉴权响应数据
static int g_auth_response_len = 0;  // 鉴权响应数据长度
static pthread_mutex_t g_auth_mutex = PTHREAD_MUTEX_INITIALIZER;  // 鉴权互斥锁
static pthread_cond_t g_auth_cond = PTHREAD_COND_INITIALIZER;  // 鉴权条件变量
static pthread_t g_msg_thread;  // 消息处理线程
static int g_msg_thread_running = 0;  // 消息线程运行标志

static int start_msg_thread(void);
static void stop_msg_thread(void);
/**
 * 信号处理函数
 */
static void signal_handler(int sig)
{
    printf("@vsim@ 收到信号 %d，准备退出\n", sig);
    stop_msg_thread();
    if (g_fd >= 0) {
        close(g_fd);
    }
    exit(0);
}
#endif

/****************************************************************************
* 	                                          Global Function Prototypes
****************************************************************************/

/****************************************************************************
* 	                                          Function Definitions
****************************************************************************/

#ifdef UICC_AGT_ITO_CLOUD_VSIM
/**
 * 创建消息队列
 */
static int uicc_agt_create_msg_queue(int module_id)
{
    int msq_id = msgget(module_id, IPC_CREAT | 0600);
    if(msq_id == -1) {
        printf("@vsim@ 创建消息队列失败 module_id=%d, errno=%d\n", module_id, errno);
    }
    return msq_id;
}

/**
 * 发送消息 - 使用 ipc_send_message 替代传统的 msgsnd
 */
static ssize_t uicc_agt_msg_send(int msqid, const void* msgp, size_t msgsz)
{
    // 将传统的消息队列调用转换为 IPC 消息发送
    // msgp 应该是 MSG_BUF 结构体指针
    const MSG_BUF* msg_buf = (const MSG_BUF*)msgp;

    if (!msg_buf) {
        printf("@vsim@ uicc_agt_msg_send: 无效的消息指针\n");
        return -1;
    }

    printf("@vsim@ uicc_agt_msg_send: 准备发送IPC消息\n");
    printf("@vsim@ 消息内容: %s\n", (char*)msg_buf->aucDataBuf);
    printf("@vsim@ 数据长度: %d\n", msg_buf->usDataLen);

    // 使用 ipc_send_message 发送消息
    // 参数：源模块ID, 目标模块ID, 消息ID, 数据长度, 数据指针, 标志
    int ret = ipc_send_message(MODULE_ID_UICC_AGT_SVR, MODULE_ID_QRZL_APP,
                              MSG_CMD_QRZL_APP_ITO_VSIM_AUTH,
                              msg_buf->usDataLen,
                              (unsigned char*)msg_buf->aucDataBuf,
                              0);

    if (ret == 0) {
        printf("@vsim@ uicc_agt_msg_send: IPC消息发送成功，数据长度=%d，消息大小=%zu\n",
               msg_buf->usDataLen, msgsz);
        return msgsz; // 返回发送的字节数
    } else {
        printf("@vsim@ uicc_agt_msg_send: IPC消息发送失败，错误码=%d\n", ret);
        return -1;
    }
}

/**
 * 接收消息
 */
static ssize_t uicc_agt_msg_recv(int msqid, void* msgp, size_t msgsz, long msgtype, int msgflag)
{
    return msgrcv(msqid, msgp, msgsz, msgtype, msgflag);
}

/**
 * 将十六进制字符串转换为二进制数据
 * @param hex_str 十六进制字符串
 * @param bin_data 输出的二进制数据
 * @param max_len 二进制数据缓冲区最大长度
 * @return 转换后的二进制数据长度，失败返回-1
 */
static int hex_str_to_bin(const char *hex_str, uint8_t *bin_data, int max_len)
{
    if (!hex_str || !bin_data || max_len <= 0) {
        return -1;
    }

    int hex_len = strlen(hex_str);
    if (hex_len % 2 != 0) {
        printf("@vsim@ 十六进制字符串长度必须为偶数: %d\n", hex_len);
        return -1;
    }

    int bin_len = hex_len / 2;
    if (bin_len > max_len) {
        printf("@vsim@ 十六进制字符串太长，将被截断: %d -> %d\n", bin_len, max_len);
        bin_len = max_len;
    }

    int i;
    for (i = 0; i < bin_len; i++) {
        int value;
        if (sscanf(hex_str + i*2, "%02x", &value) != 1) {
            printf("@vsim@ 十六进制转换失败: %s\n", hex_str + i*2);
            return -1;
        }
        bin_data[i] = (uint8_t)value;
    }

    return bin_len;
}

/**
 * 判断是否为鉴权APDU
 * @param apdu_req APDU请求结构
 * @return 1-是鉴权APDU, 0-不是鉴权APDU
 */
static int is_auth_apdu(T_UICC_TRANSPORT_APDU_REQ_MSG *apdu_req)
{
    if (!apdu_req) return 0;

    uint8_t cla = apdu_req->c_apdu.cla;
    uint8_t ins = apdu_req->c_apdu.ins;

    // 检查鉴权相关的命令
    if (ins == 0x88) {
        // AUTHENTICATE 命令
        printf("@vsim@ 检测到AUTHENTICATE命令 CLA=0x%02X, INS=0x%02X\n", cla, ins);
        return 1;
    }

    // 可以根据需要添加更多鉴权APDU的判断条件
    // if (cla == 0x80) {
    //     // CLA=0x80 通常用于安全相关命令
    //     printf("@vsim@ 检测到安全命令 CLA=0x80, INS=0x%02X\n", ins);
    //     return 1;
    // }

    return 0;
}

/**
 * 初始化uicc_agt_app消息队列连接
 */
static void init_uicc_agt_app_connection(void)
{
    if (g_uicc_agt_msq == -1) {
        g_uicc_agt_msq = uicc_agt_create_msg_queue(MODULE_ID_UICC_AGT_SVR);
        if (g_uicc_agt_msq < 0) {
            printf("@vsim@ 创建消息队列失败\n");
        } else {
            printf("@vsim@ 消息队列创建成功，ID: %d\n", g_uicc_agt_msq);

            // 启动消息处理线程
            if (start_msg_thread() < 0) {
                printf("@vsim@ 启动消息处理线程失败\n");
            }
        }
    }
}

/**
 * 处理来自qrzl_app的鉴权响应消息
 * @param msg_buf 消息缓冲区
 */
static void handle_auth_response(MSG_BUF *msg_buf)
{
    pthread_mutex_lock(&g_auth_mutex);

    if (g_waiting_auth_response) {
        // 复制响应数据
        g_auth_response_len = msg_buf->usDataLen;
        if (g_auth_response_len > sizeof(g_auth_response_data) - 1) {
            g_auth_response_len = sizeof(g_auth_response_data) - 1;
        }
        memcpy(g_auth_response_data, msg_buf->aucDataBuf, g_auth_response_len);
        g_auth_response_data[g_auth_response_len] = '\0';

        printf("@vsim@ 收到鉴权响应，长度: %d\n", g_auth_response_len);

        // 通知等待的线程
        g_waiting_auth_response = 0;
        pthread_cond_signal(&g_auth_cond);
    }

    pthread_mutex_unlock(&g_auth_mutex);
}

/**
 * 消息处理线程函数
 * 专门处理来自qrzl_app的消息
 */
static void* msg_thread_func(void* arg)
{
    MSG_BUF msg;
    int ret;

    printf("@vsim@ 消息处理线程启动\n");

    while (g_msg_thread_running) {
        memset(&msg, 0x00, sizeof(MSG_BUF));

        // // 接收消息，非阻塞模式
        // ret = uicc_agt_msg_recv(g_uicc_agt_msq, &msg, sizeof(MSG_BUF) - sizeof(long), 0, IPC_NOWAIT);
        // if (ret > 0) {
        //     printf("@vsim@ 收到消息: cmd=0x%x, src=%d, dst=%d\n",
        //            msg.usMsgCmd, msg.src_id, msg.dst_id);

        //     switch (msg.usMsgCmd) {
        //         case MSG_CMD_UICC_AGT_SVR_VSIM_AUTH_RSP:
        //             handle_auth_response(&msg);
        //             break;
        //         default:
        //             printf("@vsim@ 未知消息类型: 0x%x\n", msg.usMsgCmd);
        //             break;
        //     }
        // } else if (ret < 0 && errno != EAGAIN && errno != EINTR) {
        //     printf("@vsim@ 消息接收错误: %s\n", strerror(errno));
        //     break;
        // }
        // // ret == 0 或 EAGAIN 表示没有消息，休眠一下避免CPU占用过高
        // usleep(100000);  // 100ms

		ret = uicc_agt_msg_recv(g_uicc_agt_msq, &msg, sizeof(MSG_BUF) - sizeof(long), 0, 1); // 设置超时1秒
		if(ret <= 0) {
			// 超时或错误，检查是否需要退出
			if (!g_msg_thread_running) {
				printf("@vsim@ 接收到退出信号，跳出主循环");
				break;
			}
			usleep(100000);  // 100ms
			continue;
		}
      
		printf("@vsim@ 收到消息: cmd=0x%x, src=%d, dst=%d\n",
				msg.usMsgCmd, msg.src_id, msg.dst_id);

		switch (msg.usMsgCmd) {
			case MSG_CMD_UICC_AGT_SVR_VSIM_AUTH_RSP:
				handle_auth_response(&msg);
				break;
			default:
				printf("@vsim@ 未知消息类型: 0x%x\n", msg.usMsgCmd);
				break;
		}
        usleep(100000);
    }

    printf("@vsim@ 消息处理线程退出\n");
    return NULL;
}

/**
 * 启动消息处理线程
 */
static int start_msg_thread(void)
{
    if (g_msg_thread_running) {
        return 0;  // 已经启动
    }

    g_msg_thread_running = 1;
    int ret = pthread_create(&g_msg_thread, NULL, msg_thread_func, NULL);
    if (ret != 0) {
        printf("@vsim@ 创建消息处理线程失败: %s\n", strerror(ret));
        g_msg_thread_running = 0;
        return -1;
    }

    printf("@vsim@ 消息处理线程创建成功\n");
    return 0;
}

/**
 * 停止消息处理线程
 */
static void stop_msg_thread(void)
{
    if (g_msg_thread_running) {
        g_msg_thread_running = 0;
        pthread_join(g_msg_thread, NULL);
        printf("@vsim@ 消息处理线程已停止\n");
    }
}
#endif
int usim_reset_proc(T_UICC_RESET_REQ_MSG *reset_req)
{
	VSIM_MSG_BUF stMsg={0};
	int ret =0;
	T_UICC_RESET_RSP_MSG *reset_rsp = (T_UICC_RESET_RSP_MSG *)stMsg.aucDataBuf;
	reset_rsp->card_selector = reset_req->card_selector;
	reset_rsp->func_resp = zVcard_ResetCard(reset_req->card_selector);
	if(reset_rsp->func_resp){
		printf("@vsim@ usim_reset_proc err rsp=%d\n",reset_rsp->func_resp);
		system("cp -fp /etc_ro/vSim.bin /mnt/userdata/");
	}
	stMsg.usMsgCmd=MSG_CMD_USIM_RESET;
	stMsg.usDataLen=sizeof(T_UICC_RESET_RSP_MSG);
	ret = write(g_fd, &stMsg, sizeof(stMsg));
	printf("@vsim@ usim_reset_proc send %d\n",ret);
	return 1;
}

int usim_getatr_proc(T_UICC_ATR_REQ_MSG *atr_req)
{
	VSIM_MSG_BUF stMsg={0};
	int ret =0;
	T_UICC_ATR_RSP_MSG *atr_rsp = (T_UICC_ATR_RSP_MSG *)stMsg.aucDataBuf;
	atr_rsp->card_selector = atr_req->card_selector;
	atr_rsp->func_resp = zVcard_GetAtr(atr_req->card_selector,atr_rsp->atr_data);
	{
		int i = 0;
		char atr_str[32*3+1] = {0};
		for(i=0; i<32; i++)
		{
			sprintf(atr_str+i*3, "%02x ", atr_rsp->atr_data[i]);
		}
		printf("@vsim@ atr_data: %s\n", atr_str);
	}
	stMsg.usMsgCmd=MSG_CMD_USIM_GETATR;
	stMsg.usDataLen=sizeof(T_UICC_ATR_RSP_MSG);
	ret = write(g_fd, &stMsg, sizeof(stMsg));
	printf("@vsim@ usim_getatr_proc send %d\n",ret);
	return 1;
}

int usim_apdu_proc(T_UICC_TRANSPORT_APDU_REQ_MSG *apdu_req)
{
	VSIM_MSG_BUF stMsg={0};
	int ret =0;
	T_ZDrvUicc_ApduHeader tCApdu;
	T_UICC_TRANSPORT_APDU_RSP_MSG *apdu_rsp = (T_UICC_TRANSPORT_APDU_RSP_MSG *)stMsg.aucDataBuf;
	{
		int i = 0;
		char apdu_str[260*3+1] = {0};
		printf("@vsim@ apdu_req->c_apdu: cla=%x,ins=%x,p1=%x,p2=%x,lc=%d\n", apdu_req->c_apdu.cla,apdu_req->c_apdu.ins,apdu_req->c_apdu.p1,apdu_req->c_apdu.p2,apdu_req->c_apdu.lc);
		for(i=0; i<apdu_req->c_apdu.lc; i++)
		{
			sprintf(apdu_str+i*3, "%02x ", apdu_req->data_req[i]);
		}
		printf("@vsim@ apdu_req->data_req: %s\n", apdu_str);
	}
	apdu_rsp->card_selector = apdu_req->card_selector;
	memcpy(&tCApdu, &apdu_req->c_apdu,sizeof(T_ZDrvUicc_ApduHeader));

#ifdef UICC_AGT_ITO_CLOUD_VSIM
	// 检查是否为鉴权APDU，如果是则发送到qrzl_app进行云端鉴权
	if (is_auth_apdu(apdu_req)) {
		printf("@vsim@ 检测到鉴权APDU，发送到qrzl_app进行云端鉴权\n");

		// 确保消息队列已初始化
		init_uicc_agt_app_connection();

		if (g_uicc_agt_msq >= 0) {
			// 构造APDU十六进制字符串
			char apdu_hex_str[1024] = {0};
			int hex_len = 0;

			// 构造完整的APDU字符串：CLA + INS + P1 + P2 + LC + DATA
			hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->c_apdu.cla);
			hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->c_apdu.ins);
			hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->c_apdu.p1);
			hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->c_apdu.p2);
			hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->c_apdu.lc);

			// 添加数据部分
			int i;
			for (i = 0; i < apdu_req->c_apdu.lc && i < 255; i++) {
				hex_len += sprintf(apdu_hex_str + hex_len, "%02X", apdu_req->data_req[i]);
			}

			printf("@vsim@ 构造的APDU十六进制字符串: %s (长度: %d)\n", apdu_hex_str, hex_len);

			// 构造发送给qrzl_app的消息
			MSG_BUF auth_msg = {0};
			auth_msg.dst_id = MODULE_ID_QRZL_APP;
			auth_msg.src_id = MODULE_ID_UICC_AGT_SVR;
			auth_msg.usMsgCmd = MSG_CMD_QRZL_APP_ITO_VSIM_AUTH;
			auth_msg.ulMagic = MSG_MAGIC_WORD;
			auth_msg.lMsgType = MSG_TYPE_DEFAULT;

			// 将APDU十六进制字符串复制到消息中
			auth_msg.usDataLen = strlen(apdu_hex_str);
			strncpy((char*)auth_msg.aucDataBuf, apdu_hex_str, sizeof(auth_msg.aucDataBuf) - 1);
			auth_msg.aucDataBuf[sizeof(auth_msg.aucDataBuf) - 1] = '\0';

			// 发送消息到qrzl_app
			int send_ret = uicc_agt_msg_send(g_uicc_agt_msq, &auth_msg, sizeof(MSG_BUF) - sizeof(long));
			if (send_ret > 0) {
				printf("@vsim@ 鉴权APDU已发送到qrzl_app，等待响应\n");

				// 等待鉴权响应
				pthread_mutex_lock(&g_auth_mutex);
				g_waiting_auth_response = 1;

				struct timespec timeout;
				clock_gettime(CLOCK_REALTIME, &timeout);
				timeout.tv_sec += 60;  // 10秒 测试有概率超时(网络没问题的情况) 改成60秒超时

				int wait_ret = pthread_cond_timedwait(&g_auth_cond, &g_auth_mutex, &timeout);
				if (wait_ret == 0 && !g_waiting_auth_response) {
					// 收到响应，解析APDU响应数据
					// 切到卡1通道，这个是at 通道
					//system("echo 1 > /proc/vsim_switch");
					printf("@vsim@ 收到云端鉴权响应: %s (长度: %d)\n", g_auth_response_data, g_auth_response_len);

					// 将十六进制字符串转换为二进制数据并填充到apdu_rsp
					if (g_auth_response_len > 0) {
						uint8_t bin_data[256] = {0};  // 临时缓冲区
						int bin_len = hex_str_to_bin(g_auth_response_data, bin_data, sizeof(bin_data));

						if (bin_len >= 0) {
							// 转换成功，bin_data包含纯响应数据，不包含SW1/SW2
							// SW1/SW2需要根据云端鉴权结果自己填充

							// 设置数据长度
							apdu_rsp->r_apdu_ptr.luicc = bin_len;

							// 复制响应数据到缓冲区
							if (bin_len > 0 && bin_len <= 256) {
								memcpy(apdu_req->data_req, bin_data, bin_len);
							}

							// 根据云端鉴权结果设置SW1/SW2
							// 假设云端返回数据表示鉴权成功
							apdu_rsp->r_apdu_ptr.sw1 = 0x90;  // 成功
							apdu_rsp->r_apdu_ptr.sw2 = 0x00;  // 无错误
							apdu_rsp->func_resp = DRV_UICC_TRANSFER_SUCCEEDED;

							printf("@vsim@ 云端鉴权响应解析成功: SW1=0x%02X, SW2=0x%02X, 数据长度=%d\n",
								   apdu_rsp->r_apdu_ptr.sw1, apdu_rsp->r_apdu_ptr.sw2, apdu_rsp->r_apdu_ptr.luicc);

							// 打印响应数据（用于调试）
							if (apdu_rsp->r_apdu_ptr.luicc > 0) {
								printf("@vsim@ 响应数据: ");
								int i;
								for (i = 0; i < apdu_rsp->r_apdu_ptr.luicc; i++) {
									printf("%02X ", bin_data[i]);
								}
								printf("\n");
							}
						} else {
							// 十六进制转换失败
							printf("@vsim@ 云端鉴权响应格式错误，十六进制转换失败\n");
							apdu_rsp->r_apdu_ptr.sw1 = 0x6F;
							apdu_rsp->r_apdu_ptr.sw2 = 0x00;  // 未知错误
							apdu_rsp->r_apdu_ptr.luicc = 0;
							apdu_rsp->func_resp = DRV_UICC_TRANSFER_SUCCEEDED;
						}
					} else {
						// 鉴权失败或无响应数据
						printf("@vsim@ 云端鉴权失败，无响应数据\n");
						apdu_rsp->r_apdu_ptr.sw1 = 0x98;
						apdu_rsp->r_apdu_ptr.sw2 = 0x62;  // 鉴权失败
						apdu_rsp->r_apdu_ptr.luicc = 0;
						apdu_rsp->func_resp = DRV_UICC_TRANSFER_SUCCEEDED;
					}
				} else {
					// 超时或其他错误
					printf("@vsim@ 等待云端鉴权响应超时，使用本地处理\n");
					apdu_rsp->func_resp = zVcard_TransportApdu(apdu_req->card_selector, apdu_req->command_case, apdu_req->extended_length, tCApdu, &apdu_rsp->r_apdu_ptr,apdu_req->data_req);
				}

				pthread_mutex_unlock(&g_auth_mutex);
			} else {
				printf("@vsim@ 发送鉴权APDU到qrzl_app失败，使用本地处理\n");
				apdu_rsp->func_resp = zVcard_TransportApdu(apdu_req->card_selector, apdu_req->command_case, apdu_req->extended_length, tCApdu, &apdu_rsp->r_apdu_ptr,apdu_req->data_req);
			}
		} else {
			printf("@vsim@ 消息队列未初始化，使用本地处理\n");
			apdu_rsp->func_resp = zVcard_TransportApdu(apdu_req->card_selector, apdu_req->command_case, apdu_req->extended_length, tCApdu, &apdu_rsp->r_apdu_ptr,apdu_req->data_req);
		}
	} else {
		// 非鉴权APDU，使用本地处理
		apdu_rsp->func_resp = zVcard_TransportApdu(apdu_req->card_selector, apdu_req->command_case, apdu_req->extended_length, tCApdu, &apdu_rsp->r_apdu_ptr,apdu_req->data_req);
	}
#else
	// 没有启用IOT云VSIM功能，使用本地处理
	apdu_rsp->func_resp = zVcard_TransportApdu(apdu_req->card_selector, apdu_req->command_case, apdu_req->extended_length, tCApdu, &apdu_rsp->r_apdu_ptr,apdu_req->data_req);
#endif

	// 复制响应数据
	if (apdu_rsp->r_apdu_ptr.luicc > 0) {
		memcpy(apdu_rsp->data_rsp, apdu_req->data_req, apdu_rsp->r_apdu_ptr.luicc);
	}
	{
		int i = 0;
		char apdu_str[260*3+1] = {0};
		printf("@vsim@ apdu_rsp->r_apdu_ptr: sw1=%x,sw2=%x,luicc=%d\n", apdu_rsp->r_apdu_ptr.sw1,apdu_rsp->r_apdu_ptr.sw2,apdu_rsp->r_apdu_ptr.luicc);
		for(i=0; i<apdu_rsp->r_apdu_ptr.luicc; i++)
		{
			sprintf(apdu_str+i*3, "%02x ", apdu_rsp->data_rsp[i]);
		}
		printf("@vsim@ apdu_rsp->data_rsp: %s\n", apdu_str);
	}
	stMsg.usMsgCmd=MSG_CMD_USIM_APDU;
	stMsg.usDataLen=sizeof(T_UICC_TRANSPORT_APDU_RSP_MSG);
	ret = write(g_fd, &stMsg, sizeof(stMsg));
	printf("@vsim@ usim_apdu_proc send %d\n",ret);
	return 1;
}

int usim_close_proc(T_UICC_CLOSE_REQ_MSG *close_req)
{
	VSIM_MSG_BUF stMsg={0};
	int ret =0;
	T_UICC_CLOSE_RSP_MSG *close_rsp = (T_UICC_CLOSE_RSP_MSG *)stMsg.aucDataBuf;
	close_rsp->card_selector = close_req->card_selector;
	close_rsp->func_resp = zVcard_CloseCard(close_req->card_selector);
	stMsg.usMsgCmd=MSG_CMD_USIM_CLOSE;
	stMsg.usDataLen=sizeof(T_UICC_CLOSE_RSP_MSG);
	ret = write(g_fd, &stMsg, sizeof(stMsg));
	printf("@vsim@ usim_close_proc send %d\n",ret);
	return 1;
}

int rcv_msg_proc(VSIM_MSG_BUF *msg_buf)
{
	switch (msg_buf->usMsgCmd)
    {
    case MSG_CMD_USIM_RESET:
		usim_reset_proc((T_UICC_RESET_REQ_MSG *)(msg_buf->aucDataBuf));
		break;
	case MSG_CMD_USIM_GETATR:
		usim_getatr_proc((T_UICC_ATR_REQ_MSG *)(msg_buf->aucDataBuf));
		break;
	case MSG_CMD_USIM_APDU:
		usim_apdu_proc((T_UICC_TRANSPORT_APDU_REQ_MSG *)(msg_buf->aucDataBuf));
		break;
	case MSG_CMD_USIM_CLOSE:
		usim_close_proc((T_UICC_CLOSE_REQ_MSG *)(msg_buf->aucDataBuf));
		break;
	default:
		printf("@vsim@ rcv_msg_proc msgid 0x%x\n",msg_buf->usMsgCmd);
		break;
	}
	return 0;
}

int check_vsim_bin(int fd)
{
	int fd_r = open("/etc_ro/vSim.bin",O_RDONLY);
	if(fd_r >= 0){
		unsigned char buf_r[VSIM_BIN_AUTH_LEN] = {0};
		unsigned char buf[VSIM_BIN_AUTH_LEN] = {0};
		int read_len = 0;
		int read_len_r = 0;
again1:
		read_len = read(fd, buf, sizeof(buf));
		if (read_len < 0) {
			if (errno == EINTR){
				printf("@vsim@ Read bin, interrupted!\n");
				goto again1;
			}else{
				printf("@vsim@ Read bin fail, error:[%d]%s\n", errno, strerror(errno));
				goto err_out;
			}
		}else if(read_len != sizeof(buf)){
			printf("@vsim@ Read bin len=%d err\n", read_len);
			goto err_out;
		}
again2:
		read_len_r = read(fd_r, buf_r, sizeof(buf_r));
		if (read_len_r < 0) {
			if (errno == EINTR){
				printf("@vsim@ Read bin_ro, interrupted!\n");
				goto again2;
			}else{
				printf("@vsim@ Read bin_ro fail, error:[%d]%s\n", errno, strerror(errno));
				goto err_out;
			}
		}else if(read_len_r != sizeof(buf_r)){
			printf("@vsim@ Read bin_ro len=%d err\n", read_len_r);
			goto err_out;
		}
		close(fd_r);
		return memcmp(buf_r, buf, sizeof(buf_r));
	} else {
		printf("@vsim@ open vsim_ro fail, error:[%d]%s", errno, strerror(errno));
		return -1;
	}

err_out:
	close(fd_r);
	return -1;
}

int main(int argc, char* argv[])
{
	VSIM_MSG_BUF stMsg;
	int iRet = 0;

#ifdef UICC_AGT_ITO_CLOUD_VSIM
	// 注册信号处理函数
	signal(SIGINT, signal_handler);
	signal(SIGTERM, signal_handler);
#endif

	g_fd = open("/dev/rpm0",O_RDWR);
	printf("@vsim@ verion 1.0 open rpm0 fd: %d er_no: %d\n", g_fd, errno);
	//assert(g_fd >= 0);
	if(g_fd < 0)
	{
		exit(0);
	}
	{
		int fd = open("/mnt/userdata/vSim.bin",O_RDONLY);
		printf("@vsim@ open userdata/vSim.bin fd: %d er_no: %d\n", fd, errno);
		if(fd >= 0)
		{
			if(check_vsim_bin(fd)){
				printf("@vsim@ check_vsim_bin fail copy form ro\n");
				system("cp -fp /etc_ro/vSim.bin /mnt/userdata/");
			}
			close(fd);
		}
		else
			system("cp -fp /etc_ro/vSim.bin /mnt/userdata/");
	}
	while(1)
    {
        memset(&stMsg, 0x00, sizeof(VSIM_MSG_BUF));

		iRet = read(g_fd, &stMsg, sizeof(stMsg));
        if (iRet >= 0)
        {
            rcv_msg_proc(&stMsg);            
        }
        else
        {
            if(errno != EINTR)
                printf("errno = %d, errmsg = %s\n", errno,strerror(errno));
        }
    }
	
}

