/*********************************************************************************
** Copyright @ Fibocom Technologies Co., Ltd. 2023-2030. All rights reserved.
** File name: fibo_error.h 
** Author: GaoZeng 
** Version: V1.0.0
** Date: 2023.02.14
** Description: This file provides the definitions for error code. 
** Others: 
** History: 
***********************************************************************************/
#ifndef __FIBO_ERROR_H__
#define __FIBO_ERROR_H__

/****************************************************************************
 * Error Code Definition
 ***************************************************************************/

typedef enum {
    E_FIBO_NO_ERR                               = 0,
    E_FIBO_ERR_GENERIC                          = -1, /*Generic error. */
    E_FIBO_ERR_NOT_SUPPORT                      = -2,
    E_FIBO_ERR_IO_ERROR                         = -3,
    E_FIBO_ERR_NOT_IMPLEMENTED                  = -4,
    E_FIBO_ERR_IP_INVALID                       = -5,
    E_FIBO_ERR_OS_INVALID                       = -6,
    E_FIBO_ERR_LOG_INIT_FIAL                    = -7,
    E_FIBO_ERR_OS_TIMEOUT                       = -8,
    E_FIBO_ERR_OS_ERROR                         = -9,
    E_FIBO_ERR_PARAM_INVALID                    = -10, /*Parameter is invalid.*/
    E_FIBO_ERR_CALLBACK_EXIST                   = -11,   
    E_FIBO_ERR_SERVER_FD_PARAM_INVALID          = -12, 
    E_FIBO_ERR_DATA_CREATE_FIAL                 = -13, 
    E_FIBO_ERR_DATA_INVALID_TECH_PREF           = -14, /*Invalid technical preference. */
    E_FIBO_ERR_DATA_INVALID_APN_TYPE            = -15,
    E_FIBO_ERR_DATA_INVALID_AUTH_TYPE           = -16,
    E_FIBO_ERR_DATA_INVALID_IP_FAMILY_PREF      = -17,
    E_FIBO_ERR_DATA_INVALID_CDMA_PROFILE_PREF   = -18,
    E_FIBO_ERR_DATA_INVALID_UMTS_PROFILE_PREF   = -19,
    E_FIBO_ERR_DATA_INVALID_USERNAM_PREF        = -20,
    E_FIBO_ERR_DATA_INVALID_PASSWORD_PREF       = -21,
    E_FIBO_ERR_DATA_CALL_FAILED                 = -22,
    E_FIBO_ERR_DATA_GET_IP_FAIL                 = -23,
    E_FIBO_ERR_DATA_GET_NAME_FAIL               = -24,
    E_FIBO_ERR_DATA_GET_NO_IP                   = -25,
    E_FIBO_ERR_DATA_CALL_EXIST                  = -26, 
    E_FIBO_ERR_RADIO_RESET                      = -27, /*SSR happen, device not in proper state. */
    E_FIBO_ERR_INSUFFICIENT_RESOURCES           = -28, /*Insufficient resources. */
    E_FIBO_ERR_DATA_APN_BE_USED                 = -29,
    E_FIBO_ERR_DATA_EXIST_SAME_PROFILE          = -30,
    E_FIBO_ERR_DATA_PROFILE_USED_CALLED         = -31,
    
}e_error_code_t;

#define FIBO_NO_ERR                                                        E_FIBO_NO_ERR
#define FIBO_ERR_GENERIC                                                   E_FIBO_ERR_GENERIC        
#define FIBO_ERR_NOT_SUPPORT                                               E_FIBO_ERR_NOT_SUPPORT
#define FIBO_ERR_IO_ERROR                                                  E_FIBO_ERR_IO_ERROR
#define FIBO_ERR_NOT_IMPLEMENTED                                           E_FIBO_ERR_NOT_IMPLEMENTED
#define FIBO_ERR_IP_INVALID                                                E_FIBO_ERR_IP_INVALID
#define FIBO_ERR_OS_INVALID                                                E_FIBO_ERR_OS_INVALID
#define FIBO_ERR_LOG_INIT_FIAL                                             E_FIBO_ERR_LOG_INIT_FIAL
#define FIBO_ERR_OS_TIMEOUT                                                E_FIBO_ERR_OS_TIMEOUT
#define FIBO_ERR_OS_ERROR                                                  E_FIBO_ERR_OS_ERROR
#define FIBO_ERR_PARAM_INVALID                                             E_FIBO_ERR_PARAM_INVALID
#define FIBO_ERR_CALLBACK_EXIST                                            E_FIBO_ERR_CALLBACK_EXIST
#define FIBO_ERR_SERVER_FD_PARAM_INVALID                                   E_FIBO_ERR_SERVER_FD_PARAM_INVALID
#define FIBO_ERR_DATA_CREATE_FIAL                                          E_FIBO_ERR_DATA_CREATE_FIAL
#define FIBO_ERR_DATA_INVALID_TECH_PREF                                    E_FIBO_ERR_DATA_INVALID_TECH_PREF
#define FIBO_ERR_DATA_INVALID_APN_TYPE                                     E_FIBO_ERR_DATA_INVALID_APN_TYPE
#define FIBO_ERR_DATA_INVALID_AUTH_TYPE                                    E_FIBO_ERR_DATA_INVALID_AUTH_TYPE
#define FIBO_ERR_DATA_INVALID_IP_FAMILY_PREF                               E_FIBO_ERR_DATA_INVALID_IP_FAMILY_PREF
#define FIBO_ERR_DATA_INVALID_CDMA_PROFILE_PREF                            E_FIBO_ERR_DATA_INVALID_CDMA_PROFILE_PREF
#define FIBO_ERR_DATA_INVALID_UMTS_PROFILE_PREF                            E_FIBO_ERR_DATA_INVALID_UMTS_PROFILE_PREF
#define FIBO_ERR_DATA_INVALID_USERNAM_PREF                                 E_FIBO_ERR_DATA_INVALID_USERNAM_PREF
#define FIBO_ERR_DATA_INVALID_PASSWORD_PREF                                E_FIBO_ERR_DATA_INVALID_PASSWORD_PREF
#define FIBO_ERR_DATA_CALL_FAILED                                          E_FIBO_ERR_DATA_CALL_FAILED
#define FIBO_ERR_DATA_GET_IP_FAIL                                          E_FIBO_ERR_DATA_GET_IP_FAIL
#define FIBO_ERR_DATA_GET_NAME_FAIL                                        E_FIBO_ERR_DATA_GET_NAME_FAIL
#define FIBO_ERR_DATA_GET_NO_IP                                            E_FIBO_ERR_DATA_GET_NO_IP
#define FIBO_ERR_DATA_CALL_EXIST                                           E_FIBO_ERR_DATA_CALL_EXIST
#define FIBO_ERR_RADIO_RESET                                               E_FIBO_ERR_RADIO_RESET
#define FIBO_ERR_INSUFFICIENT_RESOURCES                                    E_FIBO_ERR_INSUFFICIENT_RESOURCES
#define FIBO_ERR_DATA_APN_BE_USED                                          E_FIBO_ERR_DATA_APN_BE_USED
#define FIBO_ERR_DATA_EXIST_SAME_PROFILE                                   E_FIBO_ERR_DATA_EXIST_SAME_PROFILE
#define FIBO_ERR_DATA_PROFILE_USED_CALLED                                  E_FIBO_ERR_DATA_PROFILE_USED_CALLED


#endif //#ifndef __FIBO_ERROR_H__