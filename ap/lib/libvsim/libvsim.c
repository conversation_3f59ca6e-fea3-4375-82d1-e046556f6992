#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <pthread.h>
#include <asm/unistd.h>
#include "softap_api.h"
#include "zte_vsim_api.h"

#define VSIM_MAX_MSG_LEN	1024
#define vsim_debug_printf printf 
uint8_t bWithVsimApp = 1; //  0 ����Ӧ��    1 ��Ӧ��

enum{
	MSG_CMD_VSIM_GET_SWITCH = 1,//AT����һ����
	MSG_CMD_VSIM_SET_SWITCH,
	MSG_CMD_VSIM_GET_STANDBY,//�ͻ����ô�����,���ܲ��ǵ�ǰ��ǰ������,����TAU
	MSG_CMD_VSIM_SET_STANDBY,
	MSG_CMD_VSIM_GET_TAUING,//������TAU,���ڼ�Ȩ�����л�ʵ�忨�ж�
	MSG_CMD_VSIM_SET_TAUING,
	MSG_CMD_VSIM_GET_AUTHING,//��������Ȩ,��ʱ��ֹTAU
	MSG_CMD_VSIM_SET_AUTHING,
	MSG_CMD_VSIM_GET_CARD_STAT,//��ȡ��ǰ�ײ�ʵ�ʴ�����
	MSG_CMD_VSIM_APDU,
	MSG_CMD_VSIM_GET_FLOW_STATISTIC,//��ȡ��ǰ����ͳ��
	MSG_CMD_VSIM_RESET_FLOW_STATISTIC,//���õ�ǰ����ͳ��
	MSG_CMD_VSIM_GET_SCANING,//��scan,��ʱ��ֹTAU
	MSG_CMD_VSIM_SET_SCANING,
	MSG_CMD_VSIM_MAX,
};

typedef struct {
    unsigned short usMsgCmd;               /* ��Ϣ���� */  
    unsigned short usDataLen;                /* ��Ϣ���ȣ�����ͷ�� */  
    unsigned char aucDataBuf[VSIM_MAX_MSG_LEN];  /* ��Ϣ���� */  
} VSIM_MSG_BUF;

pthread_mutex_t vsim_at_mutex = PTHREAD_MUTEX_INITIALIZER;
pthread_t read_thread_tid;
pthread_t msg_thread_tid;
int g_read_thread_state = -1;
int g_msg_thread_state = -1;
VSIM_COMMAND g_vsim_apdu_process_cb;
SoftSim_COMMAND g_softsim_apdu_process_cb;
zte_mdm_event_ind_callback g_vsim_event_ind_cb;
unsigned char g_vsim_slot[2];
int g_sim_status[2] = {-1,-1};

int32_t zte_mdm_event_ind_cb(E_MODEM_EVENT_ID event_id, void *ind_data, uint32_t  ind_data_len)
{
	printf("##vsim## [%s] event_id=%d len=%d \n", __FUNCTION__, event_id, ind_data_len);
	return 0;
}
int zte_vsim_ind_cb(uint8_t *apdu_req, uint16_t apdu_req_len,uint8_t *apdu_rsp, uint16_t *apdu_rsp_len, uint8_t slot)
{
	char addr[64] = {0};
	char cmd[128] = {0};
	
	sc_cfg_get("vsim_debug_ip",addr,sizeof(addr));
	if(strlen(addr)){
		snprintf(cmd, sizeof(cmd), "ping %s -w 5", addr);
		system(cmd);
	}else
		system("ping ************** -w 5");
	return 0;
}


static int vsim_send_at(char *req_at, char *info_fmt, void **pval)
{
	int ret;
	pthread_mutex_lock(&vsim_at_mutex);
	ret = get_modem_info(req_at,info_fmt,pval);
	pthread_mutex_unlock(&vsim_at_mutex);
	return ret;
}

static int vsim_card_switch_req(unsigned char sim)
{
	int ret = 0;
	if(sim)
		ret = vsim_send_at("AT+ZCARDSWITCHREQ=0,1\r\n", NULL, NULL);
	else
		ret = vsim_send_at("AT+ZCARDSWITCHREQ=1,0\r\n", NULL, NULL);
	if(ret != 0){
		printf("##vsim## [%s] sim=%d ret=%d err\n", __FUNCTION__, sim, ret);
	}
	vsim_debug_printf("##vsim## [%s] sim=%d ret=%d\n", __FUNCTION__, sim, ret);
	return ret;
}

static int vsim_set(unsigned short msg, unsigned char param)
{
	VSIM_MSG_BUF msg_in = {0};
	VSIM_MSG_BUF msg_out = {0};
	int ret = 0;
	
	msg_in.usMsgCmd = msg;
	msg_in.aucDataBuf[0] = param;
	vsim_debug_printf("##vsim## [%s] msg=%d param=%d start\n", __FUNCTION__, msg, param);
	if(msg == MSG_CMD_VSIM_SET_SWITCH)
		pthread_mutex_lock(&vsim_at_mutex);
	ret = syscall(__NR_vsim_proc,(unsigned char *)&msg_in, (unsigned char *)&msg_out);
	if(msg == MSG_CMD_VSIM_SET_SWITCH)
		pthread_mutex_unlock(&vsim_at_mutex);
	if(ret == 0){
		printf("##vsim## [%s] msg=%d param=%d err\n", __FUNCTION__, msg, param);
	}
	return ret;
}

static unsigned char vsim_get(unsigned short msg)
{
	VSIM_MSG_BUF msg_in = {0};
	VSIM_MSG_BUF msg_out = {0};
	int ret = 0;
	
	msg_in.usMsgCmd = msg;
	vsim_debug_printf("##vsim## [%s] msg=%d start\n", __FUNCTION__, msg);
	ret = syscall(__NR_vsim_proc,(unsigned char *)&msg_in, (unsigned char *)&msg_out);
	if(ret == 0){
		printf("##vsim## [%s] msg=%d err\n", __FUNCTION__, msg);
	}
	return msg_out.aucDataBuf[0];
}

static int vsim_get_cardstat_fromps(void)
{
	VSIM_MSG_BUF msg_in = {0};
	VSIM_MSG_BUF msg_out = {0};
	int ret = 0;
	
	msg_in.usMsgCmd = MSG_CMD_VSIM_GET_CARD_STAT;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	ret = syscall(__NR_vsim_proc,(unsigned char *)&msg_in, (unsigned char *)&msg_out);
	if(ret == 0 || msg_out.aucDataBuf[0] == msg_out.aucDataBuf[1] || msg_out.aucDataBuf[2]){
		printf("##vsim## [%s] err ret=%d sim1=%d sim2=%d busy=%d\n", __FUNCTION__, ret, msg_out.aucDataBuf[0], msg_out.aucDataBuf[1], msg_out.aucDataBuf[2]);
		return -1;
	}
	if(msg_out.aucDataBuf[0])
		return 0;
	if(msg_out.aucDataBuf[1])
		return 1;
	return -1;
}

static void read_thread_entry(void *arg)
{
	prctl(PR_SET_NAME, "vsim_read", 0, 0, 0);
	while(g_vsim_event_ind_cb)
	{
		VSIM_MSG_BUF msg_in = {0};
		VSIM_MSG_BUF msg_out = {0};
		int ret = 0;
		int is_auth = 0;
		int is_slot1 = 0;
		vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
		ret = syscall(__NR_vsim_read,(unsigned char *)&msg_out);
		if(ret == 0){
			printf("##vsim## read fail\n");
		}
		vsim_debug_printf("##vsim## read msg=%d\n", msg_out.usMsgCmd);
		if(msg_out.usMsgCmd != MSG_CMD_VSIM_APDU || ret == 0){
			printf("##vsim## read msg=%d ret=%d err\n", msg_out.usMsgCmd, ret);
		}
		is_auth = msg_out.aucDataBuf[1];
		is_slot1 = msg_out.aucDataBuf[0];
		//��ʵ�忨
		if(is_auth && g_vsim_slot[msg_out.aucDataBuf[0]] && is_slot1){
			printf("##vsim## vsim_apdu_auth standby=%d\n", vsim_get(MSG_CMD_VSIM_GET_STANDBY));
			vsim_set(MSG_CMD_VSIM_SET_AUTHING,1);
			usleep(20000);//������ʩ,�ӳ�20�������·��п�����֤��Ȩ�����rlc ack�ظ�������
			vsim_card_switch_req(0);//��ʱʵ�忨���ܴ����������ӣ���������������
			vsim_set(MSG_CMD_VSIM_SET_STANDBY,0);
		}
		msg_in.usDataLen = sizeof(msg_in.aucDataBuf);
		if(g_vsim_apdu_process_cb && msg_out.aucDataBuf[0] < 2 && g_vsim_slot[msg_out.aucDataBuf[0]] == 1){
			ret = g_vsim_apdu_process_cb(&msg_out.aucDataBuf[2], msg_out.usDataLen, msg_in.aucDataBuf, 
			&msg_in.usDataLen, msg_out.aucDataBuf[0]);
		}
		if(g_softsim_apdu_process_cb && msg_out.aucDataBuf[0] < 2 && g_vsim_slot[msg_out.aucDataBuf[0]] == 2){
			ret = g_softsim_apdu_process_cb(&msg_out.aucDataBuf[2], msg_out.usDataLen, msg_in.aucDataBuf, 
			&msg_in.usDataLen, msg_out.aucDataBuf[0]);
		}
		vsim_debug_printf("##vsim## g_vsim_apdu_process_cb ret=0x%x len=%d\n", ret, msg_in.usDataLen);
		//�����⿨
		if(is_auth && g_vsim_slot[msg_out.aucDataBuf[0]] && is_slot1){
			vsim_set(MSG_CMD_VSIM_SET_STANDBY,1);//��ʱ�ƿ�Ӧ��δ���������ӣ���������������
			vsim_card_switch_req(1);
			vsim_set(MSG_CMD_VSIM_SET_AUTHING,0);
		}
		//msg_in.usDataLen = ret;���ز��ǳ�����״̬��
		msg_in.usMsgCmd = MSG_CMD_VSIM_APDU;
		ret = syscall(__NR_vsim_write,(unsigned char *)&msg_in);
		if(ret == 0){
			printf("##vsim## write fail\n");
		}
	}
	printf("##vsim## read_thread quit\n");
	g_read_thread_state = -1;
	pthread_exit(0);
}

static void msg_thread_entry(void *arg)
{
	int iMsgHandle = msgget(MODULE_ID_VSIM, IPC_CREAT|0600);
	LONG msgSize = sizeof(MSG_BUF)-sizeof(LONG);
	unsigned char SwitchStat;
	
	prctl(PR_SET_NAME, "vsim_msg", 0, 0, 0);
	while(g_vsim_event_ind_cb)
	{
		MSG_BUF stMsg = {0};
		vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
		if (msgrcv(iMsgHandle, &stMsg, msgSize, 0, 0) >= 0)
		{
			switch (stMsg.usMsgCmd)
			{
			case MSG_CMD_ZCARDSWITCHIND_IND:
				//callback todo
				SwitchStat = stMsg.aucDataBuf[1];
				if(vsim_get(MSG_CMD_VSIM_GET_AUTHING) || vsim_get(MSG_CMD_VSIM_GET_SCANING)){
					printf("##vsim## ZCARDSWITCHIND AUTHING abort\n");
					if(SwitchStat)
						vsim_set(MSG_CMD_VSIM_SET_TAUING,0);
					break;
				}
				if(SwitchStat == 0){
					vsim_set(MSG_CMD_VSIM_SET_TAUING,1);
					vsim_card_switch_req(stMsg.aucDataBuf[0]);
				}else{
					if(vsim_get(MSG_CMD_VSIM_GET_STANDBY) == stMsg.aucDataBuf[0])
						vsim_card_switch_req(stMsg.aucDataBuf[0]);
					vsim_set(MSG_CMD_VSIM_SET_TAUING,0);
				}
				break;
			case MSG_CMD_CREG_IND:
				{
					uint8_t nSimID = vsim_get(MSG_CMD_VSIM_GET_STANDBY);
					if(nSimID == vsim_get_cardstat_fromps())
						g_vsim_event_ind_cb(E_NW_ATTACH_COMMPLETE_EVENT, nSimID, 1);
				}
				break;
			case MSG_CMD_ZSCISCAN_IND:
				{
					uint8_t nSimID = vsim_get(MSG_CMD_VSIM_GET_SCANING);
					RegInfo_t reg_info = {0};
					lte_cellinfo_t cell_info = {0};
					lte_Scell_t cell[20] = {0};
					lte_Scell_t cell_order[20] = {0};
					int reg_ret = 0;
					int i = 0;
					int j = 0;
					memcpy(cell, stMsg.aucDataBuf, sizeof(lte_Scell_t)*20);
					if(nSimID != 0){
						nSimID--;
						reg_ret = zte_GetRegInfobySimid(&reg_info, nSimID);
						vsim_set(MSG_CMD_VSIM_SET_SCANING,0);
						for(i = 0; i < 20; i++){
							for(j = 0; j < 20; j++){
								if((cell_order[j].base_Scell_info.mcc == 0 && cell_order[j].base_Scell_info.mnc == 0)
								||(cell_order[j].base_Scell_info.mcc == cell[i].base_Scell_info.mcc 
									&& cell_order[j].base_Scell_info.mnc == cell[i].base_Scell_info.mnc 
									&& cell_order[j].netMode == cell[i].netMode)){
									if(cell[i].rsrp > cell_order[j].rsrp)
										memcpy(&cell_order[j], &cell[i], sizeof(lte_Scell_t));
								}
							}
							if(reg_ret == 0 && cell[i].base_Scell_info.cell_id == reg_info.lte_scell_info.cell_id){
								memcpy(&cell_info.lteScell, &cell[i], sizeof(lte_Scell_t));
								cell_info.lteScell.roamingFlag = reg_info.lte_scell_info.roamingFlag;
							}
						}
						for(i = 0; i < 20; i++){
							for(j = 0; j < 6; j++){
								if(cell_info.lteNcell[j].base_Ncell_info.mcc == 0 && cell_info.lteNcell[j].base_Ncell_info.mnc == 0 
									&& (cell_order[i].base_Scell_info.mcc != 0 || cell_order[i].base_Scell_info.mnc != 0)){
									memcpy(&cell_info.lteNcell[j].base_Ncell_info, &cell_order[i].base_Scell_info, sizeof(base_cell_info_t));
									cell_info.lteNcell[j].rsrp = cell_order[i].rsrp;
									cell_info.lteNcell[j].rsrq = cell_order[i].rsrq;
									cell_info.lteNcell[j].netMode = cell_order[i].netMode;
									cell_info.lteNcellNum++;
								}
								if(cell_info.lteNcellNum == 6){
									if(cell_order[i].rsrp > cell_info.lteNcell[j].rsrp){
										memcpy(&cell_info.lteNcell[j].base_Ncell_info, &cell_order[i].base_Scell_info, sizeof(base_cell_info_t));
										cell_info.lteNcell[j].rsrp = cell_order[i].rsrp;
										cell_info.lteNcell[j].rsrq = cell_order[i].rsrq;
										cell_info.lteNcell[j].netMode = cell_order[i].netMode;
									}
								}
							}
						}
						g_vsim_event_ind_cb(E_GET_CELLINFO_BY_SIMID_EVENT, &cell_info, sizeof(cell_info));
					}
				}
				break;
			case MSG_CMD_VSIMDEBUG_IND:
				if(stMsg.aucDataBuf[0] == 1){
					char imei[]="862769025435956";
					fibo_set_IMEI_by_simid(imei,sizeof(imei),1);
					fibo_mdm_event_regist(zte_mdm_event_ind_cb);
					vsim_set(MSG_CMD_VSIM_SET_STANDBY,1);//��ʱ�ƿ�δ���������ӣ���������������
					vsim_card_switch_req(1);
					vsim_set(MSG_CMD_VSIM_SET_SWITCH,1);
					vsim_send_at("AT+CFUN=1\r\n", NULL, NULL);
					g_vsim_slot[1] = 1;
				}
				if(stMsg.aucDataBuf[0] == 2){
					fibo_vsim_close(zte_vsim_ind_cb,1);
				}
				if(stMsg.aucDataBuf[0] == 10){
					zte_SetGTDATASIM(0);
				}
				if(stMsg.aucDataBuf[0] == 11){
					zte_SetGTDATASIM(1);
				}
				if(stMsg.aucDataBuf[0] == 20){
					zte_SetGTDUALSIM(0);
				}
				if(stMsg.aucDataBuf[0] == 21){
					zte_SetGTDUALSIM(1);
				}
				if(stMsg.aucDataBuf[0] == 30){
					bWithVsimApp = 0;
				    fibo_SoftSim_init(zte_vsim_ind_cb,0);
				}
				if(stMsg.aucDataBuf[0] == 40){
					fibo_SoftSim_close(zte_vsim_ind_cb,0);
				}
				if(stMsg.aucDataBuf[0] == 31){
					bWithVsimApp = 0;
					fibo_SoftSim_init(zte_vsim_ind_cb,1);
				}
				if(stMsg.aucDataBuf[0] == 41){
					fibo_SoftSim_close(zte_vsim_ind_cb,1);
				}
				if(stMsg.aucDataBuf[0] == 50){
					fibo_get_CellInfo_by_simid(0);
				}
				if(stMsg.aucDataBuf[0] == 51){
					fibo_get_CellInfo_by_simid(1);
				}
				break;
			default:
				printf("##vsim## rcv_msg err msgid 0x%x\n",stMsg.usMsgCmd);
				break;
			}
		}
		vsim_debug_printf("##vsim## rcv_msg msgid 0x%x param=%d end\n",stMsg.usMsgCmd, stMsg.aucDataBuf[0]);
	}
	printf("##vsim## msg_thread quit\n");
	g_msg_thread_state = -1;
	pthread_exit(0);
}

int32_t zte_atVsimInit(VSIM_COMMAND vsim_apdu_process_fn, uint8_t nSimID)
{
	char stat[4] = {0};
	
	sc_cfg_get("vsim_stat",stat,sizeof(stat));
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(vsim_apdu_process_fn == NULL || nSimID != 1 || g_vsim_apdu_process_cb || atoi(stat)){
		printf("##vsim## init param err fn=0x%x old=0x%x sim=%d stat=%d\n", 
			vsim_apdu_process_fn, g_vsim_apdu_process_cb, nSimID, atoi(stat));
		return -1;
	}
	g_vsim_apdu_process_cb = vsim_apdu_process_fn;
	if(g_read_thread_state != 0)
		g_read_thread_state = pthread_create(&read_thread_tid, NULL, (void *)read_thread_entry, NULL);
	if(g_msg_thread_state != 0)
		g_msg_thread_state = pthread_create(&msg_thread_tid, NULL, (void *)msg_thread_entry, NULL);
	if(g_read_thread_state != 0 || g_msg_thread_state != 0){
		printf("##vsim## init fail read=%d msg=%d\n", g_read_thread_state, g_msg_thread_state);
		return -1;
	}
	g_vsim_slot[1] = 1;
	g_sim_status[1] = -1;
	sc_cfg_set("vsim_stat","1");
	vsim_set(MSG_CMD_VSIM_SET_STANDBY,1);//��ʱ�ƿ�δ���������ӣ���������������
	vsim_card_switch_req(1);
	vsim_set(MSG_CMD_VSIM_SET_SWITCH,1);
	vsim_send_at("AT+ZCARDSWITCH=1,1\r\n", NULL, NULL);
	vsim_send_at("AT+CFUN=1\r\n", NULL, NULL);
	return 0;
}

int32_t zte_atSoftSimInit(SoftSim_COMMAND softssim_apdu_process_fn,  uint8_t nSimID)
{
	char stat[4] = {0};
	
	sc_cfg_get("softsim_stat",stat,sizeof(stat));
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(softssim_apdu_process_fn == NULL || (nSimID != 1 && nSimID != 0) || g_softsim_apdu_process_cb || atoi(stat)){
		printf("##vsim## soft init param err fn=0x%x old=0x%x sim=%d\n", 
			softssim_apdu_process_fn, g_softsim_apdu_process_cb, nSimID, atoi(stat));
		return -1;
	}
	g_vsim_slot[nSimID] = 2;
	g_sim_status[nSimID] = -1;
	sc_cfg_set("softsim_stat","1");
	g_softsim_apdu_process_cb = softssim_apdu_process_fn;

#if 1
//add by svk@20250717
	if(g_read_thread_state != 0)
		g_read_thread_state = pthread_create(&read_thread_tid, NULL, (void *)read_thread_entry, NULL);
	if(g_msg_thread_state != 0)
		g_msg_thread_state = pthread_create(&msg_thread_tid, NULL, (void *)msg_thread_entry, NULL);
	if(g_read_thread_state != 0 || g_msg_thread_state != 0){
		printf("##vsim## init fail read=%d msg=%d\n", g_read_thread_state, g_msg_thread_state);
		return -1;
	}
#endif

	if(nSimID){
		vsim_set(MSG_CMD_VSIM_SET_STANDBY,1);//��ʱ�ƿ�δ���������ӣ���������������
		vsim_card_switch_req(1);
		vsim_set(MSG_CMD_VSIM_SET_SWITCH,1);
		if(bWithVsimApp == 1)
		{
			vsim_send_at("AT+ZCARDSWITCH=2,1\r\n", NULL, NULL);
		}
		else
		{
			vsim_send_at("AT+ZCARDSWITCH=3,1\r\n", NULL, NULL);
		}
		vsim_send_at("AT+CFUN=1\r\n", NULL, NULL);
	}else{
		uint8_t sim = 0;
		uint8_t ret = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_get_cardstat_fromps();
		if(ret >= 0 && nSimID != ret)
			vsim_card_switch_req(nSimID);
		if(bWithVsimApp == 1)
		{
			vsim_send_at("AT+ZCARDSWITCH=2,0\r\n", NULL, NULL);
		}
		else
		{
			vsim_send_at("AT+ZCARDSWITCH=3,0\r\n", NULL, NULL);
		}
		vsim_send_at("AT+CFUN=0\r\n", NULL, NULL);
//		vsim_send_at("AT+CFUN=5\r\n", NULL, NULL);		
		vsim_send_at("AT+CFUN=1\r\n", NULL, NULL);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		if(ret >= 0 && nSimID != ret)
			vsim_card_switch_req(ret);
	}
	return 0;
}

int32_t zte_atSoftSimClose(VSIM_COMMAND vsim_apdu_process_fn, uint8_t nSimID)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(nSimID != 1 && nSimID != 0){
		printf("##vsim## soft close param err sim=%d\n", nSimID);
		return -1;
	}
	g_vsim_slot[nSimID] = 0;
	g_sim_status[nSimID] = -1;
	if(nSimID){
		vsim_card_switch_req(1);
		zte_SetGTDATASIM(1);
		vsim_send_at("AT+CFUN=0\r\n", NULL, NULL);
		vsim_card_switch_req(0);
		zte_SetGTDUALSIM(0);
		vsim_set(MSG_CMD_VSIM_RESET_FLOW_STATISTIC,1);
	}else{
		uint8_t sim = 0;
		uint8_t ret = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_get_cardstat_fromps();
		if(ret >= 0 && nSimID != ret)
			vsim_card_switch_req(nSimID);
	//	vsim_send_at("AT+CFUN=5\r\n", NULL, NULL);
		vsim_send_at("AT+ZCARDSWITCH=0,0\r\n", NULL, NULL);
		vsim_send_at("AT+CFUN=0\r\n", NULL, NULL);
		vsim_send_at("AT+CFUN=1\r\n", NULL, NULL);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		if(ret >= 0 && nSimID != ret)
			vsim_card_switch_req(ret);
	}
	g_vsim_apdu_process_cb = NULL;
	sc_cfg_set("softsim_stat","0");
	return 0;
}

int32_t zte_atVsimClose(SoftSim_COMMAND softssim_apdu_process_fn, uint8_t nSimID)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(nSimID != 1 && nSimID != 0){
		printf("##vsim## close param err sim=%d\n", nSimID);
		return -1;
	}
	g_vsim_slot[1] = 0;
	g_sim_status[1] = -1;
	vsim_card_switch_req(1);
	zte_SetGTDATASIM(1);
	vsim_send_at("AT+CFUN=0\r\n", NULL, NULL);
	vsim_card_switch_req(0);
	zte_SetGTDUALSIM(0);
	vsim_set(MSG_CMD_VSIM_RESET_FLOW_STATISTIC,1);
	g_vsim_apdu_process_cb = NULL;
	sc_cfg_set("vsim_stat","0");
	return 0;
}


int32_t zte_SetGTDATASIM(uint8_t nSimID)
{
	int ret = 0;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(vsim_get(MSG_CMD_VSIM_GET_AUTHING) || vsim_get(MSG_CMD_VSIM_GET_SCANING)){
		printf("##vsim## [%s]->%d fail AUTHING\n", __FUNCTION__, nSimID);
		return -1;
	}
	vsim_set(MSG_CMD_VSIM_SET_STANDBY,nSimID);
	ret = vsim_get_cardstat_fromps();
	if(ret >= 0){
		if(nSimID != ret){
			//������ú͵�ǰ�ײ�����಻һ�²���,forTAUING
			vsim_card_switch_req(nSimID);
		}
	}else
		vsim_card_switch_req(nSimID);
	vsim_set(MSG_CMD_VSIM_SET_SWITCH, nSimID);
	printf("##vsim## [%s] cardstat=%d sim=%d\n", __FUNCTION__, ret, nSimID);
	return 0;
}

int32_t zte_GetGTDATASIM(uint8_t *nSimID)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	*nSimID = vsim_get(MSG_CMD_VSIM_GET_STANDBY);
	return 0;
}

int32_t zte_SetGTDUALSIM(uint8_t nSimID)
{
	int ret = 0;
	unsigned char standby = vsim_get(MSG_CMD_VSIM_GET_STANDBY);
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	ret = vsim_set(MSG_CMD_VSIM_SET_SWITCH,nSimID);
	if(ret == 0){
		printf("##vsim## [%s] err sim=%d\n", __FUNCTION__, nSimID);
		return -1;
	}
	//AT�Ѿ��кã��������ж��Ƿ�Ҫ�п�
	if(vsim_get(MSG_CMD_VSIM_GET_TAUING)){//TAU�Ĺ�����,���п�
		printf("##vsim## [%s] TAUING\n", __FUNCTION__);
		return 0;
	}
	ret = vsim_get_cardstat_fromps();
	if(ret >= 0 && standby != ret && standby == nSimID){
		//���л�AT��Ŀ���������ϲ�֮ǰ�趨�Ĵ�������ͬ,�Һ͵ײ�ʵ�ʵĴ����಻ͬ,�ֲ���TAU�Ĺ�����,�ʹ����п�
		vsim_card_switch_req(nSimID);
	}
	printf("##vsim## [%s] cardstat=%d standby=%d sim=%d\n", __FUNCTION__, ret, standby, nSimID);
	return 0;
}

int32_t zte_GetGTDUALSIM(uint8_t *nSimID)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	*nSimID = vsim_get(MSG_CMD_VSIM_GET_SWITCH);
	return 0;
}

int32_t zte_mdm_event_regist(zte_mdm_event_ind_callback ind_cb)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	g_vsim_event_ind_cb = ind_cb;
	return 0;
}

void zte_vsim_debug(void)
{
	g_vsim_event_ind_cb = zte_mdm_event_ind_cb;
	g_vsim_apdu_process_cb = zte_vsim_ind_cb;
	if(g_read_thread_state != 0)
		g_read_thread_state = pthread_create(&read_thread_tid, NULL, (void *)read_thread_entry, NULL);
	if(g_msg_thread_state != 0)
		g_msg_thread_state = pthread_create(&msg_thread_tid, NULL, (void *)msg_thread_entry, NULL);
	if(g_read_thread_state != 0 || g_msg_thread_state != 0){
		printf("##vsim## debug fail read=%d msg=%d\n", g_read_thread_state, g_msg_thread_state);
	}
}

int32_t zte_atVsimReset(void)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	return restart_request(MODULE_ID_VSIM);
}

int32_t zte_SetCfunMode(uint8_t mode)
{
	char at_cmd[64] = {0};
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	snprintf(at_cmd,sizeof(at_cmd),"AT+CFUN=%d\r\n", mode);
	return vsim_send_at(at_cmd, NULL, NULL);
}

int32_t zte_GetCfunMode(uint8_t *mode)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(mode)
		return vsim_send_at("AT+CFUN?\r\n", "%1d", (void**)&mode);
	return -1;
}

int32_t zte_SetIMEIbySimId(uint8_t *imei, uint8_t imei_len, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(imei){
		char at_cmd[64] = {0};
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		snprintf(at_cmd,sizeof(at_cmd),"AT+CGSN=%s\r\n",imei);
		ret = vsim_send_at(at_cmd, NULL, NULL);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}

int32_t zte_GetIMEIbySimId(uint8_t *imei, uint8_t imei_len, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(imei){
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at("AT+CGSN\r\n", "%s", (void**)&imei);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}


int32_t zte_GetCCIDbySimId(uint8_t *ccid, uint8_t ccid_len, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(ccid){
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at("AT+ZICCID?\r\n", "%s", (void**)&ccid);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}

int32_t zte_GetMCCMNCbySimId(char *mcc, char *mnc, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(mcc && mnc){
#if 0
		int sw1=0;
		int sw2=-1;
		char response[513] = {0};//���ȷ�Χ0-256�ֽڣ�7F205F70��ʾ4���ֽڳ���
		void *p[] = {&sw1,&sw2,response};
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		vsim_send_at("AT+CRSM=176,28589,0,0,4\r\n", "%d,%d,%s", (void**)p);
		if(sw1 == 144 && sw2 == 0){
			char imsi[64] = {0};
			char strPlmnLast[10] = {0};
			void *p1[] = {imsi};
		
			vsim_send_at("AT+CIMI\r\n", "%s", (void**)p1);
			strncpy(strPlmnLast, &response[strlen(response)-2], 2);
			memset(mcc, 0, 4);
			memset(mnc, 0, 4);
			memcpy(mcc, imsi, 3);
			if(0 == strcmp("03", strPlmnLast))
				memcpy(mnc, imsi+3, 3);
			else
				memcpy(mnc, imsi+3, 2);
		}
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		return !(sw1 == 144 && sw2 == 0);
#else
		int i1,i2,i3,i4,i5;
		char plmn[8] = {0};
		void *p[] = {&i1,&i2,&i3,&i4,&i5,plmn};
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at("AT+ZEMSCIQ=1\r\n", "%d,%d,%d,%d,%d,%s", (void**)p);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		memset(mcc, 0, 4);
		memset(mnc, 0, 4);
		memcpy(mcc, plmn, 3);
		memcpy(mnc, plmn+3, 3);
#endif
	}
	return ret;
}

int32_t zte_SwitchSimHandle(uint8_t mode)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	return 0;
}

int32_t zte_GetCSQbySimId(int32_t *rssi, int32_t *ber, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(rssi && ber){
		void *p[] = {rssi,ber};
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at("AT+CSQ\r\n", "%d,%d", (void**)p);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}

int32_t zte_GetRegInfobySimid(RegInfo_t *reg_info, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(reg_info){
		int i,act,rac,srv_domain;
		void *p[] = {&i,&act,&reg_info->lte_scell_info.cell_id,&rac,&reg_info->lte_scell_info.tac};
		void *p1[] = {&reg_info->nStatus,&srv_domain,&reg_info->lte_scell_info.roamingFlag};
		uint8_t sim = 0;
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		reg_info->nStatus = 0;
		vsim_send_at("AT^SYSINFO\r\n", "%d,%d,%d", (void**)p1);
		ret = vsim_send_at("AT+ZEMSCIQ=1\r\n", "%d,%d,%d,%d,%d", (void**)p);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		if(ret || reg_info->nStatus != E_NW_STATUS_SRV_AVAILABLE)
			return -1;
		if(act == 16){
			reg_info->curr_rat = E_NW_RADIO_IF_LTE;
		} else {
			if(act == 1)
				reg_info->curr_rat = E_NW_RADIO_IF_TDSCDMA;
			else if(act == 2)
				reg_info->curr_rat = E_NW_RADIO_IF_WCDMA;
			else if(act == 4)
				reg_info->curr_rat = E_NW_RADIO_IF_GSM;
			else
				printf("##vsim## [%s] act=%d ???\n", __FUNCTION__, act);
		}
	}
	return ret;
}

int32_t zte_GetCellInfobySimId(uint8_t nSimID)
{
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	vsim_set(MSG_CMD_VSIM_SET_SCANING, 1+nSimID);
	ipc_send_message(MODULE_ID_VSIM, MODULE_ID_AT_CTL, MSG_CMD_ZSCISCAN_REQ, 0, NULL, 0);
	return 0;
}

int32_t zte_GetNetInfobySimId(NetInfo_t *pNetInfo, uint8_t nSimID)
{
	VSIM_MSG_BUF msg_in = {0};
	VSIM_MSG_BUF msg_out = {0};
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(pNetInfo && nSimID < 2){
		msg_in.usMsgCmd = MSG_CMD_VSIM_GET_FLOW_STATISTIC;
		msg_in.aucDataBuf[0] = nSimID;
		ret = syscall(__NR_vsim_proc,(unsigned char *)&msg_in, (unsigned char *)&msg_out);
		if(ret == 0){
			printf("##vsim## [%s] vsim_proc err\n", __FUNCTION__);
		}
		if(sizeof(NetInfo_t) > msg_out.usDataLen)
			memcpy(pNetInfo, msg_out.aucDataBuf, msg_out.usDataLen);
		else
			memcpy(pNetInfo, msg_out.aucDataBuf, sizeof(NetInfo_t));
	}
	return ret;
}

int32_t zte_SetAPNbySimID(Data_call_config_info_t *data_call, uint8_t nSimID)
{
	return 0;
}

int32_t zte_StartDataCallbySimId(Data_call_config_info_t *data_call, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(data_call){
		uint8_t sim = 0;
		char at_cmd[128] = {0};
		void *p[] = {&data_call->profile_id};
		char reservecid[30] = {0};
		unsigned char cid_reserved = 0;
		unsigned char cid_reserved_vsim = 0;
		
		zte_GetGTDATASIM(&sim);
		if(sim != nSimID){
			printf("##vsim## [%s] nSimID=%d standby=%d err\n", __FUNCTION__, nSimID, sim);
			return ret;
		}
		if(nSimID){
			sc_cfg_get("cid_reserved",reservecid,sizeof(reservecid));
			if(strlen(reservecid))
				cid_reserved = atoi(reservecid);
			cid_reserved_vsim = cid_reserved|15;
			snprintf(at_cmd,sizeof(at_cmd),"AT+CIDRESERVED=%d\r\n", cid_reserved_vsim);
			vsim_send_at(at_cmd, NULL, NULL);
		}
		if(data_call->ip_family == E_DATA_IP_FAMILY_IPV4)
			snprintf(at_cmd,sizeof(at_cmd),"AT+ZPDPACT=0,\"IP\",\"%s\",,,,%d,\"%s\",\"%s\"\r\n", data_call->apn_name, data_call->auth_pref, data_call->user_name, data_call->password);
		else if(data_call->ip_family == E_DATA_IP_FAMILY_IPV6)
			snprintf(at_cmd,sizeof(at_cmd),"AT+ZPDPACT=0,\"IPV6\",\"%s\",,,,%d,\"%s\",\"%s\"\r\n", data_call->apn_name, data_call->auth_pref, data_call->user_name, data_call->password);
		else if(data_call->ip_family == E_DATA_IP_FAMILY_IPV4V6)
			snprintf(at_cmd,sizeof(at_cmd),"AT+ZPDPACT=0,\"IPV4V6\",\"%s\",,,,%d,\"%s\",\"%s\"\r\n", data_call->apn_name, data_call->auth_pref, data_call->user_name, data_call->password);
		else {
			printf("##vsim## [%s] ip_family=%d ???\n", __FUNCTION__, data_call->ip_family);
			snprintf(at_cmd,sizeof(at_cmd),"AT+ZPDPACT=0,\"IP\",\"%s\",,,,%d,\"%s\",\"%s\"\r\n", data_call->apn_name, data_call->auth_pref, data_call->user_name, data_call->password);
		}
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at(at_cmd, "%d", (void**)p);
		vsim_debug_printf("##vsim## [%s] ret=%d cid=%d\n", __FUNCTION__, ret, data_call->profile_id);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		if(nSimID){
			snprintf(at_cmd,sizeof(at_cmd),"AT+CIDRESERVED=%d\r\n", cid_reserved);
			vsim_send_at(at_cmd, NULL, NULL);
		}
	}
	return ret;
}

int32_t zte_StopDataCallbySimId(uint8_t profile_id, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(profile_id > 0 && profile_id < 9){
		uint8_t sim = 0;
		char at_cmd[64] = {0};
		
		snprintf(at_cmd,sizeof(at_cmd),"AT+ZPDPDEACT=%d\r\n", profile_id);
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at(at_cmd, NULL, NULL);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}

int32_t zte_GetDataAddrbySimID(uint8_t profile_id, Data_call_addr_info_list_t *addr_list, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(profile_id > 0 && profile_id < 9 && addr_list){
		uint8_t sim = 0;
		char at_cmd[64] = {0};
		int cid = 0;
		void *p[] = {&cid, addr_list};
		
		snprintf(at_cmd,sizeof(at_cmd),"AT+CGPADDR=%d\r\n", profile_id);
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at(at_cmd, "%d,%17s", (void**)p);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
	}
	return ret;
}

int32_t zte_GetSimStatusbySimId(E_SIM_STATUS *sim_status, uint8_t nSimID)
{
	int ret = -1;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	if(sim_status){
		uint8_t sim = 0;
		char at_paras[64] = {0};
		void *p[] = {at_paras};

		vsim_debug_printf("##vsim## [%s] simid=%d sim_stat=%d\n", __FUNCTION__, nSimID, g_sim_status[nSimID]);
		if(g_vsim_slot[nSimID] && g_sim_status[nSimID] == E_SIM_STAT_READY){
			*sim_status = E_SIM_STAT_READY;
			return 0;
		}
		zte_GetGTDUALSIM(&sim);
		if(sim != nSimID)
			zte_SetGTDUALSIM(nSimID);
		ret = vsim_send_at("AT+CPIN?\r\n", "%s", (void**)p);
		if(sim != nSimID)
			zte_SetGTDUALSIM(sim);
		if(ret == 0){
			if (strncmp("READY", at_paras, strlen("READY")) == 0)
			{
				*sim_status = E_SIM_STAT_READY;
			}
			/*�����ǰ������������ΪPIN��PIN2��������+CPIN=<pin>����У��*/
			else if(strncmp("SIM PIN",at_paras, strlen("SIM PIN")) == 0)
			{
				*sim_status = E_SIM_STAT_PIN;
			}
			/*�����ǰ������������ΪPUK��PUK2��������+CPIN=<pin>,<newpin>���н���*/
			else if(strncmp("SIM PUK",at_paras, strlen("SIM PUK")) == 0)
			{
				*sim_status = E_SIM_STAT_PUK;
			}
		} else if(ret == 10){
			*sim_status = E_SIM_STAT_NOT_INSERTED;
		} else if(ret == 14){
			*sim_status = E_SIM_STAT_BUSY;
		} else {
			*sim_status = E_SIM_STAT_UNKNOWN;
		}
		if(g_vsim_slot[nSimID]){
			g_sim_status[nSimID] = *sim_status;
		}
		return 0;
	}
	return -1;
}

int32_t zte_SendATbySimId(char *req_at, char *info_fmt, void **pval, uint8_t nSimID)
{
	int ret = -1;
	uint8_t sim = 0;
	vsim_debug_printf("##vsim## [%s] start\n", __FUNCTION__);
	zte_GetGTDUALSIM(&sim);
	if(sim != nSimID)
		zte_SetGTDUALSIM(nSimID);
	printf("##vsim## [%s] nSimID=%d sw=%d at=%s\n", __FUNCTION__, nSimID, sim, req_at);
	ret = vsim_send_at(req_at, info_fmt, pval);
	if(sim != nSimID)
		zte_SetGTDUALSIM(sim);
	return ret;
}


